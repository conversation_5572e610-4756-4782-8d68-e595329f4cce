<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ActiveLayerIndex</key>
	<integer>0</integer>
	<key>ApplicationVersion</key>
	<array>
		<string>com.omnigroup.OmniGrafflePro</string>
		<string>138.31.0.156985</string>
	</array>
	<key>AutoAdjust</key>
	<true/>
	<key>BackgroundGraphic</key>
	<dict>
		<key>Bounds</key>
		<string>{{0, 0}, {1118, 783}}</string>
		<key>Class</key>
		<string>SolidGraphic</string>
		<key>ID</key>
		<integer>2</integer>
		<key>Style</key>
		<dict>
			<key>shadow</key>
			<dict>
				<key>Draws</key>
				<string>NO</string>
			</dict>
			<key>stroke</key>
			<dict>
				<key>Draws</key>
				<string>NO</string>
			</dict>
		</dict>
	</dict>
	<key>CanvasOrigin</key>
	<string>{0, 0}</string>
	<key>ColumnAlign</key>
	<integer>1</integer>
	<key>ColumnSpacing</key>
	<real>36</real>
	<key>CreationDate</key>
	<string>2011-11-04 10:48:34 +0100</string>
	<key>Creator</key>
	<string>Marc Hoffmann</string>
	<key>DisplayScale</key>
	<string>1.000 cm = 1.000 cm</string>
	<key>GraphDocumentVersion</key>
	<integer>8</integer>
	<key>GraphicsList</key>
	<array>
		<dict>
			<key>Class</key>
			<string>LineGraphic</string>
			<key>Head</key>
			<dict>
				<key>ID</key>
				<integer>16</integer>
			</dict>
			<key>ID</key>
			<integer>18</integer>
			<key>Points</key>
			<array>
				<string>{240.754, 67.9371}</string>
				<string>{294.662, 100.74}</string>
			</array>
			<key>Style</key>
			<dict>
				<key>stroke</key>
				<dict>
					<key>HeadArrow</key>
					<string>FilledArrow</string>
					<key>LineType</key>
					<integer>1</integer>
					<key>TailArrow</key>
					<string>0</string>
				</dict>
			</dict>
			<key>Tail</key>
			<dict>
				<key>ID</key>
				<integer>10</integer>
			</dict>
		</dict>
		<dict>
			<key>Class</key>
			<string>LineGraphic</string>
			<key>Head</key>
			<dict>
				<key>ID</key>
				<integer>12</integer>
			</dict>
			<key>ID</key>
			<integer>17</integer>
			<key>Points</key>
			<array>
				<string>{199.985, 67.9124}</string>
				<string>{138.367, 100.764}</string>
			</array>
			<key>Style</key>
			<dict>
				<key>stroke</key>
				<dict>
					<key>HeadArrow</key>
					<string>FilledArrow</string>
					<key>LineType</key>
					<integer>1</integer>
					<key>TailArrow</key>
					<string>0</string>
				</dict>
			</dict>
			<key>Tail</key>
			<dict>
				<key>ID</key>
				<integer>10</integer>
			</dict>
		</dict>
		<dict>
			<key>Bounds</key>
			<string>{{483, 269}, {85.0394, 22.6772}}</string>
			<key>Class</key>
			<string>ShapedGraphic</string>
			<key>FontInfo</key>
			<dict>
				<key>Color</key>
				<dict>
					<key>w</key>
					<string>0</string>
				</dict>
				<key>Font</key>
				<string>Helvetica</string>
				<key>NSKern</key>
				<real>0.0</real>
				<key>Size</key>
				<real>12</real>
			</dict>
			<key>ID</key>
			<integer>38</integer>
			<key>Shape</key>
			<string>Rectangle</string>
			<key>Style</key>
			<dict>
				<key>fill</key>
				<dict>
					<key>Color</key>
					<dict>
						<key>b</key>
						<string>0</string>
						<key>g</key>
						<string>0.5</string>
						<key>r</key>
						<string>1</string>
					</dict>
				</dict>
				<key>shadow</key>
				<dict>
					<key>Draws</key>
					<string>NO</string>
					<key>Fuzziness</key>
					<real>2.6766984462738037</real>
				</dict>
				<key>stroke</key>
				<dict>
					<key>CornerRadius</key>
					<real>9</real>
				</dict>
			</dict>
			<key>Text</key>
			<dict>
				<key>Text</key>
				<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 \expnd0\expndtw0\kerning0
Java Agent}</string>
			</dict>
		</dict>
		<dict>
			<key>Bounds</key>
			<string>{{377, 269}, {85.0394, 22.6772}}</string>
			<key>Class</key>
			<string>ShapedGraphic</string>
			<key>FontInfo</key>
			<dict>
				<key>Color</key>
				<dict>
					<key>w</key>
					<string>0</string>
				</dict>
				<key>Font</key>
				<string>Helvetica</string>
				<key>NSKern</key>
				<real>0.0</real>
				<key>Size</key>
				<real>12</real>
			</dict>
			<key>ID</key>
			<integer>36</integer>
			<key>Shape</key>
			<string>Rectangle</string>
			<key>Style</key>
			<dict>
				<key>shadow</key>
				<dict>
					<key>Draws</key>
					<string>NO</string>
					<key>Fuzziness</key>
					<real>2.6766984462738037</real>
				</dict>
				<key>stroke</key>
				<dict>
					<key>CornerRadius</key>
					<real>9</real>
				</dict>
			</dict>
			<key>Text</key>
			<dict>
				<key>Text</key>
				<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 \expnd0\expndtw0\kerning0
Class Loader}</string>
			</dict>
		</dict>
		<dict>
			<key>Bounds</key>
			<string>{{376, 214}, {85.0394, 22.6772}}</string>
			<key>Class</key>
			<string>ShapedGraphic</string>
			<key>FontInfo</key>
			<dict>
				<key>Color</key>
				<dict>
					<key>w</key>
					<string>0</string>
				</dict>
				<key>Font</key>
				<string>Helvetica</string>
				<key>NSKern</key>
				<real>0.0</real>
				<key>Size</key>
				<real>12</real>
			</dict>
			<key>ID</key>
			<integer>28</integer>
			<key>Shape</key>
			<string>Rectangle</string>
			<key>Style</key>
			<dict>
				<key>fill</key>
				<dict>
					<key>Color</key>
					<dict>
						<key>b</key>
						<string>0</string>
						<key>g</key>
						<string>0.5</string>
						<key>r</key>
						<string>1</string>
					</dict>
				</dict>
				<key>shadow</key>
				<dict>
					<key>Draws</key>
					<string>NO</string>
					<key>Fuzziness</key>
					<real>2.6766984462738037</real>
				</dict>
				<key>stroke</key>
				<dict>
					<key>CornerRadius</key>
					<real>9</real>
				</dict>
			</dict>
			<key>Text</key>
			<dict>
				<key>Text</key>
				<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 \expnd0\expndtw0\kerning0
On-The-Fly}</string>
			</dict>
		</dict>
		<dict>
			<key>Bounds</key>
			<string>{{165, 269}, {85.0394, 22.6772}}</string>
			<key>Class</key>
			<string>ShapedGraphic</string>
			<key>FontInfo</key>
			<dict>
				<key>Color</key>
				<dict>
					<key>w</key>
					<string>0</string>
				</dict>
				<key>Font</key>
				<string>Helvetica</string>
				<key>NSKern</key>
				<real>0.0</real>
				<key>Size</key>
				<real>12</real>
			</dict>
			<key>ID</key>
			<integer>30</integer>
			<key>Shape</key>
			<string>Rectangle</string>
			<key>Style</key>
			<dict>
				<key>shadow</key>
				<dict>
					<key>Draws</key>
					<string>NO</string>
					<key>Fuzziness</key>
					<real>2.6766984462738037</real>
				</dict>
				<key>stroke</key>
				<dict>
					<key>CornerRadius</key>
					<real>9</real>
				</dict>
			</dict>
			<key>Text</key>
			<dict>
				<key>Text</key>
				<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 Replace}</string>
			</dict>
		</dict>
		<dict>
			<key>Bounds</key>
			<string>{{271, 269}, {85.0394, 22.6772}}</string>
			<key>Class</key>
			<string>ShapedGraphic</string>
			<key>FontInfo</key>
			<dict>
				<key>Color</key>
				<dict>
					<key>w</key>
					<string>0</string>
				</dict>
				<key>Font</key>
				<string>Helvetica</string>
				<key>NSKern</key>
				<real>0.0</real>
				<key>Size</key>
				<real>12</real>
			</dict>
			<key>ID</key>
			<integer>32</integer>
			<key>Shape</key>
			<string>Rectangle</string>
			<key>Style</key>
			<dict>
				<key>shadow</key>
				<dict>
					<key>Draws</key>
					<string>NO</string>
					<key>Fuzziness</key>
					<real>2.6766984462738037</real>
				</dict>
				<key>stroke</key>
				<dict>
					<key>CornerRadius</key>
					<real>9</real>
				</dict>
			</dict>
			<key>Text</key>
			<dict>
				<key>Text</key>
				<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 Inject}</string>
			</dict>
		</dict>
		<dict>
			<key>Bounds</key>
			<string>{{271, 214}, {85.0394, 22.6772}}</string>
			<key>Class</key>
			<string>ShapedGraphic</string>
			<key>FontInfo</key>
			<dict>
				<key>Color</key>
				<dict>
					<key>w</key>
					<string>0</string>
				</dict>
				<key>Font</key>
				<string>Helvetica</string>
				<key>NSKern</key>
				<real>0.0</real>
				<key>Size</key>
				<real>12</real>
			</dict>
			<key>ID</key>
			<integer>24</integer>
			<key>Shape</key>
			<string>Rectangle</string>
			<key>Style</key>
			<dict>
				<key>shadow</key>
				<dict>
					<key>Draws</key>
					<string>NO</string>
					<key>Fuzziness</key>
					<real>2.6766984462738037</real>
				</dict>
				<key>stroke</key>
				<dict>
					<key>CornerRadius</key>
					<real>9</real>
				</dict>
			</dict>
			<key>Text</key>
			<dict>
				<key>Text</key>
				<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 Offline}</string>
			</dict>
		</dict>
		<dict>
			<key>Bounds</key>
			<string>{{325, 158}, {85.0394, 22.6772}}</string>
			<key>Class</key>
			<string>ShapedGraphic</string>
			<key>FontInfo</key>
			<dict>
				<key>Color</key>
				<dict>
					<key>w</key>
					<string>0</string>
				</dict>
				<key>Font</key>
				<string>Helvetica</string>
				<key>NSKern</key>
				<real>0.0</real>
				<key>Size</key>
				<real>12</real>
			</dict>
			<key>ID</key>
			<integer>22</integer>
			<key>Shape</key>
			<string>Rectangle</string>
			<key>Style</key>
			<dict>
				<key>fill</key>
				<dict>
					<key>Color</key>
					<dict>
						<key>b</key>
						<string>0</string>
						<key>g</key>
						<string>0.5</string>
						<key>r</key>
						<string>1</string>
					</dict>
				</dict>
				<key>shadow</key>
				<dict>
					<key>Draws</key>
					<string>NO</string>
					<key>Fuzziness</key>
					<real>2.6766984462738037</real>
				</dict>
				<key>stroke</key>
				<dict>
					<key>CornerRadius</key>
					<real>9</real>
				</dict>
			</dict>
			<key>Text</key>
			<dict>
				<key>Text</key>
				<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 Byte Code}</string>
			</dict>
		</dict>
		<dict>
			<key>Bounds</key>
			<string>{{222, 158}, {85.0394, 22.6772}}</string>
			<key>Class</key>
			<string>ShapedGraphic</string>
			<key>FontInfo</key>
			<dict>
				<key>Color</key>
				<dict>
					<key>w</key>
					<string>0</string>
				</dict>
				<key>Font</key>
				<string>Helvetica</string>
				<key>NSKern</key>
				<real>0.0</real>
				<key>Size</key>
				<real>12</real>
			</dict>
			<key>ID</key>
			<integer>20</integer>
			<key>Shape</key>
			<string>Rectangle</string>
			<key>Style</key>
			<dict>
				<key>shadow</key>
				<dict>
					<key>Draws</key>
					<string>NO</string>
					<key>Fuzziness</key>
					<real>2.6766984462738037</real>
				</dict>
				<key>stroke</key>
				<dict>
					<key>CornerRadius</key>
					<real>9</real>
				</dict>
			</dict>
			<key>Text</key>
			<dict>
				<key>Text</key>
				<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 Source}</string>
			</dict>
		</dict>
		<dict>
			<key>Bounds</key>
			<string>{{257, 101}, {113.386, 22.6772}}</string>
			<key>Class</key>
			<string>ShapedGraphic</string>
			<key>FontInfo</key>
			<dict>
				<key>Color</key>
				<dict>
					<key>w</key>
					<string>0</string>
				</dict>
				<key>Font</key>
				<string>Helvetica</string>
				<key>NSKern</key>
				<real>0.0</real>
				<key>Size</key>
				<real>12</real>
			</dict>
			<key>ID</key>
			<integer>16</integer>
			<key>Shape</key>
			<string>Rectangle</string>
			<key>Style</key>
			<dict>
				<key>fill</key>
				<dict>
					<key>Color</key>
					<dict>
						<key>b</key>
						<string>0</string>
						<key>g</key>
						<string>0.5</string>
						<key>r</key>
						<string>1</string>
					</dict>
				</dict>
				<key>shadow</key>
				<dict>
					<key>Draws</key>
					<string>NO</string>
					<key>Fuzziness</key>
					<real>2.6766984462738037</real>
				</dict>
				<key>stroke</key>
				<dict>
					<key>CornerRadius</key>
					<real>9</real>
				</dict>
			</dict>
			<key>Text</key>
			<dict>
				<key>Text</key>
				<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 Instrumentation}</string>
			</dict>
		</dict>
		<dict>
			<key>Class</key>
			<string>LineGraphic</string>
			<key>Head</key>
			<dict>
				<key>ID</key>
				<integer>4</integer>
			</dict>
			<key>ID</key>
			<integer>15</integer>
			<key>Points</key>
			<array>
				<string>{125.916, 124.07}</string>
				<string>{152.282, 157.607}</string>
			</array>
			<key>Style</key>
			<dict>
				<key>stroke</key>
				<dict>
					<key>HeadArrow</key>
					<string>FilledArrow</string>
					<key>LineType</key>
					<integer>1</integer>
					<key>TailArrow</key>
					<string>0</string>
				</dict>
			</dict>
			<key>Tail</key>
			<dict>
				<key>ID</key>
				<integer>12</integer>
			</dict>
		</dict>
		<dict>
			<key>Class</key>
			<string>LineGraphic</string>
			<key>Head</key>
			<dict>
				<key>ID</key>
				<integer>13</integer>
			</dict>
			<key>ID</key>
			<integer>14</integer>
			<key>Points</key>
			<array>
				<string>{104.768, 124.027}</string>
				<string>{70.4638, 157.65}</string>
			</array>
			<key>Style</key>
			<dict>
				<key>stroke</key>
				<dict>
					<key>HeadArrow</key>
					<string>FilledArrow</string>
					<key>LineType</key>
					<integer>1</integer>
					<key>TailArrow</key>
					<string>0</string>
				</dict>
			</dict>
			<key>Tail</key>
			<dict>
				<key>ID</key>
				<integer>12</integer>
			</dict>
		</dict>
		<dict>
			<key>Bounds</key>
			<string>{{16, 158}, {85.0394, 22.6772}}</string>
			<key>Class</key>
			<string>ShapedGraphic</string>
			<key>FontInfo</key>
			<dict>
				<key>Color</key>
				<dict>
					<key>w</key>
					<string>0</string>
				</dict>
				<key>Font</key>
				<string>Helvetica</string>
				<key>NSKern</key>
				<real>0.0</real>
				<key>Size</key>
				<real>12</real>
			</dict>
			<key>ID</key>
			<integer>13</integer>
			<key>Shape</key>
			<string>Rectangle</string>
			<key>Style</key>
			<dict>
				<key>shadow</key>
				<dict>
					<key>Draws</key>
					<string>NO</string>
					<key>Fuzziness</key>
					<real>2.6766984462738037</real>
				</dict>
				<key>stroke</key>
				<dict>
					<key>CornerRadius</key>
					<real>9</real>
				</dict>
			</dict>
			<key>Text</key>
			<dict>
				<key>Text</key>
				<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 JVMPI}</string>
			</dict>
		</dict>
		<dict>
			<key>Bounds</key>
			<string>{{60, 101}, {113.386, 22.6772}}</string>
			<key>Class</key>
			<string>ShapedGraphic</string>
			<key>FontInfo</key>
			<dict>
				<key>Color</key>
				<dict>
					<key>w</key>
					<string>0</string>
				</dict>
				<key>Font</key>
				<string>Helvetica</string>
				<key>NSKern</key>
				<real>0.0</real>
				<key>Size</key>
				<real>12</real>
			</dict>
			<key>ID</key>
			<integer>12</integer>
			<key>Shape</key>
			<string>Rectangle</string>
			<key>Style</key>
			<dict>
				<key>shadow</key>
				<dict>
					<key>Draws</key>
					<string>NO</string>
					<key>Fuzziness</key>
					<real>2.6766984462738037</real>
				</dict>
				<key>stroke</key>
				<dict>
					<key>CornerRadius</key>
					<real>9</real>
				</dict>
			</dict>
			<key>Text</key>
			<dict>
				<key>Text</key>
				<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 Runtime Profiling}</string>
			</dict>
		</dict>
		<dict>
			<key>Bounds</key>
			<string>{{119, 158}, {85.0394, 22.6772}}</string>
			<key>Class</key>
			<string>ShapedGraphic</string>
			<key>FontInfo</key>
			<dict>
				<key>Color</key>
				<dict>
					<key>w</key>
					<string>0</string>
				</dict>
				<key>Font</key>
				<string>Helvetica</string>
				<key>NSKern</key>
				<real>0.0</real>
				<key>Size</key>
				<real>12</real>
			</dict>
			<key>ID</key>
			<integer>4</integer>
			<key>Shape</key>
			<string>Rectangle</string>
			<key>Style</key>
			<dict>
				<key>shadow</key>
				<dict>
					<key>Draws</key>
					<string>NO</string>
					<key>Fuzziness</key>
					<real>2.6766984462738037</real>
				</dict>
				<key>stroke</key>
				<dict>
					<key>CornerRadius</key>
					<real>9</real>
				</dict>
			</dict>
			<key>Text</key>
			<dict>
				<key>Text</key>
				<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 JVMTI}</string>
			</dict>
		</dict>
		<dict>
			<key>Bounds</key>
			<string>{{165, 45}, {113.386, 22.6772}}</string>
			<key>Class</key>
			<string>ShapedGraphic</string>
			<key>ID</key>
			<integer>10</integer>
			<key>Shape</key>
			<string>Rectangle</string>
			<key>Style</key>
			<dict>
				<key>fill</key>
				<dict>
					<key>Color</key>
					<dict>
						<key>catalog</key>
						<string>System</string>
						<key>name</key>
						<string>controlHighlightColor</string>
					</dict>
				</dict>
				<key>shadow</key>
				<dict>
					<key>Draws</key>
					<string>NO</string>
					<key>Fuzziness</key>
					<real>2.6766984462738037</real>
				</dict>
			</dict>
			<key>Text</key>
			<dict>
				<key>Text</key>
				<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\b\fs24 \cf0 Code Coverage}</string>
			</dict>
		</dict>
		<dict>
			<key>Class</key>
			<string>LineGraphic</string>
			<key>Head</key>
			<dict>
				<key>ID</key>
				<integer>20</integer>
			</dict>
			<key>ID</key>
			<integer>19</integer>
			<key>Points</key>
			<array>
				<string>{303.588, 124.056}</string>
				<string>{274.641, 157.622}</string>
			</array>
			<key>Style</key>
			<dict>
				<key>stroke</key>
				<dict>
					<key>HeadArrow</key>
					<string>FilledArrow</string>
					<key>LineType</key>
					<integer>1</integer>
					<key>TailArrow</key>
					<string>0</string>
				</dict>
			</dict>
			<key>Tail</key>
			<dict>
				<key>ID</key>
				<integer>16</integer>
			</dict>
		</dict>
		<dict>
			<key>Class</key>
			<string>LineGraphic</string>
			<key>Head</key>
			<dict>
				<key>ID</key>
				<integer>22</integer>
			</dict>
			<key>ID</key>
			<integer>21</integer>
			<key>Points</key>
			<array>
				<string>{324.74, 124.041}</string>
				<string>{356.455, 157.637}</string>
			</array>
			<key>Style</key>
			<dict>
				<key>stroke</key>
				<dict>
					<key>HeadArrow</key>
					<string>FilledArrow</string>
					<key>LineType</key>
					<integer>1</integer>
					<key>TailArrow</key>
					<string>0</string>
				</dict>
			</dict>
			<key>Tail</key>
			<dict>
				<key>ID</key>
				<integer>16</integer>
			</dict>
		</dict>
		<dict>
			<key>Class</key>
			<string>LineGraphic</string>
			<key>Head</key>
			<dict>
				<key>ID</key>
				<integer>24</integer>
			</dict>
			<key>ID</key>
			<integer>23</integer>
			<key>Points</key>
			<array>
				<string>{356.253, 181.037}</string>
				<string>{324.855, 213.641}</string>
			</array>
			<key>Style</key>
			<dict>
				<key>stroke</key>
				<dict>
					<key>HeadArrow</key>
					<string>FilledArrow</string>
					<key>LineType</key>
					<integer>1</integer>
					<key>TailArrow</key>
					<string>0</string>
				</dict>
			</dict>
			<key>Tail</key>
			<dict>
				<key>ID</key>
				<integer>22</integer>
			</dict>
		</dict>
		<dict>
			<key>Class</key>
			<string>LineGraphic</string>
			<key>Head</key>
			<dict>
				<key>ID</key>
				<integer>28</integer>
			</dict>
			<key>ID</key>
			<integer>27</integer>
			<key>Points</key>
			<array>
				<string>{378.169, 181.047}</string>
				<string>{407.805, 213.631}</string>
			</array>
			<key>Style</key>
			<dict>
				<key>stroke</key>
				<dict>
					<key>HeadArrow</key>
					<string>FilledArrow</string>
					<key>LineType</key>
					<integer>1</integer>
					<key>TailArrow</key>
					<string>0</string>
				</dict>
			</dict>
			<key>Tail</key>
			<dict>
				<key>ID</key>
				<integer>22</integer>
			</dict>
		</dict>
		<dict>
			<key>Class</key>
			<string>LineGraphic</string>
			<key>Head</key>
			<dict>
				<key>ID</key>
				<integer>30</integer>
			</dict>
			<key>ID</key>
			<integer>29</integer>
			<key>Points</key>
			<array>
				<string>{291.216, 236.907}</string>
				<string>{229.79, 268.77}</string>
			</array>
			<key>Style</key>
			<dict>
				<key>stroke</key>
				<dict>
					<key>HeadArrow</key>
					<string>FilledArrow</string>
					<key>LineType</key>
					<integer>1</integer>
					<key>TailArrow</key>
					<string>0</string>
				</dict>
			</dict>
			<key>Tail</key>
			<dict>
				<key>ID</key>
				<integer>24</integer>
			</dict>
		</dict>
		<dict>
			<key>Class</key>
			<string>LineGraphic</string>
			<key>Head</key>
			<dict>
				<key>ID</key>
				<integer>32</integer>
			</dict>
			<key>ID</key>
			<integer>31</integer>
			<key>Points</key>
			<array>
				<string>{313.52, 237.177}</string>
				<string>{313.52, 268.5}</string>
			</array>
			<key>Style</key>
			<dict>
				<key>stroke</key>
				<dict>
					<key>HeadArrow</key>
					<string>FilledArrow</string>
					<key>LineType</key>
					<integer>1</integer>
					<key>TailArrow</key>
					<string>0</string>
				</dict>
			</dict>
			<key>Tail</key>
			<dict>
				<key>ID</key>
				<integer>24</integer>
			</dict>
		</dict>
		<dict>
			<key>Class</key>
			<string>LineGraphic</string>
			<key>Head</key>
			<dict>
				<key>ID</key>
				<integer>36</integer>
			</dict>
			<key>ID</key>
			<integer>35</integer>
			<key>Points</key>
			<array>
				<string>{418.735, 237.177}</string>
				<string>{419.305, 268.5}</string>
			</array>
			<key>Style</key>
			<dict>
				<key>stroke</key>
				<dict>
					<key>HeadArrow</key>
					<string>FilledArrow</string>
					<key>LineType</key>
					<integer>1</integer>
					<key>TailArrow</key>
					<string>0</string>
				</dict>
			</dict>
			<key>Tail</key>
			<dict>
				<key>ID</key>
				<integer>28</integer>
			</dict>
		</dict>
		<dict>
			<key>Class</key>
			<string>LineGraphic</string>
			<key>Head</key>
			<dict>
				<key>ID</key>
				<integer>38</integer>
			</dict>
			<key>ID</key>
			<integer>37</integer>
			<key>Points</key>
			<array>
				<string>{441.03, 236.906}</string>
				<string>{503.043, 268.771}</string>
			</array>
			<key>Style</key>
			<dict>
				<key>stroke</key>
				<dict>
					<key>HeadArrow</key>
					<string>FilledArrow</string>
					<key>LineType</key>
					<integer>1</integer>
					<key>TailArrow</key>
					<string>0</string>
				</dict>
			</dict>
			<key>Tail</key>
			<dict>
				<key>ID</key>
				<integer>28</integer>
			</dict>
		</dict>
	</array>
	<key>GridInfo</key>
	<dict/>
	<key>GuidesLocked</key>
	<string>NO</string>
	<key>GuidesVisible</key>
	<string>YES</string>
	<key>HPages</key>
	<integer>2</integer>
	<key>ImageCounter</key>
	<integer>1</integer>
	<key>KeepToScale</key>
	<false/>
	<key>Layers</key>
	<array>
		<dict>
			<key>Lock</key>
			<string>NO</string>
			<key>Name</key>
			<string>Layer 1</string>
			<key>Print</key>
			<string>YES</string>
			<key>View</key>
			<string>YES</string>
		</dict>
	</array>
	<key>LayoutInfo</key>
	<dict>
		<key>Animate</key>
		<string>NO</string>
		<key>circoMinDist</key>
		<real>18</real>
		<key>circoSeparation</key>
		<real>0.0</real>
		<key>layoutEngine</key>
		<string>dot</string>
		<key>neatoSeparation</key>
		<real>0.0</real>
		<key>twopiSeparation</key>
		<real>0.0</real>
	</dict>
	<key>LinksVisible</key>
	<string>NO</string>
	<key>MagnetsVisible</key>
	<string>NO</string>
	<key>MasterSheets</key>
	<array/>
	<key>ModificationDate</key>
	<string>2011-11-04 11:04:50 +0100</string>
	<key>Modifier</key>
	<string>Marc Hoffmann</string>
	<key>NotesVisible</key>
	<string>NO</string>
	<key>Orientation</key>
	<integer>2</integer>
	<key>OriginVisible</key>
	<string>NO</string>
	<key>PageBreaks</key>
	<string>YES</string>
	<key>PrintInfo</key>
	<dict>
		<key>NSBottomMargin</key>
		<array>
			<string>float</string>
			<string>41</string>
		</array>
		<key>NSLeftMargin</key>
		<array>
			<string>float</string>
			<string>18</string>
		</array>
		<key>NSPaperSize</key>
		<array>
			<string>coded</string>
			<string>BAtzdHJlYW10eXBlZIHoA4QBQISEhAdOU1ZhbHVlAISECE5TT2JqZWN0AIWEASqEhAx7X05TU2l6ZT1mZn2WgVMCgUoDhg==</string>
		</array>
		<key>NSRightMargin</key>
		<array>
			<string>float</string>
			<string>18</string>
		</array>
		<key>NSTopMargin</key>
		<array>
			<string>float</string>
			<string>18</string>
		</array>
	</dict>
	<key>PrintOnePage</key>
	<false/>
	<key>QuickLookPreview</key>
	<data>
	JVBERi0xLjMKJcTl8uXrp/Og0MTGCjUgMCBvYmoKPDwgL0xlbmd0aCA2IDAgUiAvRmls
	dGVyIC9GbGF0ZURlY29kZSA+PgpzdHJlYW0KeAGNW8uOHbcR3fdXcKlZTLv57O5lPIgB
	CwlsR4NkEWRhjCXLykiOpdiAfzb5lZxTZBXZM32vAkGYruKjyNNVrEOy7y/uO/eLW/DP
	e7+5dYvu42v3N/fBfXH3ybuHT87Lv08P7naZs+P/seYblL7E/3eT1L97JX0t7tUduvUi
	3PIPO35471Ly8xIX70Is877k1UG3pznEFbqCh1Ty9OheYVgc1OJgOC9+jjkUl32el30N
	0ijPZdmCy8HPKfjdPaKjMG8l76hX5mXLwT1Ob09bv0HHL/H/3ekgzV4oKwYZtsHeFIqf
	87r50V4omIxPG3SDvaE1p9NwemZxIix+m9eYgco6+xVQULXPPqTVhbxhpn5F5+yG+rAu
	mDgmGfe9sG4IrBumHNKcMgYMMFDsAzCgLqZYB3fW+jIYdWjNHidZliUN9mRsOeGFDPZk
	vLlQ99ZGC9is9TUw6CPRxzn7HTOvaPB1dx3RaA5iSkLBcUX8lZaCAwYQPfyVXanibe9J
	Gk3v3eXpHwfDCYw2ZJ6DjUkVgw2ZNRp9bsph93PwJbWwWBPGFeJWHU3CAjEHW+wnRDj8
	Wmo0+CVH9B/iPu95T+IUeOuMBkFvLxucAg0YDHgfZ42vA2At6M7bnlYZWjPH+W1r4tDU
	HKIYAdJi4VnjIxbT2RIR1232Zduc37BWpBoMWAF8YoAscPpljQ2LBBELQ3R5XeZ1TcQt
	LXGOJWAVQEer95x43Ld5R4BAl+ZSBwznfNoaozMwTsdmLYIPcwhZgk/thQXL14aBD/bC
	ss0R7wMeO9gbWn/OM2Iuc8iYuaARxTMiPGANAQHCCahfxICAXyN8gFiElUtrjHHetghf
	wbzTsrNuDPucC9Z64pNXL0M7a2xQPFuyJDDUnExmDXDDycxx2nlf6IZmjvAkvBYiYeZ6
	42OMTM9TR8Q6tiaHdQ5esXBNjmmfsdwjQeVlThEzrhESc56h2lzBGOEUO7wiZj9veH2u
	RHgAYo1DSysiaaUOy8SiY3va+rNeYfa8vCy8kcEe0sIMl06jPZ/DnHaFXu0NrT/rFfC5
	zBAhGDkDjCkgWvyCUCAY0OcGRlgzXADpg2DgrywXG0JkRfLkxHePRohfeMMWUxKA8LCJ
	X5y1vu4X1oLT8Ru8lGNr9jjxZYt8UWaPAPmi65OOtrcWx1jmpRSMKvsJj1gUtmXbQUaW
	GJNfkfAW90+HXtwKihADFB5ZJG6ABrmxIKXu5DXGVp65NFMd2wO305Z8I2A4gYxISMmw
	dAUssGWH+6yAuBSf2NmX9xzegkR9/4D3hEaLu8Xf24RX5gFLcvfv3RdfYY0DNbh/417c
	/fzDa3f382+vP37/4+sbd//O/fF+EqtHHuYxuxIynMzv4EMRyz5FQLrQ4r6LyHBrZXjH
	61KmroH3P6C2VvCgV4SMPaiO62wOYehzCQ3YZnQqquH8MCQVHyHGGeGEcanOA/+ifYqg
	RtHY71NR8VB2EKSiKxK5UhDqvAZrQAEjmYjGA1aZDtSbZ++bSxjLPYOnA0mxAzlRNEx8
	gfNyVoNmHYH0K/xgFTbUq6xwibRuQL+9HLhurWVvzzQVSBMHIE1HsHzrk8g5M1rxMfFQ
	dhBaxYIonKRA52WvjdNWcBTICtS1MPBM/CRbBaE3hMHo/H6dYwxLVO8HotX7/+5evPzr
	n+9vEDTBvfj6xv3D3b9EANSw89O4ESnwJSxeDFhQW8Y4RaBVdjgtWBRFrHStbAWJh/+b
	ZipYBAGCVSjM0eL/gw6t6f+9z4zOZWExo00zPTiOSMsfHSrQT2LXlcWV0nqU52YRLVnS
	pLJMw3PX11pCelhd59PM1Ok2TPi26iNy3rnXo9hjAzagR3FAj6IBAYIpk4HTNTy9X0b0
	vCejEqcfqiAxw+k7enQtqaWvbDKNoGfSgJ7pMGn4ae1RnptFwUXtYzlCrVYyPrdaC6ZI
	dZuPviQQCky3YVLRmyo8V10dGw1Z8Qu4Fl2dywlW/NHVEzaoW85IKZMs9KOr/+WGu2k4
	+q/68EEf/t1C4CdVvEcuqJXdDZIfGn378WdV/WcMlCEl+QsDtJQE/C0lhRVpI2/lWVC+
	ePzpw48tE7VAPJwIYJ3FWgtPw8YbuQZ5vSainR3uOwKAKzK4+yERDRpJRL1CSwJgHl2n
	SaP3qTmlG1UNkhpGpBLXcfHd1FMVDPY0hGdNO1gOBunSM2shVZE7soomIDUj062YSP5p
	j5cCEcjVRdUmQvHR2UxlGVb0LPsYNB4rOEbUKiBALfuoDmFcs8/Qp2afblQ1gp4mGrwD
	m5bqOGnrUZ6bRUHP7I8lwzOWSUpMO/XBsml9SZKK6S8AgYHYHuvm+Ui9+u4ApwFIOQUZ
	oOWck0BEzsGyuiMQK+OyQGTG+fbrm0mYFvx7iB87sPJgRCtWLoQ2V3U5e8ICnCO2lEK1
	17Ao1eYJGBOVHVzhjWxhQRty77IiNb53KxYyNEZsgHt7Hns9OpDeFBOoIjcnOMyh7u10
	1vo697YWZM+SZrs9+BpPx2STY/bIvbENpDkw5zbYofG1fYgw5pC5f6jgrEjryCoJtL9E
	pBDszraALFg3ZR4bML+sXDGwD8ExndSFeezFphIly3Ag2ObPGxg+sCAt9QQXVO6k9WUs
	ZGjaQnYSHjtojq3aw4NwAiENao8myrod7QE1TKe2rmDIIShPJXH20WhIBPEZaEjEoYjR
	kIgEUcta2p4GDWlIF5UGYGNqjZQ09D6VZqjRyYhHG5JWQABjXJWGqy7gwNWISBWMiUDs
	9ONQdhC4SqATYSP1qdGrbg3sC+BMZGESyAbU5e0Xw5aAYuXCe9K5iQg4GqCSmA0by+Rd
	I8xkEI2ZDLrKIybtE67bmEk3qpoKqFUYADUdECCHrtyEcBgFqTiZeCg7CFoRBAVHiOhB
	GYoBSlAUHAW0AnWNo/B09MBR2q50SPy3OI3H4QWC7enSCDoODi5sw5jJJ6UmH3+9mSoj
	6dzEamml75WiqMIIjZEXtCHLn4zlH8kF1hDyBzgGTu+GbW7E4Ydtc3EYqGVtm9s1WJgQ
	X1ZBcz0cynTKLnqfyh7UaOcOeFEcklaAO2AlO25zA3YMxi9EUKNoHELf5h7KDoJUbNtc
	KTCWYdYACkZi21wZlQB1TvhZXomGzklEwlCBFBpimBjRGDQkGl1EpmiMf9C1ba72ifPK
	ts3tRlVTgbQKA5CmI1hGNQiDsYuKj4mHsoPQKso2Vwp0m2uvjaAoOBJXBtS1uOJx/Ge3
	uTvOeBacVp7E1SvzfyP/Hx/A8c9CwZINjrlaMCQlhJVq80TagiHhuuNAtaeukWAYRDvz
	6ToNht6n+roaHYNBhqQV4Ew4Vj4GA869ezCI0IMBYg+GQ9lBgNexE4ydBsgd2plPtwYt
	RmLB0IE6DwaW12DQObVg0ElLMBgmFgyDhsHQxR4Mg64Fg/bZg6Eb7cEgQ1JxANKCATPv
	wUAYzPsrPiYeyg5CqyjBIAUaDAYkQVFwGAz6fMa/+8VuxKXQ54IhYPX2yO0nsfDl75od
	hk1uTTt3mmMsXH7QjNKCpZ8OHfPGtriMDT3vB1pugIj9DM5zaqiwlPcqEio4L6rHQ4OG
	BxxdxPl8Ox5S3ZRxxi7HQ71P0F6p1Y2qBgsRh6QiljvwbwkV1U0B2wDrswrNKBpLWRMv
	C60izpBoAN3pvLo1TFtGgr+y3BlQ56GCulPA0cYAJMUBSIqKiQu4w5ZZdQ0uhTqQE26s
	LG8o+iHidJ4nRQYkPwyQ7GJAmqYCqaLMswGpOpm59UkYzGjFx8SxTOC3Eq1YL0XQg87L
	gOS0CST/KpB8RqjU8/rTW4J+W525IWVbOTMCkzq7JuBJKa65wrPjoxffvPlvP5MZ9qyX
	DJz2z+V/xX7oefc/fUB86ZZYPuoYz17FgWSu9n4yxO4UE0VzgbzgppiHr4OGh/ODiEiq
	h6+mS7hhgD/EyZwi4X78GF2mqU5h4hBdpsNEJ+2zxgbO4qW7+q6zinSKc0ErYndYg1Xn
	ZU7BaSs46hTUXTz9YXQdlymKHUhQ/mGZCjh+fRJdiJoOJO7Y5J5LbrstujZ4Gm44h+gC
	kE+iSzUVSH6JwAqH6NIqEjTaZxWa0YqPjWEsq9Glo2sV5cxBaum8DEhOW6ILfxXICtT/
	F12gJEN0jbsdnA63kMJB0PTFV3YQdLbbeTdkm7rZeUCuOt5KtLyD4yicB3jQvfrCg3zk
	JF9jMDLwSVPLO3QRZI6ad8yDuibjjGyoYJFhuu7F2md3cjWqGp63kYHi5bUA412G+JDp
	5MazRRsO5XL3fh7WdXEahUMJXlEt47lJfWoRb9YIioLzgA+y9PlSZLC8vnADkuIApLhI
	gw3u1SLDUMLVMobVRfU99KBYB/NiBdIcvxtVt69AagWZZwPSdJy59VmFGhly6smyFihj
	meBlJRVIjr0CafNSIGW5UHAYGfp8naLhALJSNHzhoJExPbmrwB354sHlzjia3VUYRfuX
	hsZjfZhe2GZfNzJPuVnfyOCArbIzfJKkDIwvNyErciMzsTTx5IoX28ZiBg3ZWRc7Oxt0
	lZ1N2mcnX92o0jEsoRySilh/QcUP7AzHcWVgfPheBLc6mj9Q1sXLAqywIi6gHnEAyCdl
	nWYN05aRtGVPny9FCcobO7M5kYN0IIWeGCbGYrpG2JmKIztTHTbH8BiyMwPSiFY3qnyt
	AqkVyLd1aqoTDKxPwjCQroM4CoLXs4pkZ1JL2Vm3BhQInrIzfb7OzvgFpmxk8pGdjfkD
	B/YxFnxvxJ3Mk/zxzdPTstv7pnmr0WLxc/tVK7L4+f1CZsFGC5kl4mquZpaUsdZJXLRX
	DcrNDz5YmrilQ9lknEs1WNXJuQbRMovpLLMkXKlIn5okEEXNqGrQF4dkIl91O2k2Hb/G
	sj4pKK/CgjiKVwStyMxSmyiXbNYq1WzgcEHsQJ3vaFheM4vNqWYWnbQssYaJrcCDhpml
	i7qqI+oa+pYFAFsD0pJEN9oziwxJxQFIa4SZg9spjwOQQ8IgKiZeFiqQklkQTGiiGdNe
	G1FQcBTICtS1kzB+TFxjpmWWkxNmXOyAxefTk7A7jYwWB67nkU+f9LL7T1rJzgEs2YwH
	ApWifTyPI2SfST7awhfiLZIyrkQQLfhyVCKJ67FFUsZNTC1TjjZoyNEG0SLJdOb11qcF
	hRlVDfrikFTk6o1XfeRoCV/OW58UNJLoiYM4jcKhxCoykmqtFklmTRaQBg45WgfqPJJY
	Xh3E5lQjySZNUTHpHE018EFGUhd7JA265vW9zxYoyMbt7VmYVCBNHIA0HWdukVQF42ij
	KBBZWB1KKuKVo0mBRpICWReQBg4jqQN1LZIQIcdIwtnAU46G7LPgrrlmH3w4OO5eXlpM
	/GZPriWZP/yoJ2mWgMbLmr6jGY4S+vX3Dk/ZcEzAX0LgCtzjYw9ennr+KmLH1ehS5KcQ
	bXLyoaJdf/uEIeNiG9ekEZ+kgs61K1iPL7HLxh97yJfRnp/K4PcT0GFBSfiJgVz5nrS+
	fOVLwMweBr3h0EE+cuKVL+3hDmlF36SaZm+P+DAe380f7WlrTJXTuvS7DbmyxBjRq6CD
	q/z6bS0+2Ob9NbZVeY+40MP9N46FAowtER+tEgz8roMHlPTGtQgYgBmXdagbgEsBuxKA
	toRvtwnGs9byede1H7FYC0xnxSU7wTd7MpYFBKzbmwAQLrvl29zB3tCaYHz3P0vKbkwK
	ZW5kc3RyZWFtCmVuZG9iago2IDAgb2JqCjQyNTYKZW5kb2JqCjMgMCBvYmoKPDwgL1R5
	cGUgL1BhZ2UgL1BhcmVudCA0IDAgUiAvUmVzb3VyY2VzIDcgMCBSIC9Db250ZW50cyA1
	IDAgUiAvTWVkaWFCb3ggWzAgMCAxMTE4IDc4M10KPj4KZW5kb2JqCjcgMCBvYmoKPDwg
	L1Byb2NTZXQgWyAvUERGIC9UZXh0IF0gL0NvbG9yU3BhY2UgPDwgL0NzMiA5IDAgUiAv
	Q3MxIDggMCBSID4+IC9Gb250IDw8Ci9GMi4wIDExIDAgUiAvRjEuMCAxMCAwIFIgPj4g
	Pj4KZW5kb2JqCjEyIDAgb2JqCjw8IC9MZW5ndGggMTMgMCBSIC9OIDEgL0FsdGVybmF0
	ZSAvRGV2aWNlR3JheSAvRmlsdGVyIC9GbGF0ZURlY29kZSA+PgpzdHJlYW0KeAGFUk9I
	FFEc/s02EoSIQYV4iHcKCZUprKyg2nZ1WZVtW5XSohhn37qjszPTm9k1xZMEXaI8dQ+i
	Y3Ts0KGbl6LArEvXIKkgCDx16PvN7OoohG95O9/7/f1+33tEbZ2m7zspQVRzQ5Urpadu
	Tk2Lgx8pRR3UTlimFfjpYnGMseu5kr+719Zn0tiy3se1dvv2PbWVZWAh6i22txD6IZFm
	AB+ZnyhlgLPAHZav2D4BPFgOrBrwI6IDD5q5MNPRnHSlsi2RU+aiKCqvYjtJrvv5uca+
	i7WJg/5cj2bWjr2z6qrRTNS090ShvA+uRBnPX1T2bDUUpw3jnEhDGinyrtXfK0zHEZEr
	EEoGUjVkuZ9qTp114HUYu126k+P49hClPslgqIm16bKZHYV9AHYqy+wQ8AXo8bJiD+eB
	e2H/W1HDk8AnYT9kh3nWrR/2F65T4HuEPTXgzhSuxfHaih9eLQFD91QjaIxzTcTT1zlz
	pIjvMdQZmPdGOaYLMXeWqhM3gDthH1mqZgqxXfuu6iXuewJ30+M70Zs5C1ygHElysRXZ
	FNA8CVgUfYuwSQ48Ps4eVeB3qJjAHLmJ3M0o9x7VERtno1KBVnqNV8ZP47nxxfhlbBjP
	gH6sdtd7fP/p4xV117Y+PPmNetw5rr2dG1VhVnFlC93/xzKEj9knOabB06FZWGvYduQP
	msxMsAwoxH8FPpf6khNV3NXu7bhFEsxQPixsJbpLVG4p1Oo9g0qsHCvYAHZwksQsWhy4
	U2u6OXh32CJ6bflNV7Lrhv769nr72vIebcqoKSgTzbNEZpSxW6Pk3Xjb/WaREZ84Or7n
	vYpayf5JRRA/hTlaKvIUVfRWUNbEb2cOfhu2flw/pef1Qf08CT2tn9Gv6KMRvgx0Sc/C
	c1Efo0nwsGkh4hKgioMz1E5UY40D4inx8rRbZJH9D0AZ/WYKZW5kc3RyZWFtCmVuZG9i
	agoxMyAwIG9iago3MDQKZW5kb2JqCjkgMCBvYmoKWyAvSUNDQmFzZWQgMTIgMCBSIF0K
	ZW5kb2JqCjE0IDAgb2JqCjw8IC9MZW5ndGggMTUgMCBSIC9OIDMgL0FsdGVybmF0ZSAv
	RGV2aWNlUkdCIC9GaWx0ZXIgL0ZsYXRlRGVjb2RlID4+CnN0cmVhbQp4AYVUz2sTQRT+
	Nm6p0CIIWmsOsniQIklZq2hF1Db9EWJrDNsftkWQZDNJ1m426+4mtaWI5OLRKt5F7aEH
	/4AeevBkL0qFWkUo3qsoYqEXLfHNbky2perAzn7z3jfvfW923wANctI09YAE5A3HUqIR
	aWx8Qmr8iACOoglBNCVV2+xOJAZBg3P5e+fYeg+BW1bDe/t3snetmtK2mgeE/UDgR5rZ
	KrDvF3EKWRICiDzfoSnHdAjf49jy7I85Tnl4wbUPKz3EWSJ8QDUtzn9NuFPNJdNAg0g4
	lPVxUj6c14uU1x0HaW5mxsgQvU+QprvM7qtioZxO9g6QvZ30fk6z3j7CIcILGa0/RriN
	nvWM1T/iYeGk5sSGPRwYNfT4YBW3Gqn4NcIUXxBNJ6JUcdkuDfGYrv1W8kqCcJA4ymRh
	gHNaSE/XTG74uocFfSbXE6/id1ZR4XmPE2fe1N3vRdoCrzAOHQwaDJoNSFAQRQRhmLBQ
	QIY8GjE0snI/I6sGG5N7MnUkart0YkSxQXs23D23UaTdPP4oInGUQ7UIkvxB/iqvyU/l
	efnLXLDYVveUrZuauvLgO8XlmbkaHtfTyONzTV58ldR2k1dHlqx5erya7Bo/7FeXMeaC
	NY/Ec7D78S1flcyXKYwUxeNV8+pLhHVaMTffn2x/Oz3iLs8utdZzrYmLN1abl2f9akj7
	7qq8k+ZV+U9e9fH8Z83EY+IpMSZ2iuchiZfFLvGS2EurC+JgbccInZWGKdJtkfok1WBg
	mrz1L10/W3i9Rn8M9VGUGczSVIn3f8IqZDSduQ5v+o/bx/wX5PeK558oAi9s4MiZum1T
	ce8QoWWlbnOuAhe/0X3wtm5ro344/ARYPKsWrVI1nyC8ARx2h3oe6CmY05aWzTlShyyf
	k7rpymJSzFDbQ1JS1yXXZUsWs5lVYul22JnTHW4coTlC98SnSmWT+q/xEbD9sFL5+axS
	2X5OGtaBl/pvwLz9RQplbmRzdHJlYW0KZW5kb2JqCjE1IDAgb2JqCjczNwplbmRvYmoK
	OCAwIG9iagpbIC9JQ0NCYXNlZCAxNCAwIFIgXQplbmRvYmoKNCAwIG9iago8PCAvVHlw
	ZSAvUGFnZXMgL01lZGlhQm94IFswIDAgNjEyIDc5Ml0gL0NvdW50IDEgL0tpZHMgWyAz
	IDAgUiBdID4+CmVuZG9iagoxNiAwIG9iago8PCAvVHlwZSAvQ2F0YWxvZyAvT3V0bGlu
	ZXMgMiAwIFIgL1BhZ2VzIDQgMCBSID4+CmVuZG9iagoyIDAgb2JqCjw8IC9MYXN0IDE3
	IDAgUiAvRmlyc3QgMTggMCBSID4+CmVuZG9iagoxOCAwIG9iago8PCAvUGFyZW50IDE5
	IDAgUiAvQ291bnQgMCAvRGVzdCBbIDMgMCBSIC9YWVogMCA3ODMgMCBdIC9UaXRsZSAo
	Q2FudmFzIDEpCj4+CmVuZG9iagoxOSAwIG9iago8PCA+PgplbmRvYmoKMTcgMCBvYmoK
	PDwgL1BhcmVudCAxOSAwIFIgL0NvdW50IDAgL0Rlc3QgWyAzIDAgUiAvWFlaIDAgNzgz
	IDAgXSAvVGl0bGUgKENhbnZhcyAxKQo+PgplbmRvYmoKMjAgMCBvYmoKPDwgL0xlbmd0
	aCAyMSAwIFIgL0xlbmd0aDEgMTQzOTYgL0ZpbHRlciAvRmxhdGVEZWNvZGUgPj4Kc3Ry
	ZWFtCngB3Xt5fFTV9fi9962zZmYy+/4ymZlM9p2EBDKGBMISDIuQIMGENaDUsAWhxW9U
	FIgUEWQR1IpaVjVDSGGA4pdaEGltRauoVK2tYOmSr377wy7AzHzPfRMipNqPf/TTP37z
	cu7+7jv33HPOPee8l6WLl81BGtSJGNQwrbV9LpJ/JdkIMbpZC1vbk/XUBoTw9lkdS73J
	OpcB/ffMbZ+3MFkXn0BI6Zp3z4r++01lCHla2ua0zk72o+uQl7ZBQ7KOiyFPb1u49L5k
	3dAL+VP33Durv994GeoNC1vv638++hDq3u+0LpyTHF8iQp7efu+Spcl68XbI29sXz+kf
	jxsBvzcRhtYguhcp0N1IQATp4GpGSLisdCEWemk//GZnqzbflVL5JdLTaRG6q/4xOX9N
	+vH5v8+5HlQ9Lv4DGhQ3xtOcD8VDCKkx9PepHh/oke+DJBhFk7KiaDRAFUAJQFbWbVbU
	iXejjQDPAjBoPn4UrQBYB/AkADtQ2ge1o/jRHlYMH8MrkB2PCatYz2SjzWNVqjxvRzHf
	+4znA+unx7ENdu+32NajQYrblPhZ/AM0G3nwD5Efr0R1KAPvOBS6x9MCXftQO0AnACOn
	GO/rcRd6XsHZyM9iuCeA3Cw+7Pl9QY7nUkGU4B7Pq8EoC9lP3FALp3hOup7x/LdrnucV
	gAPJrv0hGHHYs891j2ezO4p39Hg2uaIY7nk8mS1zwa2HPQtDWz2zC+T+cVuj5ECPpxz6
	p4RVntIyyVPiuujJC0ZFDPUc1zhPZsEvPOlwIwzzwqT+sN7jdG32DIUut6s2OBTgON6P
	d6JMvLPHP8ZzDIqw3EOjQ2Vbo/i7h+oyCvxRvDJcWpexNVQX9IfGefyhkcEglKe8LqwW
	7hRuEwqFLCFDCAiS4BCMokHUiVpRLSpFURSi+MWeKg9/HB9AVUCWA4dEXuSi+GVoZI/j
	l+TGl46IrEhEJBqjiU+AeTEyRvGBXh0tQeEwL5f4KH7pULLppbCHpSVW7tARWoYEUkSw
	SNAYFMHfj/LoYXNHlbXKMFxfPrLmm5IWuedGmvXNPyt2RbaOndQY2e9qihTSQsLVdGO4
	9UbhG/Oly6BrTnVW1tiJKw51tC+YWzvHV9viq50D0BJ5tKPNGumc6fUeXNBOO7wRJtAy
	c1YbzVvnRNp9c2oiC3w13oMd8n2DuufS7g5fzUE0t3Zy48G54Tk1PR3hjlpfa03ToZnV
	i5tveda6gWctrv6aZ1XTyRbTZ82U7xv0rGbaPZM+q5k+q5k+a2Z4pvwsuvja+ZOqlywF
	7vTWzh/rjWRMioyeMK0x4m1tqoni3dBYswxxJ5GOO4EyuE5kZ/OQB6HEBwAXaB6/I/EZ
	dwbp4gsT/8tUwKYepUDiVZXoJPo+2om6EY/2QjkDzUDb0Vm8AGR7OupF57Eb5YLuZVEU
	jUNv4ETiLTQXvQDjl6JX0RZ0EKnhnoXIBL0bsD+xEuphKM9EqxPPoXRUhh5BJ1A5zLoB
	9SX2JQ5B70R0B9qPDsD9P8c+cpBNTbycuIhENAHmXA09byXGJbqRAWWjatQAravRK9jP
	XEi0ISuqAOyeQj9Au9BP0J/xg7g30ZboSJxL/BZY1YqcaBJcq3Av/i3TzT6SeCrxx0Qc
	KJGBMuGpLWgzeh7m74brJKjWWnw3Xoo34y0kTB4kvezDnCUeAzqE0Ci46kArrwUKHEWn
	0F/QP/DnxMromKXM6URJ4v8hFRoLq6QrmYM64FoD1wZY03HM43w8AjfgVfgJvAX/imSS
	O0gjWU7uI58x45npzArmV+wStodbz23nVfEvE8cTZxLvIgtyoTvRYnQ/rO5VdA5dQVcx
	A3M5sR9X4Go8A65OvJMcxbvwUdKAT+JzZD/+Df4Uf46vEY6oiYlkkaVkMzlAXiW/ZOYz
	W5gnmd8wX7LDOcLt4i7xfuHX8ZnxdfFfJioSv038HVSsiCTYmWo0Ht2FWmG17agY/Res
	4iW4umHXTqHT6Kx8fYqdqA/9HaiAsAHbcSGuh2s8vh3PxfPxM/gYXK/IuPyVwEYQBdET
	C3GSSWQmWUg6ybukk3EwmcwYZhrTDdfrzHnmGnON5dhU1sSOYkej9exCdgdcu9m9bA/7
	JlfODefGc1O4Tm4dt56Zxb3Fnefv5zfwPfzn/BegFscJ9wrrYXfOAs/+BHj5qx+L0wH7
	QvQdNAvX4JloK+zGLtyKuoC7ZuO1QK92lJFoZu5nRpF84IZX0HeBW3egVWgdMx3tSrzP
	7EfvAafcA1N2oj1sNXJx22B3HkT5wEX9VziUGcoIBvzpvjTJCyrf6bDbrBazyZhq0Os0
	apVSIQo8xzIEo+xa38gWbyTQEmEDvrq6HFr3tUJD600NLSDK3sjIW8dEvPS+Vui6ZWQY
	Rs4dNDKcHBkeGIl13kpUmZPtrfV5I7+o8XmjeNqERih/v8bX5I30yeV6ubxRLmugLElw
	g7fW2lbjjeAWb21kZEdbV21LTU42PhoGcihzsqniCCMVnTiCRrSuAgWLRtARtRG7r6Y2
	YvNBGfoYf23r7EjDhMbaGockNUEbNE1shGfkZM+PAJ7oUfVs3+xHo2E0s4WWWqc3RpjW
	pghpoXPpsyIWX03EsvKS9avqjVLt+ps6I8Q/snVO18hIuOVRIC6tttBa63qojZ3khWnJ
	w02NEfxwPxIUxwWAKUU3eSb4WxZ4Iwpfta+ta0ELEBdNbOyxh+2y8o2ghsYeW9gmV3Ky
	j1rvr5Bg9Udzbsu5jeYVkvX+ZP77h5Ltb5+kufX+U59APnbiAAEwpYBvNOAZ8c6SH+ID
	ZMtoMqcMdc0qAzrBrwnDMucDPiMiBHiG8Uc4/+jWSOekG2i01SSRa1lQ06Ow2eVDqLoJ
	xrd06YbCTsF4nc/b9SWc1i2+vj/f2tLa38L7dV8i2kk3eoBXIrj1RrmDHpZ+WHWb1ddG
	97dD3lOo+6y1NzVAnZKG4hwxwgHe0ChFvE3QANZk9tgoUjQ0HsR4Q1MUJx6OohrXUbBR
	mbtmQHc2ZbX5NfB8qORkQ0OmBKXcbO9IePJIyiveLm/X6Nld3pHeNmAm1i/n0DGnqykP
	KDipEeiEJsMTw02OgeKcpqahME8enQdugeFdTTDDgv4ZIJeb8mIwKD8bDlMm0NA4oTHS
	WeOIhGuaYBeAfU82NEZOAuc2NcGoggFMAeNV8639OBcCzgWZ0F+UnAVsl06Yoqmri845
	qdEnRU52dTm6qLwl61GMBjeE+xuiiA6hJI/izga4FzKf5JD3QPJJgFYTpWkxsPQNjgKb
	/V9TuHQAb7hzCGBbKlO47N9E4fJvQ+Gh34rCFQOY3kLhSsC5glJ42H+OwsNvoXDVv6Zw
	eABvQPI2wDYsU7j630ThEd+GwjXfisK1A5jeQuGRgHMtpfCo/xyF626h8Oh/TeExA3gD
	kmMB2zEyhcf9myhc/20oPP5bUfj2AUxvoXAD4Hw7pfCE/xyFJ95C4Un/msKTB/AGJO8A
	bCfLFJ7yb6Lw1G9D4cZvReGmAUxvofA0wLmJUvjOAQqHHRF0sx7uHKR20b9dMU+/ieRg
	KXEGVE3KwXHej+7gpiA3wDbwv6axn6JuaD8LjraPXYKe4vejbXw5aoD+bujvhbwFxkiQ
	F0P/RBhXAXkZQB3c54R8GMBqfIZC4gLk62CO1bQNgI7tgGeug/vo8y1Q74SyCsAE4KQA
	6N2IK6kRj7ug7gUrGtz8b/iRb2gf3MyAl8jJjbycCuBh0JiUElIV+IUI4kBalAI5RBi+
	5qeHNgNKRUbwJs3gEVmRDVrsyAE4u5Abyh7AVEJpyCffnS6n/z8kfhSQl1EMHthE9DGe
	if9C0kkluZ/sZgqZCMuzG7npXBd3me8RioVnxQXiYcUMxcuKD5VmZZmyV6VTrVVdVYc1
	Xk275mfaFu1fUxpTIuCIVSPEnoOYAAOxxapkvE/MA6MMQNRFEToHQOtQZj6MIhYAQVn4
	EB2DOxCaknUMZuEgzy8o0kv6IEA1uyF6/Xfciasjomz9NYgdAefcgT8mY8k2eI43rER5
	DLZzyMZCRKr6kHSsDsI44y/qPkN59X0F+amSSboD/zWuJNtoDIKh+8oNAxwJ8IkKXQg3
	1OFG3IaZtcw2drtynzKqiCr5DCVGAs9jIioUkCiRwOH1mGG9RqXSb4A2I8f5DTBApeIY
	hZLlOawimEHELYhR3BRWgCvGK5QMB7W9YYNGY7HYuWfwM0qbWrNLWj8DULSNv2Ktj8Vs
	42vn1Hw2ssaKqiyVVZX1scpYpb68CusN5eXwpy/PW5ObtUo3FixG9qQjwp5qWpNr7W9g
	oIE51ZTVP3aNrrJSACjIx83NqBmrcGoR9jES48PMht/0PfxbYrqwJXb8B2+QjWQaWRdb
	zsy6OgJH43Xyjm0DurBQUoI0ZKAHw2XTNNP0C8gCzQL9SrJcEkZr6vTEJXpSWE8q0DAo
	ui1E5Q6KbIFjfkqBz56pMPkzzLZQZhTfdUjqmEt34Qpdz3jdX+v7rvShqlhVn6E8L1ZO
	11aQP2JF2GC1c6LNzwcEK5uFObuYhVEWhvDTAw/ACnDhkNKS4mDAJ+lvKjKSl/q9Ai+Y
	k3kIk1P3j/zOsuoH40/jl46ML3hs3Kr4sp+S5ZgsDN8eql9UNqvp4fjHsc1Mg2/IYxsL
	nfHy2LQFI+56dqgndo1L3XHn8keb8oJZpS37Nix5EbhiWuICt4i7hKgGOBiucHDb8FaO
	8WAP+yBew61L5SaJzCMuvd7ED3Ux6qEmhZu43TamgFToCvR2r6LAZvN4d0kLkgSo7+tf
	PqwcVVX1ySTQAV/C8ocip8WfGtD6HQGVWVGINEZdITboU3SCE2ocYgoxJiyjtKoLUYoB
	EtHOF2IWQ0JjdFhXqavMykqmtOGBZtwsYosvF/vSkF5nKAICDiniBV7yBgN63ZBSyce6
	cbH+Vel0zwfxL//38w+XDHO/at/UHX8vgV6+9OIxPCqDuxS/cHzD7vib8dPxePy/9zU9
	fvnpEzt/gV/Eted+J8teN8j4fuATBSoPWwU3yyoYNwRuFSJ/N7arGNGPbEpVFE89JG35
	UOaBfha4CMuvlBkgKZR6EEy9r5u5dv0N8lYs7wx3ojde3R2bTeX7LCSbZK600LcEx+TD
	IjcLYoJUfZC8/ILUIr3v7NmzVC1Apy/xAbMQxqei6nCagrfx81Qr+W2Eu4/FCgVrIJpN
	rELvR3aT0i/YjKYonnBIWt+exC65QQi2BraouSC/GQNzSWmBYbjIZDRTGpboi5iFQ+bG
	f/fDPx6/e9EPCtyn8eFj83586NL8+fetWDjiGPMrivNTQJexgAOHCsKpiDDEzXIiYxcw
	8YNy4iGGPgnEgkr+DbH4DMnyXgX0AGVnkp46Qy5fnwBL+ks3zAfyyFvkNf083FSDx4JC
	wQrGjG3Me5hLxU7GqHKop+JG5h38a+Yd1a/VSlbJamrJI4SdQLYRElJmaMqUZZpRZCrp
	IIJ/tkZJGAODiUptYHjRBBqJpSpzZ1ij9DAqPqbGJKbxGKDlcCqyGTuAPuN1VIIv2q6U
	l8Of9SIVZqqukrrKYCmHMPhBjTqK9/cSTOi27+8hhFnD1eeujLGrTq3hknlBPmpevAgv
	bl6UKikwbLu+uLQE+zAQ2KT3bcMuvBs/j+0n2Hjz6fg07hXuxLUAe+HqCGZWzrnl10Ls
	ezmlHxVff1rW3w2Jd7nLIJ8p8jndFc5eA4bMGfxT8rp4VsmPEE1DUxjHUEHhJE6nylDA
	2N3WApXN5X5/kEgOCKQsi4XIrglgv8LPBcxaayHYBIZCbBehpOOhZFGbCnEqgcSmdBQi
	PQuJLIM0ob8H4C2axazXCaRf2AwSMpToEJVEo0Fi2J3HN+05Fd8Sf+nVl554BcLAjj/F
	//dPF+Of/A2btNylqz+Nn4sfuZBAn7yPx+DMd7Du6nN4xZcQkq2Mn4m/eSV+kJsBuonK
	3t+BJ5SAX2u4ZL56vmGFeqWBrTM2GtuMK42sILr1Op0Sa1OoTCpFwhvUrMJoLGDt5hQF
	iKbJHMUqEM11N4tmTA9bScUzVqkDskCGqSCkSoWgaXnQvz4kq2GpsLSkm2w59cX5j+OF
	Z5jO+6qXxJfi9Y/s4U589PqLidhm9uhQT5xZvBEMUYiVIu4+wFUB7xufCBsEzWhcxzXh
	Rm4+N9t4Hyeaj0Pg2oYc2Bmu9kneQIthkWGZkTG4PUaniZHcZiMbMKT73UihcAhuFQk4
	HaLXb/L4zUxBynyHPSQG/EGlLSN0Xtpy62Fzpe8duEC4KqtiyeWU6y3yqUNP1GY4fbLo
	EYlhNcnjhZEK6VnCC27swXCsWEygQ/NwQF60jxm1/vnFw+bG7WfI3r0L31w4c8pUTmBU
	htwrSjWrFmaXr4xXnGGc7ZueLneDmbGrYEZs9d4i3+LO05NDI41SauWULzcWOGJdQJOW
	xLvsX4F38yC2Gw/PCKUEfYFAqbZEGhWYGVipXZ6uuFu0ai1+0qRt0+5PY5TaoWnpaUqG
	dVofMeblZTmHGhl2aJYinyi1oj49zZORn6+3+i2jRX+GvdDj149G/jxbQeGz0oL+DYaD
	96sDyKAHmwLgpoOI7nxurKh5kSwF9Rm5eg8SSYAEcvy83x5gslEWysmVMy4TzmdXqicL
	OUzWLGyz4hw2CymCqizsV+FcKAshSNwGJ3SaIZElRKeTTyoqI/S0or8HHgBpoXSWlSts
	gUzqkuL0okLW5KNUT+NNRotZ3guTkfXB8TUEY7dQPOtq+/SeseOeO/PTCeux4drv8Yjj
	KQV3XojsmFZx7pdbJqyPP/2n+P/s3MmQenxh1fhN3uHP3ldU6M/JLpl+5LX4b77sqFry
	xMx7Cr35eWkV805deXv9o//DqqjulkCu4CwBG7Q4bMe8GwmEFRWgIdE1wvg59hpvE6nR
	Bvqw/gqIxxXZaKMSA4cb8BI9yfRSCXs2rv9ZXM+d6L76F04LwkrnLgY5KIW5efRYuGYj
	fhaTMJ6MiRnj+7jPMJnHtnFrWcaWQfwGhmERtSo5zBGGB2uSY0WR6mnCPMMh/AxvEzYA
	FjZAA0zH8nL4S5qPYDxWgvloKMdr6nOzqIEIijkMCh0jBl5kY8Jza8RVulNyQnkfNS9a
	tFhBwDzEWAfKd9dvYpd/FfsD6F0X++nVEYCurGsnJj6U35akwHuwSvRRuCwzHyt1cO44
	g0V1uvmKBTqhXDSoFYyjUEhXuHRqV0UWyQ1VHKkgFYWZfoNO4ERnMM3ijOKusM/i8ghB
	V66KuEpUlUJlpdMohDL3ptuHO0LOMSnBMtuw4T/G22AzjuKtqP9wTrLvxdipAXVd1QdG
	BGVbKsi5fbl91JwE+ZYZOKN0iCkNYZsfl6ZIyOp2SMjsNUpYSkNDiITsLosEmwUJ5U0w
	oL5ix+Z0mR2HYS1OwWAzmXApNT+BF8HU9A3HRVRF6KlBAI/QgoUVDARpFigpLh2SirWL
	x9/VtFVqK1w4s2AS7h1uUj+08vsVknIv97fnT3Qss/jVbn1mdqA506wY8svvbTlxbFvX
	m9OyR+9+3OTktRpn3jx8j5htzZk+aVzmpNd21tVtj21zpjHMw2q+2heuW/CjtVteSMUX
	qV6Ft2nMOXa87KfuCeftseHt1r3ifiszRtTvNDKMkXfZBY0LrAPB4bDoggbMBIne7lIG
	LTYnfGIgHJIWr/rK9Kms7ysvp2bZTUpBJmUxsol+tUkZQNpUXSBpl9qgBnapJNulKrMm
	AHYpJAorH6B2qfQ1dikV9WZkTlqlQNYkBYso6Qicj0UCOf+ppVu3+P4Xx+Sv3dT+kK3b
	/cXxt69iwztOdnzkvVkP7V347K4P1y1/9zQu+gxeBQ7lgAZliQtMH/cq+G8utDxcOEQ7
	SjtVu4fd5+D8opGkuHRIdLmEVCVxWVRcbmquLqQ32D2qoN3m9qyRFlffvPzYRbBM+6hp
	rgdvS1663epUKBHGVhWszQkJspEAUjrEACwQ/mQFZqCsIDMIb4Kj36IHi7SELguVFBuK
	/rpp16pdu1eu3Ye7JuUPe+m5qhfvPRS/+vnH+K7L7539+U/P/YwMKXaPJa6rw7fMasQ5
	V/+Ip4JvWpe4wNrh7aQT3mT7sTq8Ypv4pH2Ph+G0JIUzmrSGFJMxrA4bxZAdj1UdZs7g
	15gzjvfFDxTnPe/7Llsu+1Rn9GcMZLrISekpO8yu9HJeEMySyykoXWaVX9jm3OM84nzP
	yfrNKX4nZ1OqBb02mOIKcvZgeq4QtNkCwXek3c391sFF+ex4JyZ7orJDmtc8wCfUWui7
	4cqNhPgSx8CrX8yxvAe8DYMuVWfUsbzan+ZID0AcxRXAbpfCIgSQyqQNYI3WZ5egiYNE
	tAJfaXSQJI2qpFsDspmZlfkAXtSMFoE/S89ls0lygyRSt0aLQS552dFBRfJRncaDrd17
	vqzUoLv+Obdx2/cn5xsPCrcXTFxx28TX43/E1t9hjypjzEvf28thHzvq7jsm3DPmuedP
	N5eOqng8t8GpA10IDj+ujgeWjXzwUBemH1iB/ob4FbFwb0N8qD6cJbh4pYvBKcZys4Y3
	KG2gnLUafchiEAwpWo+WaK8bbVbbdWne/f0UbC4/Re0p3c0HRhVYJwX5hiGlRYVgauQC
	y/CmIpMPjhBfSVHJj3xVvfp0i9Ommujt6e3ZsoWrLp5OyAsE3/Hyhuuzmac27JV187B4
	BXMZeMWDcuCLiSPh+lLjaHG0olFsUqxV73Psde0L7s466lCFRcacFtKeUqaB+mX5kMum
	NLiUKblCbi7nZHLNuTkhzp6v1gY1wwNBpy0v/yYBudJXTjkgdvFL2Od+DQGSIm97UlSy
	fRl2t0qf7tcFfO5AAGXYIdGrtBJK0ao1fldaAAcdIdATajCI+5Vuvx0gSxGVnJIifdLl
	Chb1GwWyZk3Xg3pAsgK+4Yhh8r0ZRSW7K9vjZ1/6s/aIJjjsoTfDAaZ0+6qX49ewcAzX
	vPBfr4z0b/7eq7dnx99iq4f7Rqy5XvhGx4WdP6wLVm6a8tHEhr+Bs6HBufFdJ3vu2vGj
	E92zVpMceZ9XQ3CI6hQzmhTOBqkRLYJFDLLB1GXCMlFM1ZBUE0J6Fy+Y1EpNSGm3YlMI
	mW0WK3x5dkiamdQpA/EOeiBTjVKOaawDNYOHB+dJ8hAB7ydp4+h9q3vDRVMf/MOknKPu
	gjXth3u5V2MfTpDKn296JjaBPN8xpHHH+djrlA8JfJWCcAUczDS2Vhp2CpdYYE6eUVIz
	Bfg2JDCgsBX7v8LkVKzy1ADbVcmxMFBReuC01Ufgx2ZeO8+deIPOnbgQb8Bl8tx66mBP
	Ao+bsv4U+EYP0/gcjdtBrjgHHrgWBujzjgEeNF6XX4CBafFwDE4y3UU4EIO4rLc3/tyK
	gt5AVUTj8rB95/5RzPqms4evDVk2dCahN8Lk6yChcTgGhcJAWUbJwUJgHYiBSN5NywCf
	M+lyJv1k37reXtnrp3MATXg/OwqimQ+HKwRR0PIpFtGitaQExSCotTrbFNU8ldrnV9pd
	PpuSsBa/5LK4NLyAeIfTz6QqM+CZ+hB8qIZ77CH6fR6EE3GuHxjWFsyIYs3NG3tRd6Xv
	SqwfGYjVgfHXB/ov6Vnc2GVT/y5bblgMsNnAwjy4uTftek+4uGlR5/js9Mrn5rw/PvP4
	3fULnjxiD7XP3dPL5m2/PX1YVfrIKZOemrwhNoRcvrthw+7Y4+T4wsKxz7xJuUHmBaYP
	ZN8GVsCMcMER/gxPWN7IB40d/FKBM6qJ0apzcbBMq0ppF+x2pA4p7E6caw3ZkM0B5tgt
	LJtU80kJh3X1fcW2GBjXdNNSKN+C3tViWA9efWDc/raLDdlHXPn3h0NjynIcvXgP4D9j
	4g+mPkf5d2blbI25umTR/NibgCzsdEXiA1YC20Utx9o3hou2i1t1T5p/yO4Vd+v2maPi
	6+J77CXtH4zqoSLvsgpql0FlE2w2Ewmm2B2KoMlmd0SxAiyY/hMq6dwM6Cb59M6G1xAB
	VaoCThM9CWDBAiVOAyWlUR0AfoZENIPBwmghoQagnFAPPt0gO4TU+zAXGSCERiQ4zWUj
	5ZOH88cd++HWrc/DB2rX43/7KH4dG37PL8Upu7fOeOJ6z4GLzIX4n+NX4rH4yzjrOhiR
	YWqndMTvYP2wdC28Q1gazt4n7rGQDNHr1Gt5l0lI4bUupypNS4JWe7oyV5crhdJSbL70
	NdKJ5PJo7CW5N/KhSzemP3LqNDsQZw+wAeSAhXFmSLBNG0CMRV6TvCLqZaUnXVzYM+pQ
	FUFIS+ZP+GiIiiqYsHofeW2Pf+Sx47V+SOO53aXhO797OH5k6Y4VE/Mrelf86u3O6QeP
	z97xvam7mYMbRmdUxv8Aa3xu610l7tGxj/rlmGwCGdSj28OBIBPQDGFGsaxW1BGtQq9Q
	B0XKhnqlaE/F1A5DNkNqFNeCYCWPSLrG8RCsr6qqrzoVOwXnTH98mOpMmfUGzki9b90B
	0wt3c1aXzqFbuwlE5WjpTsK8wpDuxbHtVC6qE+8xh9mxcB7m4dzwY2WK7dxWw5PG7abt
	mXxGuj9YKo2URqWPCk5Jnxqcmz4vsEK9QrNC2+Fbmr7UvzSw2703O5UB84TLYXNTIVro
	sDitphxjbkaKaj5EG0r9xJ+mUbJZqdbXnK5UgXXl7shS5QkKrY4IKE/Ks3usZmvQMjwj
	IAQz7AVaT1A3HAVzbfkFPQM2FaiQ5JlaroMSXW55HqT9XjnEh2WVknTHx+EcEjCBGy5p
	PRJSwKe5GDxxCXGZUHIZoM1htErYm5ImISlNqxGDSgkH/AoleOYSfI8NiVvvlKg3nvR4
	ksFiOWKcZHrZ/0EQa29OlY9emV363XH5tBL+2R9P6vjPRX/N3tnbhwWXPLbutqW/PvqX
	u0eQ/Vxg+JNz59dmjF/+avX8Dz7+/IyAj+CGaflTp95Zmw7WaFrm6Ae2/3jDtLZhhaPG
	h0dm2lJdedm1Tzx27oNnyT9An1sSnxMFNw20w8QfaXKVJ7U4iqvCftZcbmF4rVJvB3UN
	XymGkElrSmE8DGGum202O9hb/R5N7FZ7Ky/poffpYhflICu1sqgc3PDhAiXU5Np7+MCB
	gKlA4zZ6RgTvn/b449y0+LubY7VlqSpMNijEB+aR05vhzCKoM/Ep8zHIM31XOCM8NGp8
	3UgUqaLRlmozZvDLmffAbECcVol4jZID3WUVrFZwk3KVIbXKbschiuzbN05o+dUAZf8B
	26qqkjIENRcgOpVEFHxQ6lgMkW3dIODrx2X2/Id+XOPv3U98xfM2X5qUg7tZeKsysbhl
	77SnifbaW88My5z85MR15H07lU8IcDB/ZPPgzSsJ51bj05igeaiNtDHz+DXsWm4P2ktE
	+PqU1LJjuEfYddwZ9nVOHJ2xJIO+yQJVK5uyEE6IJtp7wbj3slH80BGGWWiAiC+Ejx8K
	u3l+oQGexPEsg/sjF/AtopJGLphucgxTy2X1IdzN25LvvD75pP+t11dhC1i2oVyA2IVu
	/MV6IZlljZ2wIuwnITksEropLHJjcng30g1hkYF5vy4gwgm6LPiDmAe4ERB+VmAa8/gQ
	u3HW6fg9J+PL2Lzr25m2a28BhQgyxUfLNjV9E/yz8He6TGute6yMwFv4MkOdodEwT1jO
	LBfWG7fD2/Xtpm3mbZa9aK9ZV4fGmkZZzprYGu41jqzhdqPdeA+318KlZ3BWk8UMdppJ
	rUpxiVp6mJkdQBiKt8Vk7VY/ZoYz7Z0klYE89RettwR2kkcbBHsKbXlWiGhCjKcc0+CO
	wQQvrc0LDRaLlcOYboAVXhTqVp2SMxFyTIN6i2isBxfxDBGILLgl1IEqHTIcDwFKMIx0
	JvDQzOqnOp8KhNx5mbrCPB03XBtf+gYEQtm8efHH439+OT63lxdf0PCSVXwinR0P5HqQ
	nuXgFzH3yX6RA3yQ1nCp45INfeUfucBB8uiVEqzV4Q5ZPf/kJnmlt6V5/Z74gNieB1+p
	38KCl6HUmqbOUlUf/mZ/yV9SZBJARP7JbyKpvfD7Z+/J88YbZ66dl+WZrmEprIGupjFc
	QMC+15ldGuzky20KHnFKN6CPQthuc4YUvJ5PsXvsxH5dgNcJX4f8DeMw+R5XxhwQl8Dm
	xknfrvgrl0/gXXAa+5ilvb2xz8HL2/xda9AGTt84W0rSwAVH75XlSxh2F8HZtvKHN1D9
	I/8Sc+Ab8K/7BaGRgW8dIHYAFnEIvmfPkSPNheBGlaBSNAS+vq5BI+VvzUfDv1fQL8rr
	0e3yN+8T4Tv2O9AUNBU1oiY0HZ2Uv3LG8H0Elh/F028oqqvrxtdNzqqbc0/HnKXzZ7VC
	T7KXDnkBIAJwEuAcwCcAXyQHYLgXewHyAcIADQAtAO0AnQAbAZ4FiACcBDgH8AnAF8lF
	Ex3kXoB8gDBAA0ALQDtAJ8BGgGcBIgAnAc4BfALwBSVIov+H4DdQxsg7qE69wJv7bxtU
	rx5UHzGoPnJQHQzeW+YbM6g+blC9flD99kH1hkH1iYPqkwbVJw+qTxlUbx1UnzWoPntQ
	Xea5m+g3d1D/vEH1tkH1+YPqCwbV6ffvN9Nf/j+6m573nUH99w6qtw+qLx5UXzKovnRQ
	fdmgeseg+opb69fwoLosnf8Hczp6KwplbmRzdHJlYW0KZW5kb2JqCjIxIDAgb2JqCjk0
	MDIKZW5kb2JqCjIyIDAgb2JqCjw8IC9UeXBlIC9Gb250RGVzY3JpcHRvciAvQXNjZW50
	IDc3MCAvQ2FwSGVpZ2h0IDczNyAvRGVzY2VudCAtMjMwIC9GbGFncyAzMgovRm9udEJC
	b3ggWy05NTEgLTQ4MSAxNDQ1IDExMjJdIC9Gb250TmFtZSAvQkJITkhUK0hlbHZldGlj
	YSAvSXRhbGljQW5nbGUgMAovU3RlbVYgMCAvTWF4V2lkdGggMTUwMCAvWEhlaWdodCA2
	MzcgL0ZvbnRGaWxlMiAyMCAwIFIgPj4KZW5kb2JqCjIzIDAgb2JqClsgMjc4IDAgMCAw
	IDAgMCAwIDAgMCAwIDAgMCAwIDMzMyAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAg
	MCAwIDAgMCAwIDY2Nwo2NjcgNzIyIDAgMCA2MTEgMCAwIDI3OCA1MDAgMCA1NTYgODMz
	IDAgNzc4IDY2NyAwIDcyMiA2NjcgNjExIDAgNjY3IDAgMCAwCjAgMCAwIDAgMCAwIDAg
	NTU2IDAgNTAwIDU1NiA1NTYgMjc4IDU1NiA1NTYgMjIyIDIyMiAwIDIyMiA4MzMgNTU2
	IDU1NiA1NTYKMCAzMzMgNTAwIDI3OCA1NTYgNTAwIDAgMCA1MDAgMCAwIDAgMCAwIDAg
	MCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMAowIDAgMCAwIDAgMCAwIDAgMCAw
	IDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAg
	MCAwCjAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAw
	IDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAKMCAwIDAgMCA1MDAgNTAwIF0KZW5kb2Jq
	CjExIDAgb2JqCjw8IC9UeXBlIC9Gb250IC9TdWJ0eXBlIC9UcnVlVHlwZSAvQmFzZUZv
	bnQgL0JCSE5IVCtIZWx2ZXRpY2EgL0ZvbnREZXNjcmlwdG9yCjIyIDAgUiAvV2lkdGhz
	IDIzIDAgUiAvRmlyc3RDaGFyIDMyIC9MYXN0Q2hhciAyMjMgL0VuY29kaW5nIC9NYWNS
	b21hbkVuY29kaW5nCj4+CmVuZG9iagoyNCAwIG9iago8PCAvTGVuZ3RoIDI1IDAgUiAv
	TGVuZ3RoMSA3Mjg0IC9GaWx0ZXIgL0ZsYXRlRGVjb2RlID4+CnN0cmVhbQp4Ab1ZC3RT
	Vbr+//NI0idJX0kT0pM0adN3S2kb0hZ6WpK2UAqlUEhKKw2lWBgKBWsVEabyECiozBoF
	RMYnPgbFmxaEMD4ul2F8zMgaFb16EWGcEUWxl9FBUaHJ/c9JqZTlcrHmsjw7+/x773/v
	/X/72//e55ydruW3tkEU9AALdY3ezgUgX4ZnSLzZ2uHtDOVjLpN8tbW7yxTK82kA7N0L
	Om/uCOVVPoBw682LVwy1jz0JoFzc3uadH9KD1L6onQpCeSwgaW3v6Lo9lI95l2Ta4qWt
	Q/rYo5RP6PDePmQfqD8wLfF2tIXqG3pIpnUuvaVrKF9Nclzn8rah+ugmfH8FpNJwWARh
	sBhUwICaQhMhOxtuBY60kp5ieXbDo3NHlX4DGpXc3eO2nB4p8ar5pe8vnrxsi1gdVkn1
	wuT6koLaKNID6QCRSPoPI1YPayStdIX7oT7TD9UUJ1AsoJiR2acS/4BbIa75ghiGAgcR
	wgeJ//sK5hD/n8p3H+aIkVEQ1rq2VGhdu7Y6vTwMa8DOIQjoAqssnf3WZwU/Tui3WkiM
	Dwmm326kHIhhdqswaJ8nXLb7VSgahO+svxUuUvzWWiZ8Yx0jvE313rJXCcfKSd8v/CXD
	z5D4s9XPoThKeN16l/CCPV3Yby8R+m1U1i/0lZM4IOy23yU8sU4ueTxDFo9Z/bizX3hU
	EgeER6j/bWtlxQOhhmtConOdbGjpPlks2ednnj0gdFhThXnUEMUIodm6WGiyOoSZ5X5M
	6RdqpWYHhCm2Y0KNZLpfEEOGikK9F1plxPkhs1nWF4W0kIVkqbYYK5isUwQj9Z/16DYh
	y3qTUJ7hx2cOVqdlWKtt24r8eEG2IQkCKoklIdFqexmfhipIx0ZIwQf3VacTZtzaL6wl
	sXNfdZo9xc+eFWOEfbZq2zqKRRRTKDb4caaYpdyunK9sUI5VZirTlalKszJJaVDGqWJU
	alW0KlIVrlKpFCpOxahAFecP/k3MlLwoTqGWhIKT7pycVjNSmm50BwZVDEwGvwLWJ3SX
	6cpiJmgclc6fuLXIhS3OzB8v3Y/JTB0afdtqZrh9e4weX76UCBo9V+n/P8m2CmpdU79i
	X/2Kc7NcbRZXi8XVRrHFt7m7XefrmWcy9Z1bISlMPja1ZV5ruyS9bb4Vljan75zFaeqr
	l9tdo54lqestzj6Y5Zrp7psltjn768V6l8Xr9Oyrc1VPHWFr07CtatdP2HJJnVVLturk
	dtfYmiqp6yRbUyVbUyVbdWKdbCsz07VwRgXwh0HDH4FsfjsYuQowAgRPUPxQkoEZwfP8
	WxAeHAwOsLSzYbIUT1/COPgPUMJBWE27zTuwB8PAAgOYD/+DRsyADyAAH8LfQQ+b4VG6
	u+Asfku7zOeYRnWKYA08DI8EO6ETyiicRR7iYRx8HlwZfD34PVRALxxFJcaiMXgIcmED
	hZ2wCyOZecE+0MEUuI129TXwBpwI9ge/oP6L4AxqMJcrCX5EDsZTiQM2wR44iGa0YAbO
	CZ6hch1hbII9wdpgN7U7T7VyYSqsJGsfo4CpmIk78RQ7EOwJ3ktjG026Bmil0AF3wQ7Y
	BXvlWvO40Xw89e+EGtLdC2/CWfiaNtx0rMDbmffYL9h/ciXczuBRwtFA9lrgEWSJFSs2
	4HzsxL24H/+I3zJ2xss62Pe4Tu4xwtYAG+ExeBleg+PwEZyDAfgBBpEjTBNwGq7E31G7
	vzNjmWZmFbOFOcGcZ8ewpzglt5lfz78Y5ILvBX8gzEmQASW00qeDG9ooLIAlcCv8Gtah
	ErZDH/yR0J6G0xiOaszFMViFM3EO/gpXwG9wN/4BT+In+Cl+TuhiGYGxMLlMN9lbw2xi
	9jL9zCFmgNWwXewq9jB7iv2Wi+eaucMUTvPZfJditKJGOT1wf+B0MDu4NbiT5iWBghXS
	IRsmIEcsdsA6mslNxNku2A3PwvPQD/3BS+iAo/A24foYzsNFmrHRFMyYj+OwDqcTwsXY
	gb/GHYRwDx4glC/ii/A+vo+XKAQgkQljspk5jJdZQWEn7GCOy/xEsmY2jc1ma9gZwa/Y
	vWwf+zWXwjVyy7iVXC+3g3uEH82P52fzjXwn/wB/gP8L/9/8ef6CwqjYoNit2K84rlQp
	C5Q7lAFMJiwmTIH98Ap53Ta2k/JWmIjraFZnwZvkvQPwJ7gE38NheBqNEGCl2UwNPgb+
	4EaazZfhBfZOKIXfML9lJgfL2GfYMMwPXqS+8mi+rgQQM9LTbKkpVkuy2SQkGUcb9Ik6
	bUJ8XGyMRj0qOioyIjxMpVTwHMsgZLkslS0mX2qLj0u1VFdnS3mLlwq8VxW0+ExUVDmy
	js8ktfOSakRNkWouuKamGKopDtdEtakUSrOzTC6LyXfMaTH5sXG6m9L3OC0ek29ATtfK
	6a1yOorSZjM1MLl07U6TD1tMLl9ld3uvq8WZnYWHROl9IjsLDgGIECF17IOJ3lW0ucJE
	qYbLp7c4Xb5EC6VJx6a4vPN9ddPdLqfBbPZkZ/lwYqtlng8sFb5RmUPNpXa0CabUu8l2
	dtZCH+GHzZHzLfM3+0WY1yKlvE1uH+v1+JgWyYYm06e1OH3aO87ofsxeSbm2XKX0MSmV
	3rbeSp/YsplIl7ItUs67hXI1M0zULbPe4/bhegIngZCxh0YRekyktCwy+cIsFZb23kUt
	xDnUufv1ot5laXF6fFDv7k8UE+VMdtYh3eoSM5FyKLs8u1ySJWbd6pD8bG2o/J3DktSt
	Pvo3kjX1w7ygZNsyiWD6TK1khLggrOOkW9s46G0dR/TR5UEa5ULCM9HHkCuxKT4+ZZLX
	1zNjCIa33TkEbpGzPyxRLz+XKjxUv6VXXUwTSPXVFlPvN0Azaxn4cmSJd6hEkaL+BiSl
	NP/DLuRD75V0t/T8TKFHUrvO0i5NX7c81ZS36FxXFVBeem5l0wtnVo0fwurcfYj3evwY
	XO8Hp/EQPWDYuTeROlNyuIVOMkeZrCwqyDBTihBUkqFKyTNMvabeSfN7TZWmdnIpLkWW
	pGjr9eQSYTPcRAvMdJt9oscwnGzzeIqpnxypH2pC1Xs91MOioR5IykW5g1QpN6uGRpVa
	557u9vU4DT7R6SHSyYkP17l9h8l/PR6qlTeMlBCvWqgbwjyGMOdlkD4/1Au91vRQF57e
	XqnPGW6L2Xe4t9fQK626UJ7ekK8tEIcK/CBVkRj2Y08dtSVhMRtkys0WM8HySJyOJQe+
	4kD0Wv/zDBcO46aWRYS2UGbYfoMYHnc9DDuui+HiYaQjGC4hzMUSw6W/HMPjRzA84ecZ
	LhvGTSBFQlsmM1x+gxiuuB6GJ14Xw85hpCMYdhFmp8Rw5S/HcNUIhqt/nuFJw7gJ5GRC
	O0lmuOYGMTzlehiuvS6Gpw4jHcHwNMI8VWK47pdjePoIhut/nuEZw7gJ5ExCO0NmuOEG
	MTzrehiefV0Mu4eRjmDYQ5jdEsONwwyLBh9cvQ/3XLPtwg3fmOdcRTn/GuxkHAAkm7hb
	oIT7B5SRzCVZIUXWCGsor6f3qtAZEB3kgAL8lDfBbOnD+wZc0if8v3uxckOOvo3ooOma
	TpRyPnRQBfQwD10FUABP4WTczYxj+lkVW88eIAVDX4RA7+9H6HRPCRNEM68w0nsxpzSy
	EM5zRpZl9GEKpREhURW2x7y4lA4Npl4orR0snar+trRWPVgKZaWDpVIckzdWY9bYKO7k
	nvBfPsYf+WGCn6u/9Lxkn4Gm4Pv8Sv48fVsJ0CWmzw6/X3m/ip3DeAxu4wLuNtzE/z6u
	nzsY/ir3WsQJ5sO4j3SnDd/p1Fo/RogWvUqljywXWDamXB8mJNi1KruQpNSbR9mTEk3m
	B817Z8m4agcIVa3GMfDuQC6UDZSVDsQ4ctUDY/KwGZpj7EVmkzZBa061pVqSmfi4hLH5
	9iK7WQFmky1Vg01/3Y8J2PXcXGXgzaTcmU8+c+TYw4835Ao4Ji1wMBAMHDlwgNnKzX77
	wIVNvYuKWgJffffdxUWO5V8F3nnzGLaxemKwJHiKG8fNhWj6ri2G+8RpJUxh0QrchNwH
	SZj6r08zPrNER/H0ZR2rz6TvEy41JzUnQyrgDBHJhoSsYkGZER6RlR9RHFsLtTnFhRkT
	UvWl+lpDtqq2MLGk9CVMBDNU4/MwNNwL8oA/0TiOnTkjj5gm4phDE6N1xDgcKEkpZoYI
	wGgchQqlQh55kd1WZC8qLCAqlAqlmdLmfPpK0sRpk1Abb85BG9W0JKcWFhBHscwpgz1P
	bLRVTC9ueojdOy15fHNjW0ZSeGAgrGoZxu7bvJlhR48O/DkqnC2pbeq6/78eaniyk4nR
	xIdFqrW2+knli+87Hz5Kb584Nj+l7L6mrVVVfwpEFkwelxaVYS5OEbMLf//QG41j4lE6
	5CVfKSMe2/jDdBZrhGli1tlIJNRGRs2C1qpWKsKN1vCIeFYfKygE1sbpBX1RVGKSsMNc
	7brKDQYvfKKJcUhuQD+NQ0OUjMmDZkjQkouaC6PRkgyFahgbQyxIHiHzwtyxMw/NgXPj
	d3X9Z+AS4vsHV7dNqF91620ruKbZtYzqB3G7142FX6MWxcvL99/3+qyCl7dsf4HmPjd4
	kiumuSd3gmR4VpxUqdoQtx0fDOcUGMYr1Ly+hq9UTzLdjetHbRDC2QRWG5sQq61WTUmY
	op2kb0po0jbqT+KH3OfGz0wXTerJWKneyK9Vc4wfHxDHToueG700mo2ONiisyWalNibL
	EJHAMslskXZlclJLZE8kE6m3MkL0A0mJFitRMTVExeAntCSaaU18MpAbouNYaFk0DxIb
	y5pxWTPQ/OegpSiBFkeC0ky3oYUhOQBo1FCC+FZHNL6oXDln44kqMTaCGUxQeEtmuO1J
	WrRENG65/FbgCApn4tiuOxctu/XcgiXenpp7dlek5xvyvPMfwUjMQQMdX9PFQkWggruJ
	eIqiU6M8aBTVvUnkqLw1L1cZo0iJsqb7sUw0jTZl6UblMUKMkGLLy4rV549eZ8gOK8pK
	HJN/1TSH3F/joKkm7x88VjbgKKPRaWiisdl6xbvj42hEMTS4QrMmTqkwJ1ttVwY5XloP
	tBkUFsSMtTMv965dss2RZCreFjG+XcT4qjsCT70d+C4aiyINOR07C5LTcxs2vnPp61Nz
	vtj+5EOP31OzZO7kXnZ5YuYtv7v07fFf+Xc/kZ9gu7liV2WlpRxtl/+FNfI2TWMOnuB2
	cW0QAVo6PasV07QsqiI3Rm5Us9oo3agFUSxv1cUpI6zRETqdiinS6vWqIk1iot6P3fuG
	p1Je6zS4oa2Nxrgcli8bGqbsvVbayqCwQLrHI3Pu7rtXrdqwYRWTE/gy8BmFLzEOEynE
	DR5/o3/37r6+3bv7FwSexYZ/fomNgae+ZER6sq2hBXiJa4JYmHwIWKzax4yKUvixSkyM
	VUYpIsNNTB4jMmy8VqtnoiNskYlx8X6cv89ct2DI4Y6+Sweozk9rm6GMVp7G8S7tv9Ki
	IxejrSZeWnzxtN2MZT6KiDVkRD1XYg58jOqKMXU9XBNi4CTLdJatHbzIVbzSkTaRXIYw
	0XOYm01nqgp4WJxmU8xUzFc8qXiBznAUqGaqSHUH08XeximqcCNs4LfzT/MH+dfZz/Ai
	hrEmjrPF8LyCMSHaYqgLBcPT3yQKlglXMQygHyP7WZ5GGHmAS1S2tdAwErunqi/oagcd
	Dvolql/V0TOurLS0NMbBb8jJXKWume6+w3N0Q45uOC352zJpNZmlEMumYH7gtsDxwAoU
	/8FVXHqFq7h8kvY1+QpKJ4o/dYVTIQupdCJaBdV0qF9Pp5keuSJCzNA7h4JOeKGh0jVx
	0rTM6rbF3W1dC1u92RVLF8+XeLpy5VGilOIUivRvFv23BXAnxS0Ud1F8juJLwaGL0jCc
	RjBdk5en4Cq99xo9GR7RXh7bVfVvvka/9Jr88mvy3VL+/wCv+6VICmVuZHN0cmVhbQpl
	bmRvYmoKMjUgMCBvYmoKNDQzMgplbmRvYmoKMjYgMCBvYmoKPDwgL1R5cGUgL0ZvbnRE
	ZXNjcmlwdG9yIC9Bc2NlbnQgNzcwIC9DYXBIZWlnaHQgNzIwIC9EZXNjZW50IC0yMzAg
	L0ZsYWdzIDMyCi9Gb250QkJveCBbLTEwMTggLTQ4MSAxNDM2IDExNTldIC9Gb250TmFt
	ZSAvVUZFQ0lPK0hlbHZldGljYS1Cb2xkIC9JdGFsaWNBbmdsZQowIC9TdGVtViAwIC9N
	YXhXaWR0aCAxNTAwIC9YSGVpZ2h0IDY0NCAvRm9udEZpbGUyIDI0IDAgUiA+PgplbmRv
	YmoKMjcgMCBvYmoKWyAyNzggMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAw
	IDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMAo3MjIgMCAwIDAgMCAwIDAg
	MCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDU1NiAw
	IDAgNjExCjU1NiAwIDYxMSAwIDAgMCAwIDAgMCAwIDYxMSAwIDAgMzg5IDAgMCAwIDU1
	NiBdCmVuZG9iagoxMCAwIG9iago8PCAvVHlwZSAvRm9udCAvU3VidHlwZSAvVHJ1ZVR5
	cGUgL0Jhc2VGb250IC9VRkVDSU8rSGVsdmV0aWNhLUJvbGQgL0ZvbnREZXNjcmlwdG9y
	CjI2IDAgUiAvV2lkdGhzIDI3IDAgUiAvRmlyc3RDaGFyIDMyIC9MYXN0Q2hhciAxMTgg
	L0VuY29kaW5nIC9NYWNSb21hbkVuY29kaW5nCj4+CmVuZG9iagoyOCAwIG9iagooTWFj
	IE9TIFggMTAuNi44IFF1YXJ0eiBQREZDb250ZXh0KQplbmRvYmoKMjkgMCBvYmoKKEQ6
	MjAxMTExMDQxMDA1NDZaMDAnMDAnKQplbmRvYmoKMSAwIG9iago8PCAvUHJvZHVjZXIg
	MjggMCBSIC9DcmVhdGlvbkRhdGUgMjkgMCBSIC9Nb2REYXRlIDI5IDAgUiA+PgplbmRv
	YmoKeHJlZgowIDMwCjAwMDAwMDAwMDAgNjU1MzUgZiAKMDAwMDAyMjQxOSAwMDAwMCBu
	IAowMDAwMDA2NTA5IDAwMDAwIG4gCjAwMDAwMDQzNzIgMDAwMDAgbiAKMDAwMDAwNjM2
	MCAwMDAwMCBuIAowMDAwMDAwMDIyIDAwMDAwIG4gCjAwMDAwMDQzNTIgMDAwMDAgbiAK
	MDAwMDAwNDQ3NyAwMDAwMCBuIAowMDAwMDA2MzI0IDAwMDAwIG4gCjAwMDAwMDU0Mjgg
	MDAwMDAgbiAKMDAwMDAyMjE0NSAwMDAwMCBuIAowMDAwMDE2OTg0IDAwMDAwIG4gCjAw
	MDAwMDQ2MDAgMDAwMDAgbiAKMDAwMDAwNTQwOCAwMDAwMCBuIAowMDAwMDA1NDY0IDAw
	MDAwIG4gCjAwMDAwMDYzMDQgMDAwMDAgbiAKMDAwMDAwNjQ0MyAwMDAwMCBuIAowMDAw
	MDA2NjcyIDAwMDAwIG4gCjAwMDAwMDY1NTcgMDAwMDAgbiAKMDAwMDAwNjY1MCAwMDAw
	MCBuIAowMDAwMDA2NzY1IDAwMDAwIG4gCjAwMDAwMTYyNTggMDAwMDAgbiAKMDAwMDAx
	NjI3OSAwMDAwMCBuIAowMDAwMDE2NTA0IDAwMDAwIG4gCjAwMDAwMTcxNTkgMDAwMDAg
	biAKMDAwMDAyMTY4MSAwMDAwMCBuIAowMDAwMDIxNzAyIDAwMDAwIG4gCjAwMDAwMjE5
	MzMgMDAwMDAgbiAKMDAwMDAyMjMyNSAwMDAwMCBuIAowMDAwMDIyMzc3IDAwMDAwIG4g
	CnRyYWlsZXIKPDwgL1NpemUgMzAgL1Jvb3QgMTYgMCBSIC9JbmZvIDEgMCBSIC9JRCBb
	IDxmYjZmODVmMDAxOWQ5MDNhMmI4ZDE4ZWM1NWY4M2ZiYT4KPGZiNmY4NWYwMDE5ZDkw
	M2EyYjhkMThlYzU1ZjgzZmJhPiBdID4+CnN0YXJ0eHJlZgoyMjQ5NAolJUVPRgoxIDAg
	b2JqCjw8L0F1dGhvciAoTWFyYyBIb2ZmbWFubikvQ3JlYXRpb25EYXRlIChEOjIwMTEx
	MTA0MDk0ODAwWikvQ3JlYXRvciAoT21uaUdyYWZmbGUgUHJvZmVzc2lvbmFsIDUuMy41
	KS9Nb2REYXRlIChEOjIwMTExMTA0MTAwNDAwWikvUHJvZHVjZXIgMjggMCBSID4+CmVu
	ZG9iagp4cmVmCjEgMQowMDAwMDIzMjUyIDAwMDAwIG4gCnRyYWlsZXIKPDwvSUQgWzxm
	YjZmODVmMDAxOWQ5MDNhMmI4ZDE4ZWM1NWY4M2ZiYT4gPGZiNmY4NWYwMDE5ZDkwM2Ey
	YjhkMThlYzU1ZjgzZmJhPl0gL0luZm8gMSAwIFIgL1ByZXYgMjI0OTQgL1Jvb3QgMTYg
	MCBSIC9TaXplIDMwPj4Kc3RhcnR4cmVmCjIzNDE0CiUlRU9GCg==
	</data>
	<key>QuickLookThumbnail</key>
	<data>
	TU0AKgAABr6AP+BP8AQWDQeEQmFQuGQ2GNeIAB9RMAPWLAB8RkAPyOAAFR8AAGRACBxu
	OgKUSSBAADy0ACGYAB6TMACObQ6cTmdTueT2fQyBwSf0OiQaINeVQR00sAPmnSGRhGpT
	KaSgBRiNAStABxV2azcTWGKxcSWWi2e0Wm1Qig2u3Qxz3GTPyxvUAPO8AAJXsAAa/Tl0
	YEAAvCAAG4eEAjFAAK4234/IZGDW3JY975cAPDNACtAQABDQAAC6Oev7TAB36mJRSpBE
	AYoEZXZbO0ZTaT197kAO7eQjQBCWS7I0587ve6MCgAKcvb83nQvbc+FsTqABy9cAE7tA
	AE93pSSCvx7gB1eUAJ9QKMAG83GvX4uiAGrgAB7Hv/ek0KdNr+ADcn2jyQJaA6mqfAaE
	HZBLlOYeMGsGwrOqoegANaxjHLccxynEzJRCE7h6m6lgBoKfp/IKAb5oXEqCpGAEVtFE
	aoIKeQQCwAATjAU78Oc6KcG5H4ANNExwSI4MCHlJDuO8xoKvI8zYLrJQEuMdwACbK7rO
	wD8trebBoGMAAPlyHYAGydSCnofSCngeyCgoBaCnZNqmwAxgGIKd7xr7GIGQIAAdBAgp
	zgMFIAA4NxrR25seocwJ0NXNUIoOic1P/B84Mu8bkM4rdJooAdQL0vjCTgt1LHCXpIo8
	eptIZIUXH6frRNJS0IpEAKEVuAABBIJ4AAuFwlUU29GKLSgAHXZIAAxZj6VDWNZKWdMF
	govq/2GnDiAAdtuAAC1v04z1jwSdlqWsA1sWHYqeo4ujyzOCd4yMhiMnxKll2aq10oNd
	snXheUDoXet7yZWbk32591pwc2GAA6hiAAJ+JXmniLLsUmMAAM+N2xaAAEvkAADNkdzt
	w3RO5QAAx5Xg2ENpRx/48ni4nOAAGZvAIFAAe2eK+EdwgAcmhWdEdX6EcgAA7pSc1eku
	lA7SAAUdoCcVevB5gAB+tVhWTuym5dqnoeZ4NQY5Kvoe51gAeJ5HjKS+gLdCcHof87g0
	II4ZsB9q5chxq7+f4VcEhD+Vbq9dpTBu3CBxiEYHhhzAAEXJgAo4ABTzCEGzzYASQeWg
	QPwQVIRIhwAAEHUABv5qgAFHXaIABv9kqFcX1UER9vfAMdJIvUUCahckwAAVmoNIAGKb
	6Cg2B8WVwhADRieV7TsgoIym6waEppIgPdvqGmp8B/hP8fadj2dN8OC/1VECXy24dubZ
	xr3Koj8YTgBTIAHH/ec/LCK3wLPlGnAMAAL4DNSMEv0DMCzUGqNSO9CSv31sWck5Rjw3
	oMOtdeOQagwTBi2CWAAdg7y7D5TqSkwyfh8qyToQUB71wHgNNiOoHyOgRg3Cg94hrFh/
	m8SqvooaukoIVIPA9/BmC0K6MOA1+KdyDs0KSWlXS8QJslaMOEbh9B/L2M02RTYDAFp3
	HoPVCa/QIAPOAAIA6dwKgZA87CHR0CSxxjoW5ZLakIxEIU+8hEVI6k6YVH+QRCSSjWkM
	ACBYGX2E9juttbrrgUSDIVIGSUkmpjGkwAAKUmyfD6bcf4uwo5RAACpKVt5OQEPtPofY
	78lJKm0G7LFkruXFRRQO/Mhb+QHS7Qsk0bowBOF9F+Ghzo91ZPWIKONsjSTgIuRNCIuy
	v4mP4AFEwBwYRkmMA8oVHkc5Xn4liiF3xCBsTlfM8kvy6FXg9nYe82K/YoTjG6LEPjWR
	oiFAANFmqy5pj5LoTI4przPIwPDM8F4GiCjmCILd04MgkMJm9N878tYoIRI+zog7A0oR
	yII2BeY8h2jlWQMV4QCQAp6IUPo3QBjSElHnGMlgFQSyIB6e6jai6I0SNo/mWq+kKqbI
	O0dQwHAOHPcgAADVSSEM8TbLVCLWnmTpNpK6nRQ2By1IPVCdxORt1dAACWsBz0fxZLKC
	QnMFHPMtq1UAtdVKqkNX6JWuQAAr11ay1uXBO38iTr4AAO1fzniKsFX6wBQyZoTHDYkA
	Ax7GAADJY8tNbl0ulZaronhJV+1pnHEtoLQySu5LRD5pLS6tNTYHVItFaYASLHaOpyI7
	hXhhJYPZDakifgBAQA4/MlaxwVBE6pv4ALKLaQOzdO5e32gsuU7B/Y47RtQq6Ntn0UXZ
	PJc2NlxBV1SAAB3d0hDuSuobA9eO4DrIjKWsPUipV6VdU9JSC2+BCLxxvGmL4UgAAWDP
	C7Yt0wABzufOVE4BLBxzyfhQn0goP7f27kldV2FtiCv5ryQ2pjuomgAqPhI7xB8NPXId
	BRgpv4RIKkbVotDA5d26o8PEd7ahximv2+1R5OE2F0AgAqgZBwDW6ICAAAAOAQAAAwAA
	AAEAPwAAAQEAAwAAAAEAHAAAAQIAAwAAAAQAAAdsAQMAAwAAAAEABQAAAQYAAwAAAAEA
	AgAAAREABAAAAAEAAAAIARIAAwAAAAEAAQAAARUAAwAAAAEABAAAARYAAwAAAAEAHAAA
	ARcABAAAAAEAAAa1ARwAAwAAAAEAAQAAAT0AAwAAAAEAAgAAAVIAAwAAAAEAAQAAAVMA
	AwAAAAQAAAd0AAAAAAAIAAgACAAIAAEAAQABAAE=
	</data>
	<key>ReadOnly</key>
	<string>NO</string>
	<key>RowAlign</key>
	<integer>1</integer>
	<key>RowSpacing</key>
	<real>36</real>
	<key>SheetTitle</key>
	<string>Canvas 1</string>
	<key>SmartAlignmentGuidesActive</key>
	<string>YES</string>
	<key>SmartDistanceGuidesActive</key>
	<string>YES</string>
	<key>UniqueID</key>
	<integer>1</integer>
	<key>UseEntirePage</key>
	<false/>
	<key>VPages</key>
	<integer>1</integer>
	<key>WindowInfo</key>
	<dict>
		<key>CurrentSheet</key>
		<integer>0</integer>
		<key>ExpandedCanvases</key>
		<array>
			<dict>
				<key>name</key>
				<string>Canvas 1</string>
			</dict>
		</array>
		<key>Frame</key>
		<string>{{20, 4}, {1064, 871}}</string>
		<key>ListView</key>
		<false/>
		<key>OutlineWidth</key>
		<integer>142</integer>
		<key>RightSidebar</key>
		<true/>
		<key>ShowRuler</key>
		<true/>
		<key>Sidebar</key>
		<true/>
		<key>SidebarWidth</key>
		<integer>120</integer>
		<key>VisibleRegion</key>
		<string>{{0, 0}, {772, 702}}</string>
		<key>Zoom</key>
		<real>1</real>
		<key>ZoomValues</key>
		<array>
			<array>
				<string>Canvas 1</string>
				<real>1</real>
				<real>1</real>
			</array>
		</array>
	</dict>
	<key>saveQuickLookFiles</key>
	<string>YES</string>
</dict>
</plist>
