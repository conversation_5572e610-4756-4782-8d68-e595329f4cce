<?xml version="1.0" encoding="UTF-8"?>
<!--
   Copyright (c) 2009, 2025 Mountainminds GmbH & Co. KG and Contributors
   This program and the accompanying materials are made available under
   the terms of the Eclipse Public License 2.0 which is available at
   http://www.eclipse.org/legal/epl-2.0

   SPDX-License-Identifier: EPL-2.0

   Contributors:
      <PERSON><PERSON><PERSON> - initial API and implementation
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.jacoco</groupId>
    <artifactId>org.jacoco.build</artifactId>
    <version>0.8.14-SNAPSHOT</version>
    <relativePath>../org.jacoco.build</relativePath>
  </parent>

  <artifactId>org.jacoco.ant</artifactId>

  <name>JaCoCo :: Ant</name>
  <description>JaCoCo Ant Tasks</description>

  <dependencies>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.core</artifactId>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.report</artifactId>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.agent</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant</artifactId>
      <scope>provided</scope>
    </dependency>
  </dependencies>

  <build>
    <sourceDirectory>src</sourceDirectory>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <shadedArtifactAttached>true</shadedArtifactAttached>
              <shadedClassifierName>nodeps</shadedClassifierName>
              <minimizeJar>true</minimizeJar>
              <relocations>
                <relocation>
                  <pattern>org.objectweb.asm</pattern>
                  <shadedPattern>org.jacoco.asm</shadedPattern>
                </relocation>
              </relocations>
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <excludes>
                    <exclude>META-INF/MANIFEST.MF</exclude>
                  </excludes>
                </filter>
                <filter>
                  <artifact>org.jacoco:org.jacoco.core</artifact>
                  <excludes>
                    <exclude>about.html</exclude>
                  </excludes>
                </filter>
                <filter>
                  <artifact>org.jacoco:org.jacoco.report</artifact>
                  <excludes>
                    <exclude>about.html</exclude>
                  </excludes>
                </filter>
                <filter>
                  <artifact>org.jacoco:org.jacoco.agent</artifact>
                  <excludes>
                    <exclude>about.html</exclude>
                  </excludes>
                </filter>
                <filter>
                  <artifact>org.ow2.asm:*</artifact>
                  <excludes>
                    <exclude>module-info.class</exclude>
                  </excludes>
                </filter>
              </filters>
              <transformers>
                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                  <manifestEntries>
                    <Implementation-Title>${project.description}</Implementation-Title>
                    <Implementation-Vendor>${project.organization.name}</Implementation-Vendor>
                    <Implementation-Version>${project.version}</Implementation-Version>
                  </manifestEntries>
                </transformer>
              </transformers>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <executions>
          <execution>
            <!--
            None of resource tranformers from maven-shade-plugin
            (including combination of DontIncludeResourceTransformer and ManifestResourceTransformer)
            does not allow us to leave only desired entries and remove others from META-INF/MANIFEST.MF
            So we use goal "bundle" instead of "manifest".
            This introduces some redundant operations, but their cost is negligible.
            -->
            <phase>package</phase>
            <goals>
              <goal>bundle</goal>
            </goals>
            <configuration>
              <excludeDependencies>true</excludeDependencies>
              <instructions>
                <Require-Bundle>org.apache.ant;bundle-version="[1.7.0,2.0.0)"</Require-Bundle>
              </instructions>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
