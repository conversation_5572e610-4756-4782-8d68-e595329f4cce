<?xml version="1.0" encoding="UTF-8"?>
<!--
   Copyright (c) 2009, 2025 Mountainminds GmbH & Co. KG and Contributors
   This program and the accompanying materials are made available under
   the terms of the Eclipse Public License 2.0 which is available at
   http://www.eclipse.org/legal/epl-2.0

   SPDX-License-Identifier: EPL-2.0

   Contributors:
      <PERSON><PERSON><PERSON> - initial API and implementation
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.jacoco</groupId>
    <artifactId>org.jacoco.build</artifactId>
    <version>0.8.14-SNAPSHOT</version>
    <relativePath>../org.jacoco.build</relativePath>
  </parent>

  <artifactId>org.jacoco.agent.rt</artifactId>
  <!-- do not set packaging to eclipse-plugin, because otherwise maven-shade-plugin will not work -->

  <name>JaCoCo :: Agent RT</name>
  <description>JaCoCo Java Agent</description>

  <properties>
    <maven.deploy.skip>true</maven.deploy.skip>
    <maven.javadoc.skip>true</maven.javadoc.skip>
  </properties>

  <dependencies>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.core</artifactId>
    </dependency>
  </dependencies>

  <build>
    <sourceDirectory>src</sourceDirectory>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <shadedArtifactAttached>true</shadedArtifactAttached>
              <shadedClassifierName>all</shadedClassifierName>
              <minimizeJar>true</minimizeJar>
              <relocations>
                <relocation>
                  <pattern>org.jacoco.agent.rt.internal</pattern>
                  <shadedPattern>${jacoco.runtime.package.name}</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.jacoco.core</pattern>
                  <shadedPattern>${jacoco.runtime.package.name}.core</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.objectweb.asm</pattern>
                  <shadedPattern>${jacoco.runtime.package.name}.asm</shadedPattern>
                </relocation>
              </relocations>
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <excludes>
                    <exclude>META-INF/MANIFEST.MF</exclude>
                  </excludes>
                </filter>
                <filter>
                  <artifact>org.jacoco:org.jacoco.core</artifact>
                  <excludes>
                    <exclude>about.html</exclude>
                  </excludes>
                </filter>
                <filter>
                  <artifact>org.ow2.asm:*</artifact>
                  <excludes>
                    <exclude>module-info.class</exclude>
                  </excludes>
                </filter>
              </filters>
              <transformers>
                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                  <manifestEntries>
                    <Premain-Class>${jacoco.runtime.package.name}.PreMain</Premain-Class>
                    <Automatic-Module-Name>${project.artifactId}</Automatic-Module-Name>
                    <Implementation-Title>${project.description}</Implementation-Title>
                    <Implementation-Vendor>${project.organization.name}</Implementation-Vendor>
                    <Implementation-Version>${project.version}</Implementation-Version>
                  </manifestEntries>
                </transformer>
              </transformers>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
