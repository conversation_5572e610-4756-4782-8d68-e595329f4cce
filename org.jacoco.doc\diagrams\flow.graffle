<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ApplicationVersion</key>
	<array>
		<string>com.omnigroup.OmniGrafflePro</string>
		<string>138.33.0.157554</string>
	</array>
	<key>CreationDate</key>
	<string>2011-11-03 11:56:31 +0100</string>
	<key>Creator</key>
	<string><PERSON></string>
	<key>GraphDocumentVersion</key>
	<integer>8</integer>
	<key>GuidesLocked</key>
	<string>NO</string>
	<key>GuidesVisible</key>
	<string>YES</string>
	<key>ImageCounter</key>
	<integer>1</integer>
	<key>LinksVisible</key>
	<string>NO</string>
	<key>MagnetsVisible</key>
	<string>NO</string>
	<key>MasterSheets</key>
	<array/>
	<key>ModificationDate</key>
	<string>2012-06-21 10:56:09 +0200</string>
	<key>Modifier</key>
	<string><PERSON></string>
	<key>NotesVisible</key>
	<string>NO</string>
	<key>OriginVisible</key>
	<string>NO</string>
	<key>PageBreaks</key>
	<string>YES</string>
	<key>PrintInfo</key>
	<dict>
		<key>NSBottomMargin</key>
		<array>
			<string>float</string>
			<string>41</string>
		</array>
		<key>NSLeftMargin</key>
		<array>
			<string>float</string>
			<string>18</string>
		</array>
		<key>NSPaperSize</key>
		<array>
			<string>coded</string>
			<string>BAtzdHJlYW10eXBlZIHoA4QBQISEhAdOU1ZhbHVlAISECE5TT2JqZWN0AIWEASqEhAx7X05TU2l6ZT1mZn2WgVMCgUoDhg==</string>
		</array>
		<key>NSRightMargin</key>
		<array>
			<string>float</string>
			<string>18</string>
		</array>
		<key>NSTopMargin</key>
		<array>
			<string>float</string>
			<string>18</string>
		</array>
	</dict>
	<key>QuickLookPreview</key>
	<data>
	JVBERi0xLjMKJcTl8uXrp/Og0MTGCjUgMCBvYmoKPDwgL0xlbmd0aCA2IDAgUiAvRmls
	dGVyIC9GbGF0ZURlY29kZSA+PgpzdHJlYW0KeAHNWctuJbcR3fMruJQWavPN5jIZ2IZn
	EcceBVkEWQkRBhNNAHsW/v2cw0eRV7dbGgWGEQiD6apL1uOQLFYVf9E/6V+0wV+MRefd
	61//pf+u/6O/effF6ocv2ta/Lw/6zmxR898y8FF7/RsGvNdWfWpT3n0AXYfqD+8gGgT+
	7vgfpT981tYm7UzZvN+TBunN5ifjSdtgt+KKkjHC0s4HvVAxNCFgeUshC8PlhaGe9Ac4
	OgyDN9bZLevo9i0lG2hGnRBd2iIZENkYsC43xsc2SS2THuHVe/z7dOxo1SFGDR0upi3Q
	9yfVdLjoNlcZuuuYnnzuhgPhEy270T7Fzebs1GdNKvstO3hAl0EH47e0w8XdbRZU2dyO
	X3PeyqQ+Xox8VC/5VXVg/dyQKRqrTKGqTJ85Epa1BXjBD6ujoRu2UC72nbFbdH44YnXK
	cdtzppcenqTdbgHD6csOXxYammU4hJ0vk8J+rKryZqGqS56aq+BJVrnWjMF0yWwmhZRy
	TgqfIcbkQyLXOxMLfDH63xqc7D1wAPLZaOc2TMBvOGuPWNh+hLDAPCsGB0cdHRxIcfn5
	fNqAs+p4Vo3Gzl5mwrMcQt3hdPTP99jBVcEd/r+z9QjuWQd9/1l/853dDGy5f9T/0Dc/
	3MIFp2/+cstzj48P8qVv4Sg49lb/U9+/19/eX6BA189QiDoF7LpSgIITFFRFYTFbIkWI
	CBYOsPl1PFF7xesUq2/h9/farV7PGNkCHtB3xdBBAG8tthM2wSSfQMatpJIUf/Wu9N9M
	2ko2aeVgPSeZCoIZ9w4l9EmpAJjonBoysf/3NkqUCmexS3hP2u1pM27HqvSZyuFIVeuH
	dIwRTrcBsq54V5xV0vW8bBieZBJcb/6LQYRnIFmxekBkHIwO7eO602XLzEGMToJEjYcD
	KQVKcMzEoISFg1OzYL/HflYX7LHG2Mg5Q0pfz4JTVk+0aBTOgr3wFuyFN+BQIn1wtNiw
	YC+861GDo2TMOo/xcozQ4v2EHgdT9jB34QSeuLU4fhZwvAEe3P2plHaxXoedsBljS/Q6
	qMuwc/PXW33/6Q3xxCH2H4XV84DCGW8LoR7H8v8phjpcH28LopzxP0RROv4HhdGj0N93
	0r7mBJ0Hu5AY8Bbpad0Iw+3yRBDuAwMihd+Y3HmDBKEULCVSMiZ33rgLhvp4Pek8a2AW
	KzpSacYsahQMTMaYC03DaGhflC2zX0qQeJGJxlKzvZomdR7uSSRLRZKlzt6RMvnNIFAJ
	BFExTQKLYaCjwAuAvFRW29bJ51Ashu0wApdTTaM64MMuR8mirdtKCwQJmdwzxZlWvZRQ
	JB0T84PDtEqd55rIpCyTkMtU7JWkIgJLFgs9qUBehCV2+p7FxJJVZRQQJsarrOrm+x/v
	Z4Bb9jzum2eiF3n75lyCk9fifpzCDsFiOorbaOagqNxsv4uX7KvloIs5cpUi+3LhIvNi
	YH0NpFFRXQd+57e9oPZjuqmWdPPmHJZzYfAkhIR99fwWQfL6p59H1vp9T2O/hYqZrB45
	y3DnkKzhFmexUklk7Nyj3eNaDS8hBnuPhWo9WgXf2Pp1k3v55uZOaow6P0MtnEA/5NWk
	0RRUuk25Nyhr23eNUXMUzTpceHLXheelN1d+bvl2Tdbi/aS0rNclt8CcdJx8K5b3tcon
	cFIgtz3wxoOiTg/KgeyXTwrMQjlz89pJuQYMRTMyI14xa+HxUl4BhFCUvrFSoUOBubff
	R1RZHLpbD8xan50cmFekzRNzXe69eGIUO0SywBIePHYrIsRoueDMRF62wkDAx4VySaIs
	qEUMf2DimxAOMA4yMHFluEvG0RFUHqVtEPx4DtFHISNWQKvgyijoDQFhMHAin0/CscRF
	cdbCacPFtKHDoSAT45utuPHhDTKSoWOZRPMBIPtnrNAdEuSIsgvSXPsCBqwkWAPyB+/R
	jttROYKhKpETaxKyM5zppSDJlGoVuMwfF2KVPYiqVMllOeqPEuYF6nY3xVViqGL1gN8G
	CUKtxPxlDOSi1indBxCtyqreDfdbaWFVg4J9kPMLm3N4YzfI+LVARpLYOCR1rOUqZM7k
	DpmzWBuEdU7hKJtYvWHKgHzE/bocQjTIhFwgEx59FHGN6KoaEqKZkK0EZl0OhLENsuHD
	hIzeDfcHZA2KfioOWz81VvaEhffLs97PVxVhI6++2LoIjaF3NuoX1yG0ncofUFfr4Gc/
	A0RvZejAhqp0MUDWBoaX+TrY2cBQQnAdoHT8RphiXebJQzk7xTVibToo0Xzx2wXBBYMQ
	GEsF/JKexNAG72CJope9Ku5QnHYiEG16F6J+TagqCagQw/rBIG4+9s4DvmrXoW5Z/pBa
	w2FC5VNtOLBrwfZvI1SDapDVk2688OibiKvEUNUQGJrZwJlmXBBjYG8kwJfugywM3eai
	8f8HpC3j+9XmAVbxD+kbsEmOXjKaX2tu8/L9jhsE7eh1/HEuNK9KVGHa72gWBykaLq53
	HJtSUEEc9F8lg2XmWtutfxs57c+3qrVk0ZF9LaeF/v40UNNUkDmg0Y2dMW5UVdvGTN5a
	3YwXAbPhkkagNTuInQQ2Ls0cRC3a5sCe2OIB6DBNqCDg5aGeBMgUEyhTiCbT84niax4L
	CG1AHx6d0SYXJOO6Zw5O3yr2EfEk2ZrOQy+vytTL3oXqmttY+P1inq6qYgQ8auoYid4K
	klAdJRnbkgBcYxVpxH+cScWcCfcQRdmAxw104z0qid0Sm8nBolkLF9osDEEuUer9Bgz2
	UDDY583szsNAQBiJc+fg9OGRy6O7LLMcOwjOIvQiG6qSF07X3meJheyqDMl4wNo99HvR
	LpxhYffrob5v1Hv98EljeFTPfb7AIbDq2Xm1DxzwaIbdiKtUZgXkXiiQaFvHIQTkUXhE
	mtYKZ+AQZNbwWg3JwKojI9plFiMTLFxwCHgstAn6p/bOmRbKLC7/V5VrKLvlslsKr/Pg
	hAkMtMvY1wITHwV7oD14IeF1/rvFpZlD9J2PTNDj8MBmQL0Hdud2bLm844nS803DFS8c
	hVdR7F3s5jmNb38W5/iB75nc+wFRBGtjsJ/x/oi9j2R5ctCzwX0XIUnmZTwzYvfjfhvS
	J2dYwNSJ88TMBxGuPJrbPuKoTgOE89w7HoCjsDj8gV20mQ/hCwzsQyAlXDiUi2M+psEs
	gIZDuMCg+NDrC+wSGCZnuiPzFqe79MGBh92CCUM3c8IA9CA0APUJg3AEhjYNmQn3/0//
	BcRFiNsKZW5kc3RyZWFtCmVuZG9iago2IDAgb2JqCjI0MjYKZW5kb2JqCjMgMCBvYmoK
	PDwgL1R5cGUgL1BhZ2UgL1BhcmVudCA0IDAgUiAvUmVzb3VyY2VzIDcgMCBSIC9Db250
	ZW50cyA1IDAgUiAvTWVkaWFCb3ggWzAgMCA1NTkgNzgzXQo+PgplbmRvYmoKNyAwIG9i
	ago8PCAvUHJvY1NldCBbIC9QREYgL1RleHQgXSAvQ29sb3JTcGFjZSA8PCAvQ3MxIDgg
	MCBSIC9DczIgOSAwIFIgPj4gL0ZvbnQgPDwKL0YxLjAgMTAgMCBSID4+ID4+CmVuZG9i
	agoxMSAwIG9iago8PCAvTGVuZ3RoIDEyIDAgUiAvTiAzIC9BbHRlcm5hdGUgL0Rldmlj
	ZVJHQiAvRmlsdGVyIC9GbGF0ZURlY29kZSA+PgpzdHJlYW0KeAGFVM9rE0EU/jZuqdAi
	CFprDrJ4kCJJWatoRdQ2/RFiawzbH7ZFkGQzSdZuNuvuJrWliOTi0SreRe2hB/+AHnrw
	ZC9KhVpFKN6rKGKhFy3xzW5MtqXqwM5+8943731vdt8ADXLSNPWABOQNx1KiEWlsfEJq
	/IgAjqIJQTQlVdvsTiQGQYNz+Xvn2HoPgVtWw3v7d7J3rZrStpoHhP1A4Eea2Sqw7xdx
	ClkSAog836Epx3QI3+PY8uyPOU55eMG1Dys9xFkifEA1Lc5/TbhTzSXTQINIOJT1cVI+
	nNeLlNcdB2luZsbIEL1PkKa7zO6rYqGcTvYOkL2d9H5Os94+wiHCCxmtP0a4jZ71jNU/
	4mHhpObEhj0cGDX0+GAVtxqp+DXCFF8QTSeiVHHZLg3xmK79VvJKgnCQOMpkYYBzWkhP
	10xu+LqHBX0m1xOv4ndWUeF5jxNn3tTd70XaAq8wDh0MGgyaDUhQEEUEYZiwUECGPBox
	NLJyPyOrBhuTezJ1JGq7dGJEsUF7Ntw9t1Gk3Tz+KCJxlEO1CJL8Qf4qr8lP5Xn5y1yw
	2Fb3lK2bmrry4DvF5Zm5Gh7X08jjc01efJXUdpNXR5aseXq8muwaP+xXlzHmgjWPxHOw
	+/EtX5XMlymMFMXjVfPqS4R1WjE3359sfzs94i7PLrXWc62JizdWm5dn/WpI++6qvJPm
	VflPXvXx/GfNxGPiKTEmdornIYmXxS7xkthLqwviYG3HCJ2VhinSbZH6JNVgYJq89S9d
	P1t4vUZ/DPVRlBnM0lSJ93/CKmQ0nbkOb/qP28f8F+T3iuefKAIvbODImbptU3HvEKFl
	pW5zrgIXv9F98LZua6N+OPwEWDyrFq1SNZ8gvAEcdod6HugpmNOWls05Uocsn5O66cpi
	UsxQ20NSUtcl12VLFrOZVWLpdtiZ0x1uHKE5QvfEp0plk/qv8RGw/bBS+fmsUtl+ThrW
	gZf6b8C8/UUKZW5kc3RyZWFtCmVuZG9iagoxMiAwIG9iago3MzcKZW5kb2JqCjggMCBv
	YmoKWyAvSUNDQmFzZWQgMTEgMCBSIF0KZW5kb2JqCjEzIDAgb2JqCjw8IC9MZW5ndGgg
	MTQgMCBSIC9OIDEgL0FsdGVybmF0ZSAvRGV2aWNlR3JheSAvRmlsdGVyIC9GbGF0ZURl
	Y29kZSA+PgpzdHJlYW0KeAGFUk9IFFEc/s02EoSIQYV4iHcKCZUprKyg2nZ1WZVtW5XS
	ohhn37qjszPTm9k1xZMEXaI8dQ+iY3Ts0KGbl6LArEvXIKkgCDx16PvN7OoohG95O9/7
	/f1+33tEbZ2m7zspQVRzQ5UrpaduTk2Lgx8pRR3UTlimFfjpYnGMseu5kr+719Zn0tiy
	3se1dvv2PbWVZWAh6i22txD6IZFmAB+ZnyhlgLPAHZav2D4BPFgOrBrwI6IDD5q5MNPR
	nHSlsi2RU+aiKCqvYjtJrvv5uca+i7WJg/5cj2bWjr2z6qrRTNS090ShvA+uRBnPX1T2
	bDUUpw3jnEhDGinyrtXfK0zHEZErEEoGUjVkuZ9qTp114HUYu126k+P49hClPslgqIm1
	6bKZHYV9AHYqy+wQ8AXo8bJiD+eBe2H/W1HDk8AnYT9kh3nWrR/2F65T4HuEPTXgzhSu
	xfHaih9eLQFD91QjaIxzTcTT1zlzpIjvMdQZmPdGOaYLMXeWqhM3gDthH1mqZgqxXfuu
	6iXuewJ30+M70Zs5C1ygHElysRXZFNA8CVgUfYuwSQ48Ps4eVeB3qJjAHLmJ3M0o9x7V
	ERtno1KBVnqNV8ZP47nxxfhlbBjPgH6sdtd7fP/p4xV117Y+PPmNetw5rr2dG1VhVnFl
	C93/xzKEj9knOabB06FZWGvYduQPmsxMsAwoxH8FPpf6khNV3NXu7bhFEsxQPixsJbpL
	VG4p1Oo9g0qsHCvYAHZwksQsWhy4U2u6OXh32CJ6bflNV7Lrhv769nr72vIebcqoKSgT
	zbNEZpSxW6Pk3Xjb/WaREZ84Or7nvYpayf5JRRA/hTlaKvIUVfRWUNbEb2cOfhu2flw/
	pef1Qf08CT2tn9Gv6KMRvgx0Sc/Cc1Efo0nwsGkh4hKgioMz1E5UY40D4inx8rRbZJH9
	D0AZ/WYKZW5kc3RyZWFtCmVuZG9iagoxNCAwIG9iago3MDQKZW5kb2JqCjkgMCBvYmoK
	WyAvSUNDQmFzZWQgMTMgMCBSIF0KZW5kb2JqCjE2IDAgb2JqCjw8IC9MZW5ndGggMTcg
	MCBSIC9GaWx0ZXIgL0ZsYXRlRGVjb2RlID4+CnN0cmVhbQp4Ab1XSW9bNxC+81fM0T6Y
	4b4cW8MJ6kPTNAJSoOhJqGG4UorUBZqf32+4PerpyQ56aIxA5Gg2zvrpC32gL6Tw532m
	mCz99Tt9os/05vZZ0/6ZdPl73tONkp74/8T4gC/v8f+psIvbj0WToo+3UKrL5YY/WO/+
	SElTsIRPJYPX2lN0MjinHB3oIxxhNxTBFhgcvk8iKiWtz56FLHhzoqiSNCFoyMQIPQZq
	QHLOJ5AehyyoQ/aBlLiH6qdtt5o1SgYm1IkxJ6O1Kc/GnPTeGXFia5Es75AquBBiDAiY
	ct4H68rRGuWzzvDkD3IewTAyBos3GTKwHaPOAvFfwroVRQhqE+EE8rXIcd7YNvJmOG8c
	RzGJc+ydk1oZx6n4fgclYFJ0g88bY2XKOpCVGf+Sp92R3rw1UiGLuwe62l2L3RPd7Uq1
	jMS+rFNHJCWYfK5UFKW/0tV3P19zXRm6eneNUOHzbndNv9Hu/szWKCKrAmmVUBE4CQ0r
	oZVPL9lRRswaPNwozJlC8DgfIGjH+ZHVVC5xROQVCvpSoRTTxsMgG8/UjbO+fi76hG5c
	nBFL/4w2Qei4iy41CJRwXRxJWy0tHw9CO1cOxAdmONSTTUgZKgzXwqwnAsoDHi0EdkOL
	ahrtpY2WcSkHmCsCwSHhXB9Q2QhBmkp4XAm9GqtqY/jQbWinUGLV7eZlkBGE0k9nQt/c
	TSaqqZ3UWTstKajVcWFCsZrRXIuW0lzitLnmUWIxNFbdhbJWCCbtONzIeGs0lKsPynty
	YtVi796j9DeabEP3pBCz0AQfzLm+95valjZyeGmvEJSz1xIF3QhIhs1os/59vRrFxcdf
	mFIk1hu0Uz230VNWxtJ/GDhO8KBiA8yMUcgq6hle8xC1zFW2yiv9JyonG+z6jCkuNUd4
	J3R9lasUfus5FL7JXH1Zi5AxNiLGJxSZSqNBg4No3xzw0EGzJhdJUHgPxQSeShEhpapr
	P2gUUpkzQyhmqUx2k+LocemWcZmdw/VAJgXIYBQHXE1CeuA4Lk3TQoEpCA8GXPsFri2X
	4tMJY3ObTXXt/XGL8RYo0UO3hGSPWJ9TOYcdFEzrZ9RdlxDJS2tgeU7BoE0pGLQecEoZ
	mMAYO8Itsmu6phRkXtpDRiustohJP1LLO2RkgC9TBvg6ZYCvI0ZD06DA0hRX3KZvxHQ+
	4eoeT9Ef7xrRH28/j9pJ9Aff8QwDrMZUqyLUFmBNEOdIwGFs6eyxfNYY4KdloGC2nQMc
	7Be1BjjRS5OsAxYbE5ln6cPs1SgNrDs8RNns7CzAiKiNl4FsZvmEyY/3BINvz4CNRhPx
	e8pzxARpgD5+7ODj7pcT1LH5OkZy0+sECjdimGGvYSVHYLgDw9toFA54SKUAjXa27Sfj
	axOxS9viLzoAkYsK3v6MZ5kFdl4MAazAhf1RrHCdBtLWBvAEQ8sytFslFVH4oSGvt+3z
	69eKwMQa7Y08FcDEOB6riesJrwWuwijmjttaBKIxYnzlLH2buwDtVVhhJwL6Qtgqc0Lj
	SDCgKmYW6VfBx5DAj4bqGDvb7RVnMUsXe6I+IPAymexN0vysb6oLRhDeRRkN4NrA5/iR
	8iquZ0mjjfQZU3uSfK3+OR3YsWjJ8iNrVQEd2XM7z/UPSL+NNl5SNiD9Gr38VyzvAFw8
	0mk9xnOFuTZMh4p3maQTowXr8VutnbcrDRjWUVBlrRRmhHMIhvJTkyHCwnUZ8vN0rJxs
	nJ2sxrkv27mUfNdXuS5Wyno+lkoBIEGhoH3XA/LFHQpuHVGOMWH2LJKvVwrQhNLSJxP+
	z1nJFVqHJcLZRmOhlWnJcejjcuHcnJcYJACfPA1ZURmQTCkTk9W0kTm4Wolsro1iyqaN
	FQhYFjQ34eWh+bmvjj/74e9r5Bc/YKmN0Utjtf6w/fAvfQ2P8QplbmRzdHJlYW0KZW5k
	b2JqCjE3IDAgb2JqCjE0MzAKZW5kb2JqCjE1IDAgb2JqCjw8IC9UeXBlIC9QYWdlIC9Q
	YXJlbnQgNCAwIFIgL1Jlc291cmNlcyAxOCAwIFIgL0NvbnRlbnRzIDE2IDAgUiAvTWVk
	aWFCb3gKWzAgMCA1NTkgNzgzXSA+PgplbmRvYmoKMTggMCBvYmoKPDwgL1Byb2NTZXQg
	WyAvUERGIC9UZXh0IF0gL0NvbG9yU3BhY2UgPDwgL0NzMSA4IDAgUiAvQ3MyIDkgMCBS
	ID4+IC9Gb250IDw8Ci9GMi4wIDE5IDAgUiA+PiA+PgplbmRvYmoKMjEgMCBvYmoKPDwg
	L0xlbmd0aCAyMiAwIFIgL0ZpbHRlciAvRmxhdGVEZWNvZGUgPj4Kc3RyZWFtCngBzVtL
	kxy3Db73r+BxdVC7+e6+RiWn4lTFkbVJDnEOzsSKraycsuX8/3wgHmTPa2fXduJSSRpg
	CBIAQRD8yPnevXHfuwV/ct5cXaP74Wv3F/ed++TVR+8OH51vfz4e3Mtlzo7+Dg3f4cvP
	8Pd9az69eosGNYct+TMf3r7CSPgCf17SfzTY4YMLObhYnF83l7dEw791b6azLetpQ+eX
	ZV5LcnVZ54j/P4CT5ljXClaa17SFyThl22afl9WkypbngtEPbqtzRNvkyhZmH3x0m59L
	XmrpnHWdfSxLnrrYmuaCxh6NpGvj6PBOxUxHjCZ9T9X7eSVlbXzjHFt2cO+a8xZ33pMw
	U1wBnUjNnS+gejPGfLHG5pxkvljhgRK37A6TKbOhx7qWYgo745hVXc6Z7dr5ZJyqCphc
	VTW7O1z1zfdpdAdzTq07UKAgzpaSSqmYJ3xMOZeY2scYlrz5DS77l0vZlZTmlMvqts2F
	MEMg5AnB9u5sUELA+4zAguW9vQQnBXugtbE4rIshUreE5VFynuuW04TY/s2986FN2kv8
	/zJi1mtYs0vu/oP75NM4L1gQ9+/cX93d715A/+Du/vCCVho+/Plz4fz+tXsxNdZX+t2X
	d1++eOH+5u4/c6/vn+KExc9bfIoTMlbV+gwn+DiHetYJyWOhFejwTCcc/q1e+E4//EM/
	PNUvkwRHXsJcQxqCA9N1LThCrnPexvYtmCggrwZH9gnxGhMlvovBMT0xODhw/n7WCdPt
	KyT5Opd154TtvBMotLFCYsI6iqMTWvtHnZDCNi+hOWE6dUKMtA08NzieGgGaHmLNsy95
	Z8zV9BBXCGTkbU0PSDW8d12PAKymOS/pl0gPz14G5oSICU1PcEIKCJn6DCekOG/rWSeE
	NG/btoRzEfCFhvjre8mNf1LOFy8mTpvInz0vnhQxukVGv2C667hDxqVgvw/R6ocpYj/d
	MgJcpQL2xRQ31EV9fwobTAlpKBeMYzudyk2HvkNa57ZDmgImp1oOG2REYi2rr32LVo5p
	2aXO721qTaphXmOchoop1YLkjAm1KiGtAbtqCdZ7QtznsmyjD9KKBV0zlNKSSTm99hnk
	zGLrvHNEgcl8IFrScNZ52eboUT1amZKUc2QbpN4+JQMWbLPLUzIgIsT7XdK4KQkk1Dk5
	WQZE5C4LQv7+sCsXkI/h6XxaLtz99nMsgPv3vPkfFyDa+WkBss4hlIzJPM6ud6g1tLuz
	BRXqj2UsqFqlkyk2qNZHBMV2MijY3Yjx4LhBKkqjjsDJQb6fHtw30qJ3cT5YWz+BFuJS
	bJzg10bzMFOIRcg2Sv9WBunij25NpuHp/kwVzbpgz8cCjEhR2+Ua7lPJTq/fIBlNWqQN
	82SHn2YeSmqq1dtnv+QZ1RE8KLq2QxidmajYnFqbgmjPsfkdKwAVZ8W/RMMfiKuRFg8M
	EnSG+Mwt0/uxcN3r42Eq6QCVqPuuUuu9k9z51JuTynogvGitj0gWKQazOFXsBc1iLFU2
	ENvK4uksx/bF3Eg1z0i1zpqLce792fMjOxjlOQ2nxtnobJyR0rfn5kiStxm3YgLKYBzN
	LSKXp5MUmDKSXUKFIwpkHDeJFOM6KQr05mbcyczRSmfjNiyztlB45mx0Ns5INc6am3HX
	4iJEbH/cOx10AnzlsR66bS5jJlYkMrZtypgpItU2I9U2a37ZNqqUm98CZoKGE7/10Ztt
	nZS+e3OzDTDFcVSa57C05wrogrpvxsFXSCSjcQkTV1c1zgEsaKQYJ2RPbb3548a5gJmg
	4dQ4Hh2dsXGmjBpnza8Zp64DaoJ58DkgiptxVOziBDMah4w2V1QxokBaaiPVOCNFAWmO
	Dm8wLgINoOGk7z56M07I7rje/CbjEgE/OIrozNExJtSK/kic7cVpraKyUwXgDiLFOPIO
	k2qcNW/G4ZB4Gjjm2xTgS1RM6jgeXfsWZUBy31NvfptxGQBMHozLmKatzZwmywicIizd
	uABng1TjjFTjrLnN3OVkmQoch+HUOBu9zVwyUvqW5jckS64XdKdG/YCjZHJKPzhfvVKY
	yEYhOPlTzKi4a9sjW3Z2Owa+GxnNywkp3+mpE9p56gEMVCd0DEW3zMDxRxjfQKNRCB4w
	d51NItzcNNExYkZporqyaihlmKFjDEJUrF7bQH1KLgIxBJDVqiHanDoDdgBE1e/Jb0SW
	hQykT1vBTpNpMxK/jYymXGd0v9mB1WzCcqYTrPktovwXhtkkp9xb/aYD2xhIR6Yrq4aa
	hJW3MboQ+83DdwDkIlbkHhGOABx2iDDaCMdgW5USaBdHtQiYkCDhjv9GT0dEwMeGEdPh
	C5AwGIojx2VriLABwjhGCscAYZVSzBpjac+G/troA6fZNXWpDr8f72uUntQiw1mJpY4Q
	NHYyjiK2JjbAuqYL9iGGg03fgdM8MY1ianjvWzmGBpsrOhisfQMbFzDYxh84MsddjCL2
	7NmFuHJ2aXhfqMtZNPg81kWeJIkzcDDhQzTqJcQveqwLw4Opo1O0S/Dg50F+5/HgWw+8
	zQ9nAOHzfqBKqfnh6Yiw+IEOUICE4Ydj1O9XBwmToU/DhEniGaAwu+ZXiwqTVeeQ0esh
	8gxolP2gW82ZEMHVyf8BGG6zihTnw62gKEmkCglUTU+AhsV+rMaEi6pT5OYnXx39NGy4
	+QHIxLICMzWzrl2gkURGeQJsZhR4DB1jPwTshiiUz/nhl4WHdcv0gKUK9vlhx/QVsMUW
	o+2hk69xjhvQQpMCLrOuC8HDtmP5AkS1IOvpruYGDtcOXsSo5ND9sffdOTq+lByq41A8
	eFRdPoSKXV1KF+V0HcUyuky+VjQUQG4RV+eDB8qCQmxBAFjNABgYbqL7eym5CunmESCD
	BwqVNrjxm8wDyoFPpHoaxNTe3nfn6PgsNomONJiWTxk14VorzZJ4YOBwydCl9hUDQZwX
	ro/bor75doyyF0k843qMw19PKlwx0L3Gz4MO73s/TTI/HzzMIw34cEQq6PgvTg7SogPE
	gU4Ve4T4pJcLIdvKrA7y8lh2lkTcURnWQWIeyb7HyVxaGMr8eFH3v8aJWUPsRAQUM9Fx
	WFG3I8UTnYK4+OxQMa2IHTZMi2bHUEeMaDEqZqDFFwAQUQTLkeFiHqIrxiMI3QCeYwlS
	/dqBV9p3yLgxJm8wLXXAjYqhwGKpwcRiqNFmJ0s8CmKJEnTYbsCxmGk6iJlMj2aaxI1m
	dvBYzKQJN/SY1eh4MKvRAWNWo9NqpkjcaqZhwmKm6SBmMj2aaRK3mdlhZLayQ7d9MjuQ
	LFYadCxWGm1WMpZ8o5UdHeb+uw7cv9CDlV3iRis7oCxmwnFIOsh93cyOEbMaHVNmNTqt
	ZorErWYaTixmmg5iJtOjmSZxm5kEFK+MvrKZhHhW4KGjmRGAamj4K6tBiCvRtA1QDuq0
	mikSN5pJoCqNScUKJbmug7iRdRrM7BI3molb/lgbDstm0ukQ1/A7MwMu1OlaWtWgOwWi
	1cxOq5kicaOZGY8WaEztv+vAZgo9mNklbjMzA5dJHkWubDK4rMKjwAYZ9qD14OGQYmp4
	wr4aHMhqdFrNFIlbzUR/NKaZaTqImUyPZprEo2a2OkT3fowAgDtNSmOitqK4M00bUQ0g
	pk8DfhoxsztAFejiwIBqzV04B6JVh0KlVcdPlTHgp3En9LjLuHmHQlUTw0+Vwfgpe+1Y
	iN1G1UV70hjxcBarEr6KvGrjSMMv2I9xyEVJL00i3vOQgJ/x+IUehjUSxR3JoxYRGg84
	6HUROmAG7u1LzSjWrT99gIQLHepQSXTBKinjATdmcBpeGvdGAVFAMtzpJCSwpTboARLt
	e2VcJ3vzZgTcJu3Vxj6++mDntAOVlTsO3SCg1JnotfTZk1drrS+qEJrNBUp3p+Pegh9d
	NSdOCcgIpkKdnpr51ZyeCFujCyR1emqzknCKE6fjJR+OemgyyYhCUheskjIGp6uMeMU6
	ZSfpoDjR7hmPkNZcjFCnq42YU5l088HOad3p+j1f218HaMmPFx6j4TnLgnQWTx/M/LE/
	mDkzmXLmMC9JOnWJHhjx9R3jxr1mbzcXrWaPeCNa24Ve283aYZ5ozCDvltjjmNb82iUu
	X1LRqbRtXVACB712qSe7pSnF/Xcltf8uYYkC5wI6ZDT347CLpzFt02gxq/QQs8rihR8A
	ioQMZIhDOBTkDchrogi4NAOs0BNFwKIrkXAGjVmqz1oTjlklG4TSru/1+yFmtZEEoXXK
	IWqDShBOxjj6/ojUmFUjNGbNRo1ZwgSamXun9ZjV7yVRIFlcSRRT9ogCLgaa05UenK4s
	dnpG8FSgi5J8c0DegPxhkq9xGwo0qyeKTJBrIXhLnU7POVoTdrqRGgfKaMlSFqryxOnW
	KXsx66DqRWPsv5dkbt/25myEOt1sVKc780GLVKEQJpqd9ftbEgVA4HlLqHROb3J6oji6
	ybm7nijk7EpeWrf2/ILXaMavFdZYpJDQZ6z6DEykAh7d+faTF17IIeJWD7Qmik7LQsZL
	OZW4mihUq0wPRIdCjJXS/idRErQmimwSlijGioLee67tBY1sNUI/YOvhisJpEw7KhLJq
	zcE2NyCuJK+Jgp59tveasLhVFAhvIM4pDv3pk04ZUUmN2Zbs6MnnkCiM14Jw0k5189JB
	NQhNC47ZS6Q0n5IYoTFrNlrMmg94cxMn9ZiV71tteBU8afL00JjeqGpFofSQKIQlmYDy
	EwAWdTph0iSvTifQFr9v6YkCT3HnWvC+xyaRwpGaSEWhpFUUxhicbjz2onWqJA9qFYVp
	sf9e5si+1TlSI9TpamOvKNQtnJ2V6k5Xzi2J4vIL3Jc9URw9H30kUcjebV7SioJK5Ion
	6nziuJAoEl7XBDz7hRCdjyeqS4jWRNFpXchd4mqiUK0SHs9BCekfgaBKScVitPYfTYIT
	xZWyuOAIpS9+MACfvJTxgLM+drQdiTdWVCzRF4kQNT6/suDIaEe2gUGayOlL7+EwXmtF
	z/PoYo48xic9rBFhwCQ+TokQvHDZZwS6c3MbWMcgZEd1ZdXwrzB0jEGI37UnPG7gv/tf
	ohHs62rA9t4eHVy4QkAwAOvFC5fEuxaCE1UHfo929/kP3/7z2+++enBny1z8wmDofPcL
	txUVM/1C4rjH4Rdu+NkWws/dfdTfbvz4g376j3z1QRlf84fpToV+FI67w41m6wYq9h98
	vPkvIcvVSQplbmRzdHJlYW0KZW5kb2JqCjIyIDAgb2JqCjM4ODgKZW5kb2JqCjIwIDAg
	b2JqCjw8IC9UeXBlIC9QYWdlIC9QYXJlbnQgNCAwIFIgL1Jlc291cmNlcyAyMyAwIFIg
	L0NvbnRlbnRzIDIxIDAgUiAvTWVkaWFCb3gKWzAgMCA1NTkgNzgzXSA+PgplbmRvYmoK
	MjMgMCBvYmoKPDwgL1Byb2NTZXQgWyAvUERGIC9UZXh0IF0gL0NvbG9yU3BhY2UgPDwg
	L0NzMSA4IDAgUiAvQ3MyIDkgMCBSID4+IC9Gb250IDw8Ci9GNC4wIDI1IDAgUiAvRjMu
	MCAyNCAwIFIgPj4gPj4KZW5kb2JqCjQgMCBvYmoKPDwgL1R5cGUgL1BhZ2VzIC9NZWRp
	YUJveCBbMCAwIDYxMiA3OTJdIC9Db3VudCAzIC9LaWRzIFsgMyAwIFIgMTUgMCBSIDIw
	IDAgUgpdID4+CmVuZG9iagoyNiAwIG9iago8PCAvVHlwZSAvQ2F0YWxvZyAvT3V0bGlu
	ZXMgMiAwIFIgL1BhZ2VzIDQgMCBSID4+CmVuZG9iagoyIDAgb2JqCjw8IC9MYXN0IDI3
	IDAgUiAvRmlyc3QgMjggMCBSID4+CmVuZG9iagoyOCAwIG9iago8PCAvUGFyZW50IDI5
	IDAgUiAvVGl0bGUgKFVuY29uZGl0aW9uYWwpIC9Db3VudCAwIC9EZXN0IFsgMyAwIFIg
	L1hZWiAwIDc4MwowIF0gL05leHQgMzAgMCBSID4+CmVuZG9iagozMCAwIG9iago8PCAv
	UGFyZW50IDMxIDAgUiAvUHJldiAzMiAwIFIgL0NvdW50IDAgL1RpdGxlIChDb25kaXRp
	b25hbCkgL0Rlc3QgWyAxNSAwIFIKL1hZWiAwIDc4MyAwIF0gL05leHQgMzMgMCBSID4+
	CmVuZG9iagozMyAwIG9iago8PCAvUGFyZW50IDM0IDAgUiAvUHJldiAzNSAwIFIgL0Nv
	dW50IDAgL0Rlc3QgWyAyMCAwIFIgL1hZWiAwIDc4MyAwIF0gL1RpdGxlCihFeGFtcGxl
	KSA+PgplbmRvYmoKMzUgMCBvYmoKPDwgL1BhcmVudCAzMSAwIFIgPj4KZW5kb2JqCjM0
	IDAgb2JqCjw8ID4+CmVuZG9iagozMiAwIG9iago8PCAvUGFyZW50IDI5IDAgUiA+Pgpl
	bmRvYmoKMzEgMCBvYmoKPDwgPj4KZW5kb2JqCjI5IDAgb2JqCjw8ID4+CmVuZG9iagoy
	NyAwIG9iago8PCAvUGFyZW50IDM0IDAgUiAvUHJldiAzNSAwIFIgL0NvdW50IDAgL0Rl
	c3QgWyAyMCAwIFIgL1hZWiAwIDc4MyAwIF0gL1RpdGxlCihFeGFtcGxlKSA+PgplbmRv
	YmoKMzYgMCBvYmoKPDwgL0xlbmd0aCAzNyAwIFIgL0xlbmd0aDEgODM0OCAvRmlsdGVy
	IC9GbGF0ZURlY29kZSA+PgpzdHJlYW0KeAG9Wgt4VNW1Xus85pXXTB4zmUkmZyYzk8lj
	Ju9kmCSQkzB5kQyEJGASE80QgoECBkUUKTQKFglata2AaC8+6NXi406ChUErl0vprS3l
	Xlv1YpGKtqgoTWkpahUy03XOhAj5/Pz4vuvXc7LO2ms/1/7X2mufsyerb7ltAOJgGFho
	7Q4MLQH5EjoB0Ne/IjAUlZOSiR/pX7PaEpX5bAD2u0uGbloRlVVBAI39puVrJ9snXSL5
	r4MDgcXRciAZygcpIypjKXH74IrVd0TlxDeIi8tv7p8sT3qL5MwVgTsmx4eTJFtWBlYM
	ROsLhcSzh26+dXVUzvgzcd/QLQOT9ZH0Z/8XkHK1sAzUsBxUwFBaCz0AyjMaO3BUKpUT
	1bgXPH5jQtUnoFPJ3T3pzB+WEv9t/dnnn5285IzZoK6nemq5vlRAbRQ54RyAWKTyt2M2
	TJVIpdKlDUFbXggaiWYRlRLl5o2qxJfwQUjuvSCqUeAgRnjL+JeDmE/4fyA/g5gvxsaB
	un9jldC/cWNjTo0am8HDIQhYB3aZ+8bszwohnDVmtxGbGWXMmMdMEohqj12Y8CwSLnlC
	KhTThH/YfyB8RvSpvVr4xF4k/JbqveZpEI7VUPmYcDQ3xBD7tT3EoZggvGq/S/ipJ0d4
	0VMpjDkpb0wYrSG2T9jtuUt4apOc82SuzJ6wh3DnmPC4xPYJu6j/bRvlgoejDe+OsqFN
	8kA375XZyr0h5tl9wgp7lrCIGqIYI/Talws9dq/QURNCx5jgl5rtE1qcx4RmaegxQYwO
	VB7tvcwua1wcHdZlf1nIjo6QKdUWkwSLvUUwU/+ux7cJLvsNQk1uCJ/Z35ida290bisP
	4QV5DImRohJbGWX9zlfwaWiAHOwGBz6ytzGHdMYHx4SNxHbubcz2OELsGTFR2OtsdG4i
	KidyEC0IYYfoUm5XLlYuUJYo85Q5yiylVZmhTFMmqxJVWlW8KlalUalUChWnYlSgSg5F
	3hXzJC9KVmglpuCkJyentYyUpgc9gUEVA3MgpIB79GuqU6sTZ+m89b6vePTJmX2+vC+v
	1C+TealoDm5rbu8M7jF3BYulRMTcdUX5/yc5UEutm9vW7m1be3Zh3YCtrs9WN0DUF9y6
	ZjA1OLzIYhk9u1YqsATZrL5F/YMSDwwE19oGfMGzNp9ltE1uN614oVTcZvONwsK6js7R
	heKAb6xNbKuzBXxde1vrGudeNdaWqbEa675irDqps0ZprFa53bSx5krFrdJYc6Wx5kpj
	tYqt8lh5eXVL22uBPwQ6/jC4+e1g5mrBDBA5QfS2xMPtkXP8a6CJTETGWYpsmCnRqYuY
	DP8BStgPGyja/A72oBpsMI7F8Hs0Yy68BWF4G/4IJtgKj9OzDs7gpxRlPsJsqlMOd8O/
	wa7IEAxBNd1nkIcUmAEfRdZFXo18DrUwAkdQiUlojhyAAthM9054DGOZRZFRSIUWuJ2i
	+t3wKzgRGYt8TP2Xw/uowwKuMvIHcjCecrywBfbAfrSiDXPx+sj7lJ9KOvbAnog/soba
	naNaBTAX1tFo76GAWZiHO/EddjwyHPkezS2dyhZAP90r4C7YAY/B83KtRVw6n0L9+6CZ
	yr4Hv4EzcJ4Cbg7W4h3Mm+zH7F+5Sm5n5AjpsYDG64NdyBIqdlyAi3EIn8cX8ef4KeNh
	AqyXfZMb4p4g3RbAvfAEvAK/hNfhD3AWxuELmECOdJqF83Ad/oja/ZEpYXqZ9cx9zAnm
	HFvEvsMpua38PfzLES7yZuQL0jkDcqGSVvp86IQBupfASrgNvgObUAnbYRR+TtqeglOo
	QS0WYBE2YAdej9/CtfAQ7saX8CSexg/wI9IuiREYG1PArKHx7ma2MM8zY8wBZpzVsavZ
	9ewh9h32Uy6F6+UO0X2Kd/OrFemKZuX88A/DpyLuyIORnWQXPd12yAE3zEKOUFwBm8iS
	Wwizx2A3PAsvwBiMRS6iF47Ab0mv9+AcfEYWS6fbisU4A1txPmm4HFfgd3AHabgH95GW
	L+PLcByP40W6w2Bk1IybuZ4JMGvp3gk7mNdlfGJZK5vNutlmtj3yN/Z5dpQ9zzm4bm4V
	t44b4XZwu/h0fiZ/Hd/ND/EP8/v4o/z/8ef4CwqzYrNit+JFxetKlbJUuUMZxkzSxYIO
	eBEOktdtY4dItsNs3ERWXQi/Ie8dh1/ARfgcDsHTaIYwK1kzK/IEhCL3kjVfgZ+y34Yq
	eIj5ATMnUs0+w6qxOPIZ9VVI9rp8g5ibk+3McthtmVaLkGFOTzMZUw36lOSkRJ02IT4u
	NkajVikVPMcyCK46W32fJZjVF+SybI2Nbkm2BSgjcEVGX9BCWfVX1wlapHYBKrqqpkg1
	l0yrKUZrilM1UWupgiq3y1JnswSP+WyWEHbP76T0/T5blyU4Lqf9cvpBOR1HaauVGljq
	Ugd9liD2WeqC9WsGR+r6fG4XHhBpM9C4XXAAQIQYqeMgzA6sp+AKs6UadUGTzVcXNNoo
	TWWsoy6wONg6v7POl2a1drldQZzdb1sUBFttMCFvsrnUjoKgo62Txna7lgZJf9gau9i2
	eGtIhEV9UirQ0xlkA11Bpk8aQ5cXNNh8QcOd76d+KV5O1d13RWGQcdQHBkbqg2LfVgJd
	EvskKXAfSc3tFuqWuaerM4j3kHKSErLu0VlEtwlH3zJLUG2rtQ2OLOsjzKG1c8wkmups
	fb6uILR1jhlFoyy4XQdSN1RaCZQD7hp3jcQrrakbovzDjdH83x2SeOqGI+8Sb26bwgWl
	sW1NpGbQ0k+DEBak6wzpMTADRvpnEHx0dSHNcinpMzvIkCuxjiDvaAoEh9sn1QgM+iaV
	W+YbUxtN8r5U20X1+0a0FWRAqq+1WUY+AbKsbfzPV+cEJnMUDu0nIBVK9p9yoSAGLqfX
	SPung7akwVTboGS+NbKpSbal1l2RQbK0b7nphdPVHAJ1a+co4ve6Qhi5JwQ+8wHaYNgb
	b6DiPMnhlvpoOBJcLsrItVKKNKingeolz7CMWEaaFo9Y6i2D5FKcQ+ZUMDDSVUCAtXcS
	LNDRaQ2KXWlTyYGurgrqJ1/qh5pQ9ZEu6mHZZA/E5ayCCapU4GqmWWW1ds7vDA770oKi
	r4tAJyc+1NoZPET+29VFtQqnNCWN1y9NndS5iHQuzKXy4mgv9FozTF10jYxIfbZ32qzB
	QyMjaSPSqovK9IY8PUOczAiBVEVCOITDrdSWmM2aJkNutVlJrS4J0xJy4MsORK/1X49w
	2ZTe1LKctC2TEfZ8QwjPuBaEvdeEcMWUplchXEk6V0gIV/3rEJ55FcKzvh7h6im9SUmR
	tK2WEa75hhCuvRaEZ18Twr4pTa9CuI509kkI1//rEG64CuHGr0e4aUpvUnIOadskI9z8
	DSHcci0I+68J4blTml6F8DzSea6EcOu/DuH5VyHc9vUIt0/pTUp2kLbtMsILviGEF14L
	wtddE8KdU5pehXAX6dwpIdw9hbCYFoQr4/DwtLAL33hgvv4KyPlfwk7GS5/PXtik8MIA
	dytUcn+CauIFxGspfwvRVqUZhknezJrhbiqrZfZACsnr6X0rejZEBzyggH0kW2Ch9EH+
	DV/SZ/7li72c+BpO5wR08aQTHWIRRY+r1KCBGKmArlg6S4qHBDldCqX0ZTWGCzHI+JiD
	zHk2kX2J03Ad3Anezksnbwx9MQK93x+m0z8lzBKtvMJM782c0syChufMLMuY1AqlGcGo
	Uu+xLq+iQ4W5F6r8E1VztZ9W+bUTVVBdNVElUVFhic6qcxLt5J4KXTrGH/5iVohru/iC
	pApD3zSA+/hTNA4HhaKOY+hog1MYDCaODluMvOIlOueyYtnoWnkE/4SXPtN9HxRAdTX1
	nIM6qxP3hf8Hix/hD9OnNwsDkeP8x/w5mncafYPWiE59rD7RmGw0bTfxfE26gY2pSVCn
	M570pDRThsqTZDRnnLKeXRjtfZy09+u849T9eHXVeFEh9NoTy0oZWyaTkpxYYlcqrBZn
	lk7rKbdaDHodsqeOH9+//8SJ/czhU0+Hj4SPPP3uu09jFVY9fWpi1kmM//wfmBA+//nn
	4fN3vv7cc6/j/fjw0eeeOxr+Fhsr6VoZeYebwd1IdvFCBTwgzqtkysrX4hbk3srArL9/
	kPuhLT6Opy/yJFMefddwWflZ+blSBpcWk5mmd1UIylxNjKs4piLJD/78irLcWVmmKpM/
	za3ylxkrq36GRrBCI74Ak9O7IE/wtM577P335TmSgY55dYkGb6LXixKXKK+oEHuhF+Mx
	ARVKRUqyvqS43OMs95SXlWbZMpUKpZXS1mL6utIlGzLQkGLNRyfVtGVmlZV6yj1JzDtp
	nkKx21k7v6LnUfb5eZkze7sHcjM04XF1wypM2rt1K8Omp4d/HadhK/09q3/4X48u+PEQ
	k6hLUcdqDc62pprlD5zTJJg8s0uKHdUP9DzY0PCLcGzpnBnZcbnWCofoLvvJo7/qLkpB
	6XCYfKiacBzgD9EZrhnmia4zsUhamxktCwa7VqnQmO2amBTWlCQoBNbJmQRTeZwxQ9hh
	bay7wuwTF07rEr2S4elP59URJJL9QW8g17WWxaMtE8q0UJJIKDgJBhkX5s6dhWgNn535
	2Or/DF9EPL5/w8CstvW33b6W67nOz6i+ELcHOrHsPBpQvHTLiw+8urD0lfu2/5RsXxA5
	yVWQ7RVkoUx4VmyqV21O3o6PaDgFqnmFljc18/XaJst38Z6EzYKG1bOGJH2SoVHVom8x
	NJl69D2GbtNJfJv7yPyh5TOLdg7Wa+/lN2o5JoQPiyXz4m+MvzmejY9PU9gzrUpDoist
	Rs8ymWy5YV1mRl/scCwTa7IzQvzDGUabnaCYO7m+TtMS6KU1cHq8IArHMVoIid6C3glC
	Y1UvruoFsn8+2sr1BrqVVnqUFJPRrbIDgE4LlYivrYjHl5Xrrr/3RIOYFMNM6BWByvZO
	T4YBbTHd9116LXwYhfeT2dXfXrbqtrNLVgaGm+/fXZtTnFYYWLwLYzEf0+jYmy4WasO1
	3A2EUxydNhVCt6gdySBH5e2FBcpEhSPOnhPCatGSbnGlJhQyQqLgcBa6kkzF6ZvS3Opy
	l7Go+AozR91f5yVTk/dPHKse91IYGdeRobHXftm7U5JpRok0uTKrLplWfKbdeXmSM6X1
	QMGgrDSxxMO8MrJx5TZvhqViW8zMQRFTGu4M//tvw/+Ix/LYtPwVO0szcwoW3Pu7i+ff
	uf7j7T9+9Mn7m1feOGeEvcWYd+uPLn76+rdCu58q1jtvqn2svt5Wg85Lf8dmOdQzdMYD
	uIc/SrNXQqWY2QIt2AM9dJw1SktfodSoKTyCwolKCr5j1taoF8vBV4qNFBqr/XLsQnJc
	mfaET5GXysTR0WD49osHpZ1sKz2ekWO8Q0xiADW81LETjRw/1a1/Qg641Gk03OIz4T+h
	mRpR+2H6teMxrptOqA7X+MFDK5GOrOmpJ3IQLaX9ZhmdE64lfifxLcRHiD9C/BHiPyE6
	QPQhRet4qm+l0y4WBLK1nfpMlZ8OwkCgXs2QSLuahrzAQDdNn04t1ZhALXTgh2RKZdBe
	bAYncSPkQwHaqL9WOjuddOwj2gvj3guXZ1NVRZb3j2vHdQbZ+vJCJ1dOYa06eXlTBKF1
	f0XSmkJBr4SCnkHi/AP3efO1GiZ8MjlryRq3Ifxesn3pnbkGAjmlrKB903r/LMuM9s7l
	XPeMem+7Z9nEfGbfrOyWRaVNE7czWwKuefPcXRNDnLhrgV30lLT2ud00/80AXIB8XU8z
	bBbdPKagAz3YGTMYo8BErUJtpwUXz2kMfLkhgTEZdfHOBGOq8eBlF/BPHPlyA6PNd7za
	G50cBeqvmIuTHTkePmnIXfNQeXr4NCZ5ijo3L+V6Ro9NZDLbF+Z3rKsZmBgjFTsctZJj
	Ip24AXOR64EkmHMAWGzYyyTEKULYIBqTlHGKWI2FKWREhk0hL2LiY5yxxuSUEC7ea21d
	ctkGb8jO5O+VfJTW3RuSm1KApXBC24qkZBRl5g8xSWm5cc9VWsPvoba2qHWY60EMn2SZ
	oeqNE59xtQdXZM+W4gMDtZG36VwyAHnggg3iPLVWkWWMY9WcNSamWdMU02D1WRpzjrMq
	c6YlVsPp8zi9yeVKVHKu7BiXKyFFYzHr/ZnKFLfS7zDlx4LZn+AGf57Rnb95KiaOX6AA
	KAE7foHeDXRe2iS90TeEiWPaY7ID3dB7A/aiHArl7cGR5aStkNyFM1gpKe8WUriUDiQV
	NktWGWK/OqPsgY7+7Oxw5EBLy/jx3yAmhf+kMBas6p2XmxvZs6Djb5fCkU/okLanxeIt
	Li40Gmfm1/mGt//+qVc9looKZ5HeMCN7ftu6J4/9/hmWDISQEvmYuYMfJP+Zs0/rShBi
	XbqXcBVw2CPqldCjQEUqmSZBcYFTO+H7ZKfUEMbvtfZJ5nmj6vRE1YUqyT5/8csvb+PV
	FCeLCpPKpPe3khSbLvoekKJU0Nx0KTvQNDqaeV2cOX7zr+cUsiuOYmH4taMTh2ZbEd/k
	lf6iJcwuss/6yBluAfm0kdZsi5iv0Zv0ufoZ+oXKAaXCpNSAQh8fp+HtyTGmOI3TlBpj
	SsfyVGNa+pdOLb2zJHr9E2/ImHu91VUUighteveQd54yyW1of1ZKW7SkmgOf0Dtv+X45
	HYOHz3IW/ez1Hec7CvDPXO3Ezb0F7WvEpcz8iwd38sVJVa4X+l5mHjKTnvIVkU79v+qi
	X+AoMrmgmH63aIBG+umtBeZRdJlPvzwshOvo9wLpQopR0W8BBf0iA03zOpvaWvIaB5av
	GVi9tD/grr15+WKp1uWrkRIdRJQJ0u/V9C4MPyTaTfQi0RGiN4hOE12ghhxRMpGdqDQy
	eVEZTKWRYuDVctM0ed40OTBNXjxNlvG4ov+bppUvnSYvnybL/wdwRfuV08pvmSbfOk1e
	PU2+TZL/CTlIqM0KZW5kc3RyZWFtCmVuZG9iagozNyAwIG9iago1MTg5CmVuZG9iagoz
	OCAwIG9iago8PCAvVHlwZSAvRm9udERlc2NyaXB0b3IgL0FzY2VudCA3NzAgL0NhcEhl
	aWdodCA3NDEgL0Rlc2NlbnQgLTIzMCAvRmxhZ3MgMzIKL0ZvbnRCQm94IFstMTAxOCAt
	NDgxIDE0MzYgMTE1OV0gL0ZvbnROYW1lIC9JT1hJUkwrSGVsdmV0aWNhLUJvbGQgL0l0
	YWxpY0FuZ2xlCjAgL1N0ZW1WIDAgL01heFdpZHRoIDE1MDAgL1hIZWlnaHQgNzIwIC9G
	b250RmlsZTIgMzYgMCBSID4+CmVuZG9iagozOSAwIG9iagpbIDI3OCAwIDAgMCAwIDAg
	MCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAw
	IDAgMCAwCjAgMCAwIDAgMCAwIDI3OCAwIDAgMCAwIDAgNzc4IDAgMCAwIDAgMCAwIDAg
	MCAwIDAgMCAwIDAgMCAwIDAgMCA1NTYgMCAwIDYxMQo1NTYgMCA2MTEgMCAyNzggMCAw
	IDI3OCA4ODkgNjExIDAgMCAwIDM4OSA1NTYgMzMzIDYxMSBdCmVuZG9iagoyNSAwIG9i
	ago8PCAvVHlwZSAvRm9udCAvU3VidHlwZSAvVHJ1ZVR5cGUgL0Jhc2VGb250IC9JT1hJ
	UkwrSGVsdmV0aWNhLUJvbGQgL0ZvbnREZXNjcmlwdG9yCjM4IDAgUiAvV2lkdGhzIDM5
	IDAgUiAvRmlyc3RDaGFyIDMyIC9MYXN0Q2hhciAxMTcgL0VuY29kaW5nIC9NYWNSb21h
	bkVuY29kaW5nCj4+CmVuZG9iago0MCAwIG9iago8PCAvTGVuZ3RoIDQxIDAgUiAvTGVu
	Z3RoMSA4MTkyIC9GaWx0ZXIgL0ZsYXRlRGVjb2RlID4+CnN0cmVhbQp4Ab1Zf3wTx5V/
	M7srrX7YlmTZlvxD0not+YdsbAnsYONiYeRfGByDCbEIJv6Bie3g4IJxgRbqtPwoxuVS
	CLQNaQLtNQ0QijA0EaFwTuKU0MsP0oZcSnuXpElI71K3ac9wKWCpb1bGDf60Of7Ip7t6
	M++9mZ158503b3ZHvWvWtUMM9AMH9UtbelaCctkuAJCCtu6Wnqhs+hjzLW19vY6oLGQB
	cKtW9tzXHZXFhwG0afet2jDxfDyT3+pob1kRLYcbmBd1oCIqkxmYZ3R0966PyqaTmJeu
	Wt02UR7fi7Kju2X9RP/wGyY/0NLdHq1vewrzjJ7Va1k9vGyrMPH1rGmfqE8a0b7XgKA2
	DlaDBu4HNVAw4N0EoP6dNg14LGXleK3I1e25N670ChhFRb53wb8o+c+kn775SfuNTN23
	xL+gQnOzPstV2eFsAD3B8lHdtyZLlOcwiQtBgzsENUhlSIVIbvccC/STJ+AhpANIHHSS
	nbABaQfSd5H4Se4QSqfIziFe9D1LNkAymefT8fbFZqvdotXZfxEiqpOP2X9lee80seLs
	vUusQzGgmaMlB8jjsALs5IfgJBuhGrLIIyeyV9mbsegQ9CD1I3FKSsihIZvXfpbkgpMn
	+IwLbDx52v6hJ8/+gSdEyZD9+cwQj9lzNpR8cfbhtMfs/5Z2n/0s0pFo0eFsrPG0/VDa
	KvseW4g8MmTfnRYi+My3otm6NHz0aXt39j77Co9SPn9fiB4Zshdj+RKfzl40U7IXpr1v
	z88MiQTlvLT59hzPK/YMfBCrObBRp89oT03bYy/BIltaRWYJ0mlymOyHHLJ/yDnP/iyy
	ONwTNdkz94XIl09UZ3mcIbLRV1SdtS+7OtOZPd/uzK7MzER+yUvqLep71HPUXrVbnaV2
	qSV1itosmkSDGCvqRa0oiuoQeWqozK46TY5AGcJy5ISoEoUQ+TEq+dPkqKI8+ozIi1QE
	0RyKvIPOS8AcIkdOGhiHzNMqhVOFyNETUdVRn51nHK8UGCjjMcEUKBEpzIMg+WZIBVsT
	+8osZabZxuJK/z9KmpWSm6n7H18WkhbcV9vQGDycFgh6GRNJC9ysbrnJ/MO8dx0WtZe7
	3bWLNpzo6+laWdEuVzTLFe1IzcGdfR2WYH+rw3G8q4cVOIKcq7m1rYPlLe3BHrndH+yS
	/Y7jfcpzU4pXsuI+2X8cVlYsbjy+0tfuH+rz9VXILf7AidbyNU239LVjsq815X+nr3LW
	2BrWV6vy3JS+mlhxK+urifXVxPpq9bUqfbHBV3Q2lK/tRe90VHTWOoJZDcGahUsbg46W
	gD9EnkClfx0Iw2AQzkCW0A/JfD7YASK/QrrE8vBdkcvCOTCEuyN/4mbhpJ5iRMNlpTAM
	34T9cAxU8CTyWbAcvgPnSReu7WVwEt4kNpiGsZeHEMyHl0kk8jqshH/F+r3wPOyF46DH
	Z7ohAUt3EWdkI8o+5FthS+T7kAEzYRucgWJsdReMRg5FTmDpIrgLDsMRfP7fiUyP8/GR
	H0feBxEWYptbsOT1yPzIMTBBLpRDPWq3wFni5C5FOsACs9C6R+FxOAjPwe/J18jJSEek
	L3Ih8i66qgVSoQHvTeQkeZc7xm+LPBr5n0gYkciCHOy1GfbAD7D9Y3gPY2itIPeTXrKH
	7KU++jV6kt8qJIXHEYdsqMK7GqPyNxCBUzACf4a/kD9SC2fgerkXI4WR/wUd1OIo2Uja
	oQ/v7XjvwjGdJipSQOaSerKJPEz2kl/SHHoXbaRfouvpZa6OW8Zt4H7Jr+WHhEHhOypd
	+ErkdORc5CIkQRrcA2tgM47uebgAY3CNcNhWKnGSWaScLMe7n+ynp8hBcorWk2FygR4m
	b5P3yB/JdSpQPU2gbtpL99Aj9Hn6KtfJ7eW+y73NXeFnC1Q4KHygcqp/HW4N7wi/GpkV
	eTfyCYZYESScmXKog3uhBUfbAzPgqziKo3gfw1kbgRfhvHK/R1JhFD5BFICYSDLxkgV4
	15E7yUrSSR4jz+J9VrHlKsWJoBpqpEk0lTbQVtpN++lF2s+lcDncPG4pdwzvl7g3uevc
	dV7g4/kEvoqvgUG+m38E7yf4J/kh/jWhWJgt1AlLhH5hhzDItQmvC2+qNqt2qYZUf1R9
	jGFxvnq1ehBn5zz67HPoy3+7eJKB1nvhAWgjftIK+3A2DpIWGEDvWkG+gXj1QFakidvM
	VdEC9Iaz8GX01kdgE+zglsHByFvcYfgP9BS2X/fDj/hySBO+jbPzNShAL5q4fdk52VmZ
	LmeGnC45MOSnpiRbLUmJCeZ4k9EQo9dpNaJaJfAcJZBbIVc2O4Ku5iDvkqur85gst6Ci
	5VOKZlzKjmDlrXWCDvZcCxbdUtOHNVdOqemL1vRN1iQGRymU5uU6KmRH8BW/7AiRpQsb
	kf+mXw44gqMKv0DhH1L4GOQlCR9wVFg6/I4gaXZUBCv7OgYqmv15ueSUD+HQ5uWywOED
	HWs4CHNbNmGAhbmsRkUwWfZXBK0y8ljGOStaVgTrFzZW+FMkKYA6VC1qxD7ycjuDaCfs
	1K+QV+wM+aC1mXEtyxqDXEsgSJtZW0Z3MEn2B5M2fmD5m3iTqxj8VGGQOitb2gcqg77m
	nQguE5uZ1DKIUm2DA5ulWwONQbJ1wghmYxdaysyN7gnO5i5HUCOXyx0DXc0ILixqHEr2
	JSvBNwj1jUNWn1UR8nJPWTbPknD0p/Lm5M1h+SzJsjmaf/j1qP4Xwyy3bB55B/PaRZMA
	EIaAXIN2Bh1tSicyGjuTJe0zYaBtJuKEV4DgMDvRnrlBij7DOYOCs6Yl2N9w04wOf9S4
	5i7/kMaarGxC5QGs3zxgKMGZwvoG2TFwBXfrZnn097dqWiY0KqfhCrBCNtGTvhIkLTf5
	PrZZOnHUHRa5g81vnzKnKMuWik8pUGbQMJuDZtzA6xuloCOACnybzK0Ngaa+8TghuwIh
	EtkaAn/aKXxH5e5djsW5zNU6/dg/Cnm5qMiRkJuW66jEniuZrzgGHAM1KwYclY4OdCbe
	qeRY0D4QyEcEGxoRJ1iMPfoCKZNseyBQgu3ks3bwEaw+EMAWuiZawFxR5Y9jpYJc3Ew5
	V33jwsZgvz8l6PMHcBbQfYfrG4PD6LmBANbyTFqKFm/qtEzY7EWbPTlYPj3aCr679GMT
	gYEB1mZDoywFhwcGUgbYeovKIQJTFb4JRQhYFQZ5iPTX47OYyVKKMgeSLKFZAYbpDHTp
	mx6F7+yfjXDRpN345B1obZGC8MzPCeHi20G45LYQnjVp6S0Il6LNsxjCX/jnITz7FoTL
	Phth36TdaOQctNanIFz+OSE893YQ9t8WwhWTlt6CcCXaXMEQrvrnIVx9C8I1n43wvEm7
	0chatHaegvD8zwnhBbeDcN1tIXznpKW3IFyPNt/JEF74z0N40S0IN3w2wosn7UYj70Jr
	FysIL/mcEL77dhBuvC2EA5OW3oLwUrQ5wBC+ZxJhX0oQPh2H+6eEXfjcA/OyT0GOb0qC
	CcppMQC/FkaQfMISsCEdUxVDG+rP47fYo8jXMx3yJzFv5t8DCfnD+PjNcx89fo30oeyA
	IvYZ/v9cFN/pP+vCr3vlEjBVIamjIqYi7spazHX49RYDsYp+Bn4NLIXLZA15mi6nffRl
	fHs/wOfwV4UMYRW+5Jfj6C7g9yaH7ZRFz5LEfNzwkURDCOACEpOR534TAh4JkFf/Bp5V
	el7ifhZbEWCJu8Az3SgZM5HK+V2hG78VzlybG+IXXMdzCRz1CB4/XBR2o43ycZGEyHSf
	nufVel69TwBtlcbQN2IZuTheDGVlY694CuILZ5M7phtl48gLj7h2DXNXB+IDT1x7gLuq
	tOVDm23C9yAdnvDVFfGV/N3C/WkP2DbatpDtVMwRl1rvt37F+pXUn1gFSCdxfGqsVVKn
	WvHsSbDHxaXHawvjBYd9nZSul76qnpm4Oj02M+5B+8z0jCqZ2fHG6Nio4cro+1BWOl5a
	Nmo0FeebkooJ5qbiYiMm0OQpmLvBl8pb9U6jS2eKzQKNWZ1FrHyMQZtFxARM8BTOYCDs
	o/9BaCoylZGiO4oKZ7jkdLVKLSMveU0JZrUqjqhQISVI87Y+N/zgjEX7Np2qcvHPcOXr
	SNbV9zZU/mRH68wVyVzsjexTxNSzuraw4f5NewZrt57uuxC++oOnNla1zy/y3N3FvI0D
	G3rsF3AuKXqBDi756qtJI+kg3De4b/Pf0R7ShjQhrSpLS0CtUhEqajSYaEEtkEHC8Q6z
	Vus0oc4sCE4TVtDpBE6j5VUC0VHCAbWpxRAJ+DT4OaTSaDkBpSd9ppiYpKRk4THymNaq
	jzkoDS7H0x5r3Zhlwfi4ta6i3X+50m+BsqTSstIF4winsbiMARlFMn/7NPcmQy2+tfHD
	KUF+JLB9mmVCwaGCGwm4J+puN5SWqpE8BaSpCZqIjsRPJzIncTLhdr09uvVdmnBp7/jp
	x1+mD9GldMf4l7i2a3NJKFytoHEM/WUUObYu6nwZahvP6zgbHotpRJtWJ+qpXk9B1Uln
	aZJjOdEJ1pjYENGdkPbuwMHUjaHhdYarC8beZ56ADsq8onQU+XF0VJw64wSRY3z+jT2c
	+8ZF7ivXn6d24czJcPnhcOwx7BovCm2RX7FvePRbGV73zU5RbSNbKZdG7MI2siP1aYfg
	E+P4hETO0J24OZHGJRpj+G3pBqMt3mRKUJekcwliTEmyRqayzNlMIVLrM3C8h5tlcMYn
	O7UemzUDDyPvOyF19UTNHmVWj46NGxQXRsNH8Ydmo/2KqrhpYioUb85NkUCf6nS4iFOf
	ovWCKGGiAt5LKCfwurQYL2jsai8RKCbo3W5iKDWUMgdHF3+wiTRBUmK8PI3I6WA0mCRv
	RrxUKBnlTPRv2ZHpMhrQ5zO5y9/+tefFjN8+9XL4vy8T/hwRuPAMurW/oL3u6z8PX//p
	Ky+dJdMk4b36teF3Du4Jvxp+PXwt/MyHhP7wxh/OrHbPO/QGBrIvXrpAEU+CpwZAdiO4
	HCSxU+9nleA6zY1nXCxk0fwCTzzGkPPnz7NQhPg/iusjB+uzKNnnM99BZqqomiSRTFJF
	GqmgJpSGyH5fEnq/mqpFDpeHStRyWi1RiZRjZT8R+GQ9Wwf7fVoNWHX6A1JfFG3m3og3
	y5jPRh2lrBRZHr18+6YXPQXot03otka0ieDv0Y/o5TNvj8edpSXCmetL+SeuzeV/eP0e
	xVnwP5LIReF36CtxkILnYQO+3O24qZwjL9CXxPNa1VwxoSSOSylRa1JpaqrO5OGSbRaP
	zppme0vqWvnp6Y+67GjZqDLJXkiOwQnWOAVXYqzFC2YweUmyiJxBhVySPsFL4ikmVm2K
	F4w8Jmx+lTimTDT+q5GUaDSoqRSdU5MEpkIDsEk3mySO3396949GwnvDR58/+vBZPJZL
	+Sj8p4/eD7/zfyQhVvjg2gvhC+FnLkXgnbfIPJLzBjFc+z7ZcAWPyErD58KvjYWPC8tx
	nth6/QRx0KJ9Lb7CTn2naYN+o4mvNjeaO8wbzbxatBkNBi2JjWOrWCtSlUnPa8xmD5+c
	GKfBBZyQ+HcW8LgRw3h0/Rqiy4BgIG+Kl7x44qOS0Vkh04WZ5C0qPEb3jnz85n+Fvee4
	/vXla8O9ZHDbj4Qz//nSU5HxPfypEnuYW/MQW9N4JC+sV3wqEx72mdQxNaRaCJBGoVNY
	YV4viImn8SDRCikk1VcuSw5Xs+mLpnVmzmSzm1MTOMmWaOZdpgynDTSaFLVNR12pKaLD
	mWB3JnKeuM6U5GzR5czUWrOy35T2Rid2wtMWjI2+gTduUqVl49HhFBuj2xSLrk3ohW4W
	LgmOZoYyLk7ysrMtldpG7CQxKTEpAZdrPnEphTJXNfiDNV9YGU4+R598svu17tYldwtq
	TmeaNqbV83r1iuKN4VnnuNSe3d8rtoW19KBn+fiWJ6fLa/pfXJxdaZbiS5dceciTMj6A
	mDRHLuL7xQeQj2dtYd/y7LhM2eUqii2Uqlytro2xX8rQ3C9aYpOcNBDbEXs4ndPGlqRn
	pGs5PtWyzZyf704tMXN8iVtTQLWxojEj3Z5VUGC0OJNqRGdWstfuNNaAM9/q8R6QuiYi
	NO7Y0WDH4pvJiPsLkhLxmIJFu6TiaePTm76orIIFWdOMdhCpi7rynCpnsovLBTfkTVMy
	IUd0k7R4uxtSEixuYrWQPN4Nmkydmzh1ZBry6mxMbKZULEzERFkhBoMSENka+VtQxJ2f
	4Tzdy7b+TJcCdeGMjOlePkFmqKerEsxJicpcJJh5FiXvIMSmntF2rWfZUO387597YeEg
	MV3/kMw9Hee551LwkaWzLry6d+Fg+Hsfhf+wfz9HF5BLm+p2O2YfWD/d68zLLVz2zM/C
	b1/pK1v7cOsqr6MgP33WfSNjvxjc+Qdex+KlhOsKYyG+t83wJROVDdSUFzW4f8N1yjkF
	/rrKKrINvM4wtmAMd4kxZQNnKwajGvoSbnWyUSrkz4eNPw8bhTPHrv1ZiMXFytbBYdzb
	8rHtBEiEUp+cJGQKMw2cFqhQYtAkcomJZo1Tn2whTrM1yXJA2nvLHoVzpExWKa5HYkRM
	FMgwUCqBhnNZiUR6SwO/HL/H8/OabeHB8ODWGjpXOHOj90DXgaPLH+cGb5wL/2l3+CrR
	7iZxXDHao1yRdjzX/3tXHCo5jC4J+N8G+1chFzx41jwdCvEN/Q78t4OhZVK2E/aOjf+k
	3VW18M7Kpe7q9lV97b2dbS1KjZst+5GpR2pG6kHqR8LgAAeQgkjDSBeQ3kH6mDWNZEBy
	RCYuVMAkT8AxRfZMkb1T5DlT5IopctUUuWaKXDdFvnOKXD9FXjRFbpgiL54i38XkvwKC
	24sTCmVuZHN0cmVhbQplbmRvYmoKNDEgMCBvYmoKNTExMwplbmRvYmoKNDIgMCBvYmoK
	PDwgL1R5cGUgL0ZvbnREZXNjcmlwdG9yIC9Bc2NlbnQgNzcwIC9DYXBIZWlnaHQgNzM3
	IC9EZXNjZW50IC0yMzAgL0ZsYWdzIDMyCi9Gb250QkJveCBbLTk1MSAtNDgxIDE0NDUg
	MTEyMl0gL0ZvbnROYW1lIC9VR1FPRlorSGVsdmV0aWNhIC9JdGFsaWNBbmdsZSAwCi9T
	dGVtViAwIC9NYXhXaWR0aCAxNTAwIC9YSGVpZ2h0IDcxNyAvRm9udEZpbGUyIDQwIDAg
	UiA+PgplbmRvYmoKNDMgMCBvYmoKWyAyNzggMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAg
	MCAwIDAgMCA1NTYgNTU2IDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMAo2NjcgMCAw
	IDAgNjY3IDAgNzc4IDAgMjc4IDAgMCAwIDAgNzIyIDc3OCA2NjcgMCA3MjIgNjY3IDYx
	MSA3MjIgXQplbmRvYmoKMTAgMCBvYmoKPDwgL1R5cGUgL0ZvbnQgL1N1YnR5cGUgL1Ry
	dWVUeXBlIC9CYXNlRm9udCAvVUdRT0ZaK0hlbHZldGljYSAvRm9udERlc2NyaXB0b3IK
	NDIgMCBSIC9XaWR0aHMgNDMgMCBSIC9GaXJzdENoYXIgMzIgL0xhc3RDaGFyIDg1IC9F
	bmNvZGluZyAvTWFjUm9tYW5FbmNvZGluZwo+PgplbmRvYmoKNDQgMCBvYmoKPDwgL0xl
	bmd0aCA0NSAwIFIgL0xlbmd0aDEgMTAxMjAgL0ZpbHRlciAvRmxhdGVEZWNvZGUgPj4K
	c3RyZWFtCngBvToJeFRF0tX9zjmSzEzmvh+TmclFyEECIZGMIYFwGg4hUYNJIJggKGCM
	gAt/XEEkIqsg6AqroCunbIZjdYCFZVlYwHUV72PxWhFd3azu/ngBefPXexMiyef6+f2f
	37431d3V3a+7uqq6urp7Whfc3gRJ0A4MVF/XMG8WqE8wBkBOz5jbMC+Bpxowjs1oa/Un
	cC4dgJkza95NcxO4+BCA1nPTnEU936d+A2Ac19zUMDNRDpcwLmrGjAROBmOc1jy3dWEC
	N+3D+IY5t87oKU89iXju3IaFPf3DGcT9tzTMbUrUD2J/kDbv1ttae/AKjKvnLWjqqU9q
	kL4XgWCuDW4FDdwMAlAw4FsHIHyi9QCLpUo5PjOzdWtvTCn9Eoyiit84/hdq/Cfpd69/
	03QprHtQ/BYzNJfrKzGfIWcA6AmWd+ke7C1Rv8PAFoPJWTEYjVCGUIiQlXW1HdrJFngA
	YRMCAy3kPliEsBLhlwhsb2o7YvvJfXtYMXKALAInGRPRsb4pZofPrtX5Xo4Rft9jvrfs
	Hx4kDpTeB8SxJwk0V2vJJvI4zAQfeQqCZDFUQTp5dG/GHF89Fm2HeQjtCIwaErJ9jzff
	d5hkQ5Al+E0IvCx5xvdx3kDfR3kxSvb4joZjLEZ/8CIWSfEd8Tzm+73nJt9hhJ2Joh0Z
	WOMZ33bPHN9ab4w8use3xhMj+M2Dieh2D376jG9uxnrfzDy1fNz6GN25x1eM5VMjOl/R
	UMlX6DnrGxSOiQTxgZ5xvsy8v/jS8EOs5sdGgxGjz+1Z6xuGRV5PZXgYwkGyg2yATLJh
	T3CM7wAmcbh7R2cMXR8jd+6tSs8LxsjiSFFV+vqMqnAwY5wvmDEyHMb01JPCMuF64Woh
	X8gS0oWQIAkuwSyaRIOYLOpFrSiKQow8vafMxx8kO6EM2bJzr8iLXIz8BjPZg2SXmrnr
	WZEVqQiiORZ/H5WXgDlGdu7DaUIAE8/waoqPkV17E1m7Ij5WSbFqgYEqaQwwBEpECmMg
	Su6P8bDc2lZmLzMNNxaPrPhPQb1acjnM+s+PnXii68dOronu8NRG85VE3FN7ubr9cuI/
	xq23Y1FTeVbW2EmL9rbNmz2rsilQWR+obEKoj97X1myPtjf6/btnz1MK/FEmVN84o1mJ
	G5qi8wJNFdHZgQr/7jb1u37Fs5TitkDFbphVOaVm96xIU8WetkhbZaChonZvY/mCuj59
	rezta0H59/RVrjS2QOmrUf2uX191SnGj0led0led0ldjpFHtSxl8Zcvk8ttaUTv9lS1j
	/dH0ydHRE6+rifobaitiZAtmVtwO3BEwcIcgnWsHJzsIfADxtxDeVmL52vg57gQY5Lnx
	fzElKNT9ClC5rBSOwP2wATqBh22YTofp8AicIrNxbt8A++B14oUctL0sxGAcPE/i8Zdg
	Fvwa67fCUVgHu0GP38wFC5auJsH4YsQjmG6EZfEnIA2Gwj1wCIqx1dXQFd8e34ulk+Ba
	2AE78fs/kwDdzabGfxM/CyJMxDaXYclL8XHxTjBBNpRDNeYug8MkyLwdbwY7lCB1G+Fx
	2Ax/gH+Qn5N98eZ4W/x0/ANUVTu4YTK+S8g+8gHTyd4T3xj/NC4jJ9IhE3uth7XwJLbf
	ie8RNK2V5GbSStaSdTRCf073scs5m9yNfMiAUfhWoVW+FzmwH47Bv+Fb8jm1MwamlTke
	L4z/L+hgLI5SGUkTtOG7At/VOKaDhCe5ZASpJkvIQ2QdeYVm0mtpDb2DLqTnmAnMDcwi
	5hX2NnYPt4p7hNfJX8YPxk/EX8M1wAPXwwJYiqM7CqfhPFwgDLblJkFSQsrJdHzbyQa6
	n2wm+2k1OUJO0x3kPfIh+ZxcpBzVUwvNoq10Ld1Jj9IXmBZmHfNL5j3mS3Y4R7nN3Ed8
	UPir3CivlF+Il8Q/iH+DJlYECSVTDhPgRmjA0c6DwfA/OIpd+Hai1I7BcTilvh8SN3TB
	N8gFICbiJPlkPL4TyDVkFmkhj5ED+B5WafmKoiCohhqpjbrpZNpI59J2+hptZ1xMJjOG
	uY7pxPck8zpzkbnIcmwqa2FHsaNhFTuXfRTfLew2dg/7IlfMDecmcFO5dm4lt4qZwb3E
	vc4v5Vfze/jP+S/QLI4TbhVWoXROoc7+AXX5u4claUh9PtwCM0gFaYT1KI3NpAE6ULtm
	knuRX/MgPV7HLGVG0VzUhsNwJ2rro7AEVjI3wOb4m8wOeAM1ZQ422Q5b2XLwcA+jdH4O
	uahFPW8kIzMjPRwKpgUGSH40+W6X02G3WS3mVJPRkKTXaTWiwHMsQwlkVwZG1vujofoo
	GwpUVQ1U8EADZjRckVGPU9kfHdm3TtSvfNeARX1qRrDmrH41I4makd6axOAvhdKB2f7K
	gD/6l4qAP0aum1iD6fsrArX+aJeaHq+mH1DTSZiWJPzAX2lvrvBHSb2/Mjqyrbmjsr5i
	YDbZH0F2aAdmK4YjAjql4SiMaFiCBhZGKDUqo85ARWXUEcA0ljHByoaZ0eqJNZUVLkmq
	xTzMmlSDfQzMbokinXCffmZg5n2xCDTWK6mGG2qiTENtlNYrbRmzorZARdS2+CP7d+jl
	VOWqKwqjNDiyoaljZDRSfx8yV0HrFaxhFWJjJ/uxWbq8tiZKlvcQodA4GylVyE2sCcH6
	2f6oJlAeaO6YXY/MhUk1e5wRp2p8o1Bds8cRcajIwOz99qUlEo5+/8CrB16txCWSfWki
	/vjuRP7LR5TYvvTY+xiPndTLAKJwIDAa6Yz6Z6idBJDYoUrQNBQ6ZgxFPuFTS3CYLUjP
	iChFnWGCUS44uiHaPvkyGc0VCeLqZ1fs0Tic6iJUXov16zsMw1BSWN8Q8Hd8iat1faDr
	H31zGnpy+KDhS1AKFUH36kqUNFxOtymLZRBH3WwPNCvybVNlinjAXnlFBuIKaxSao2Zc
	wKtrpKi/FjPQm8weGwNNdc1uQlbXxkh8eQwqPPvRR2VunI7F2YqqtVRg/4gMzMaMTAlT
	Odn+kdjzSEVX/B3+jtEzO/wj/c2oTGxQjbGgqaN2EHJwcg3yCaZgj5FaV2+yqbZ2GLYz
	SGkHP8HqHbXYwuyeFjBWswZ1Y6XcbFxMmVB1zcSaaHuFKxqpqEUpoPoeqa6JHkHNra3F
	Wnm9lCLFS1rsPTTnI815mVhekGgFfZd2bKK2o0Npc3JNQIoe6ehwdSjzLYHHCPTPiPRk
	xECporA8Rtqr8VuMApJLlYEUkJCsWoWng1GlL2sU+uw/zOGiXrrxyyFIbZHK4aE/EYeL
	fwyHh/0oDpf0UtqHw6VIc4nC4av+exwe3ofDZT/M4Ugv3Ujk1UhtROVw+U/E4RE/hsMV
	P4rDlb2U9uHwSKS5UuHwqP8eh6v6cHj0D3N4TC/dSORYpHaMyuFxPxGHx/8YDk/4URy+
	ppfSPhyuRpqvUTg88b/H4Ul9ODz5hzk8pZduJPJapHaKyuGpPxGHp/0YDtf8KA7X9lLa
	h8PXIc21Coev7+VwxBWFK+1wez+zCz+5Yb7hCpajp8SZoJwW48Z5B6xGqOCmQif7IXTy
	xTAD808hvhH3YxsRr1bK1PgE7MM6EubvwLzB7G0wCaENN+UlGA9FWIag7N4unwvpcbey
	A3E/7pNwm97noejh//8eFjhsFw+hEBLHTRrQ4o5Hj+c4AMmQgmdU3/cYcc+WCubvKbKA
	Vc0djDuMn+FujpLB5Aw5T2+hx5hy5hF2EnuG03KTuCi/mD8rzBJeFnPELbiRKAdgT+Oe
	lkFayhLnVeIgdCoQRAMeAJ5GUHBMM2diwCIApoUzcEClfmrWAWyFg6lZuXkFRskYRihn
	V8cu/Y07dGFEjB1/Ec8+kHOr5em0gXsNaR8e0ZiNmlSrzebUHCQbcfxmsjGSHIF2dpzB
	YbF+Lc2ZZI8J+cuzsiacH9/lfMfZ9WrXhMqminNQVpaXS6jAGw02a2ogh4RD4VChYUhR
	Kp3+q0GjJuavXbRmZMZQq66u5CD3mvziA3+VP5Df/eIh+dOzS+c8tG3aNST947UkqNJT
	gfTYkJ5UKIroRSOkWpAedlxKqkISHvMhSRrRYbZ8LZXdiYckCiWvdr1zBR2ppiFFRkM4
	xBR4ic1LLAaBZ0Y9njNSoeLRq0O5GdNLDsjTSdHqN4hEpC8eItavbmtacn6+/OYn6+R3
	sQuKeyhgu5D3itwnRNIEL8vqGC8eCWlEr1Yn6qleT4FvoSUaZzIjBsGRlBwjur3SupUJ
	irpLJxi+Gn/+rNFUPAh5U9pdWlbahenuvNxUySIZe4B0soMurWWyLr3G/OziUerjDu2T
	y3fIyZ3YtcoLhY4diGigOGJXqND0UMHfTJw6tWetLkamYc9nruz5rNJp/w4DnczFS8/T
	l7oHnVA76uyeqYx1RvwtZY8MAyAAL0WGu/h7yHLKeIiPu4esdD/j5yJiCmuxMoa51qVW
	mmI1JrH3DDAYvakmk0UYNoCxiEnDnJoADQQYrylGxkYMDJvHlBiCqc6gNs/rSMPDvpv2
	SrPn9QhL4UzX+W5DVw9zusrKFEqRR2pWcR3BgmJTcV7uiEWRbJcEenfQHyJBvUubD6KE
	AQ9sPqEMx+o8Sfmg8Qn5hKMY4BluFjGUGkqVU6OsrLvuqiN1kNDHwAAwGkxSflqqVCgZ
	A2Fe4AP+cMiIKiqFmXMP/zXveNrfnn5e/vs5wp4gHCMPpsvbc5sm3P2cfPF3fzl5mORI
	3IfVt8nvb14rvyC/JF+Qn/2Y0Kcu/fPQrVljtr9KFpD5b5+mqsxO4aRao85bPGXOwkmo
	GKecLDxDUqYrHZSbl1pgDJw6dUqZhliISo27fUXXDkQW81yQC4tVQo1wB3cv8wgTw+OC
	jwXdFmYLSzkuXczQbNN8SzmBE0ROw7xKCcfxeCaqoTSdYYImVBCe44ImzOJY3IYru3CB
	14gcZbUsQ6hW4MWb+Tv5T3iGdyYRbVAHDn3S61LbdFU4peO7Sx0TDOfq7FBWiipbarIV
	E6OtWFwxPieLW2I4viLHnsUaxk5auPi4QSwVS/NyYcH8OjK/DodEJA3OJ8EY2HiUPk9S
	u39FW+Xubvmzo9yh7sH0+e7opbX0gw9kZAZVx5yp6rUW2iLmIWQoTwViI2EyitTg8Ail
	MbIhYjPxvEAFESnHQ14to9USXqSMUvZbjnXqBVGppdWAQ6ffJLUlNEwZBOqYEimKlJiA
	ZaWYZFfkZK1YchyJRsVILSBGlAPB38bP6LlD73WnHKbDuEMXr2O3XBjBPnXxeqQPrymg
	Ov4a9wnOjxRw4RlbRyR7BS5SJ8gf6UnxlJYfIVqGpTCuYYLGTd1unSmPcXrteTqHx/um
	NHvWlSqfMAVdZV2qYueDMwmVWhPkQtZkez5aX1M+cYqYMvCYsukt+SSVYuDQuvLByGKg
	6DRRAuW5C29KbFajQaBSQo9NEpgKDaAoutkkMeyGg2u2HpPXybuO7nroMB71uT6T//XZ
	Wfn9r4klmfvowh/l0/Kzb8fh/TfJGJL5KjFceIIs+hKP3UrlE/KL5+Xd3PQeO/gN8kGL
	9DVEClv0LaZF+sUmtspcY242Lzazgug1GgxakpyiWEetSHmTntWYzXms05qiQcNosX6P
	Yew2omIl7KIhMfVJXV5uXaqUj6dIfAAnKIRDGEn5RYWddN2xL15/V84/wbQvLL9NbiWr
	7tnKHXrn5NPx7rXs/mE+mVnwgKJT1fH7VDlZIAxnIze3MLMti5jlqetdJ1z8GHet6zr3
	TMvtloXuZx0nneJQ0e6iLoeDWJwup9MJxEHA4TVhyuF0OF12C+XTgBi9FqBckpLBSmFp
	WBITHkY4jTfP6MxIy9M40jP6CxnKClDCyvBU3UuYMmUC5WSJOH/ICkNWaVYphseycPLY
	0cKl+0K8QRdKCQrBAfq0fPAne/NJiMdUQCvlE58Bg6AYzgfVuvUI/y40bHVBIZxMeqyY
	SfKjNpgFXmCLhoSH2Iou64IBfaozMz+c9Pv58o6jO1VNWH5wTaBwqTxtTvrmmUeuvxrV
	4p/yp0RHKLto18Ci998mVQmFQBVZfffJpU6jY+md65oJbVO14yX5uPy1/CecGshzvFrh
	FqrzOAwPRUxC0mhSxdWSGq6Fm2leyInWg3gg7AAXcUfKA5I/VG+ab7rdzJi8PrPbwkhe
	q5kNmdKCXtBoXIJXR0Nul+gPWnxBK5OX0uJyZoihYFiLfH5dWpeYTD2ze/x5XPRf7VLN
	VHdChYrRUPUsHcV1yP0s9EjqCGrQYFWXGClfOaPkBS/xEavNarOgmzKIoJ+i6BszatWT
	C66aJTtP0G3b5r44t3HqNE5gdKac81o9qxdmFi+WS04w7nlrflXslbV0c9707mXbCgIL
	2o9PyRhpllJLp375QJ6ruwPtuYRrNtp29MEGR5yE94JAWVGDPgxcpEyQYy/yDnEV2tsJ
	hvPjz+Oqd/6yB6UMDWlG9yBglArZU7LxOdnIHeq88G8uGR0Bhd87cK0ehG0rPmVpJGDj
	wtxQA6NFJR1m0FgZq9WsCeqddhI0O2z2TdK6PmvuZQNUinONGM02a0H+kKJCNIKqEWFC
	DjTfraW1r3Rfn/fc6HvkVfKq5aPpCO7QpdZNszftmv44s+rSCflfa+SviHYNSWGKcayD
	Uf5FSA8Pv4hUPEA2ERohUwi1ErKQO0foTWwzdy/LONJp0MQwLATRnnO4VjM8gySzoqh4
	dpR5jAPyGO8QViNXcPk5bx/fXVyMP4fKGWUtKrOh/SbKKqSsQHjfFdGjHw0MXowSynMr
	cG4dUwNF5lA3f/4CDS0gAUIMZAvZ/F73J690/x1Nu4f98AIOSOElA5PiZ9TT9xTcVZTC
	O5GhmblEa9C59O5wQZWhRTPbIBSLJr2GceULaRqPQe8pyaI5GSXPltCS/MygySBwojs8
	wOaOkQ4UhccnhD05Ouop1JUKpaVus5CRuS3NOdyV4R6TEh7quGr478jDqBz7yXroWRbO
	dym+0NnuY5clg6YDXSHFeigKnNOV06UoNOq1umSkFw2xDEBLFSRFKRLYvegaWf1mdGEH
	wBAqgdNjk1B5MOjxg9Rl4i5cKEhdmirrq0gySSHo+lhIEUp+cCgwAA1GYDgpUKaG0YyV
	sAu0KQPQfVeiUOHgoiGpJHnBhBtr10vN+XMb8yaTfcMt+rsX318iabdxXz95qO12W1Dv
	NWZmh+oyrZohL/xs3aEDD3e8eF326C0PWtx8cpJ70E1kjphtH3jD5HGZk/+0oarqke6H
	3QMYZrmeLw9Eqmb/9t51v04lZxX9bou/ywa5o2AEL8yL5GwRtrrfcDMDxBQv5fDW3sMJ
	Rq3Xo9OZw6LT78wx5JAMMDp8/hXSobrLHszZs6qHCWWKY1lmLDYmuGc3WXmtlTeHiEmL
	gUWwhUiqxhtCZuGiqrAJfRiFFSajmaocsATSEkziLcpkaess+XX9yW+/envxlPziLXTW
	gw/ef+f+0Kij3NHuz8ZPlLvk87IcLQmMX7nkk8Pb333mpYen71bnLN44MafZCeBE/2Fr
	ZNBWB3nEvk3cYWfGiMYNZoYx8x6nkOQx61yCy2UzhE2ECVOj06MN2xxuvIYX9koLlnzn
	SJSO7youVhxmXD9RXzBhSHgUg8EhBvUWbQiSUw04SmOKQXAgxgEjETTujM6aFIIUEwYa
	Ox8iLOEl1Z3ocZl7HGdVX8BqQ+uoqIcloRUFijpQ9C8KBPr6h7ZOw4KlT4/JvXfNvLsd
	nd4vDr58gZhedbMTom/MuHvb3E2bz6y847XjpOAcXpcN41CuQ+NvM10oVx144I5I/pDk
	UcnTkrey211cUDTTFI8BRI9HSNVSj03H5aTmGDKMJqdPF3Y6vL4V0oLyK4fffRZ3N31l
	67S7NVogxK7DsbkxAAcNgdYlhnCAqnRxVCZFvVWl5y3oOtkU769QGRYUDjYVfLVm85LN
	Wxbfu510TM69atcTZU/fule+8Pm75MZP3jj15z+efo4OGewdSz0Xhq+bUUMGXviUTMNx
	LUND0oU3eA6U7vRI3rP8CZ6yvJkPm9v4VoEz66nZbkCtBd6u0zoFdC/0GRqnm+TYMxzg
	cKHp4PdKjYnhoflPaG5CtqUoXmNxMVE2Qgl/1XJ5ogaMypKmzN1kYkH/ddnOcTuaz1Zn
	P+vJXRrJGDN0oGsf2coOemT6pMenPdE9kT7ZWDozyVpeOL+l+0UkFm1fSfwtVkKd1OO9
	sQMeiBQ8Iq43/NL6FLtN3GLYbo2JJ8U32I+S/27WDxN5j13Qe0w6h+BwWGg4xenShC3o
	EsWIBjUzMe3Gq5bsO61M7N3AxoZ0qRoUh5GGiGDDFJeEKa1ZHwJiwEC0oiIyyRgoxkoN
	FM82zaQu2gNU7TPhzo2ijUso3/vLc8cdeGr9+ifxcv6S/PU78iVi+phvJSlb1k9/6NKe
	nWeZt+V/4FTsln9Dsi6hwYso+qc+8Sa8r/6+x4aZjLrXT8f770y8e8+BPLxFLcD1rRCG
	4D1+MVRAJYzEm/GJeO+tnJqYEJSHV86DpowYO7l6RFZV05y2ptaWGQ1qDbUYA9xk4+2y
	cp8KgI4qbEKIIhxBOI3wPsIXSpMIBgQ/Qi5CBKEaoR5hHkI7wgMImxCiCEfiPQ/g05sm
	4O+H43/B+pTj/6L64JX98JH98FH9cFSgPt+P7YdP6Idf0w/HMfX5fmI/XOHvleOZ0g+/
	th8+tR+ucP/K7xv74TP64Yp8rqx/Sz/8VgX/PyhCin4KZW5kc3RyZWFtCmVuZG9iago0
	NSAwIG9iago2NTAyCmVuZG9iago0NiAwIG9iago8PCAvVHlwZSAvRm9udERlc2NyaXB0
	b3IgL0FzY2VudCA3NzAgL0NhcEhlaWdodCA3MzcgL0Rlc2NlbnQgLTIzMCAvRmxhZ3Mg
	MzIKL0ZvbnRCQm94IFstOTUxIC00ODEgMTQ0NSAxMTIyXSAvRm9udE5hbWUgL1RDS1NQ
	QytIZWx2ZXRpY2EgL0l0YWxpY0FuZ2xlIDAKL1N0ZW1WIDAgL01heFdpZHRoIDE1MDAg
	L1hIZWlnaHQgNjM3IC9Gb250RmlsZTIgNDQgMCBSID4+CmVuZG9iago0NyAwIG9iagpb
	IDI3OCAwIDAgMCAwIDAgMCAwIDMzMyAzMzMgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAg
	MCAwIDAgMCAwIDAgMCAwIDAgMCAwCjAgMCAwIDAgNjY3IDYxMSA3NzggMCAyNzggMCA2
	NjcgMCAwIDcyMiA3NzggNjY3IDc3OCA3MjIgMCA2MTEgNzIyIDY2NyAwIDAKMCAwIDAg
	MCAwIDAgMCAwIDU1NiA1NTYgNTAwIDU1NiAwIDAgMCAwIDAgMCAwIDAgMCA1NTYgNTU2
	IF0KZW5kb2JqCjI0IDAgb2JqCjw8IC9UeXBlIC9Gb250IC9TdWJ0eXBlIC9UcnVlVHlw
	ZSAvQmFzZUZvbnQgL1RDS1NQQytIZWx2ZXRpY2EgL0ZvbnREZXNjcmlwdG9yCjQ2IDAg
	UiAvV2lkdGhzIDQ3IDAgUiAvRmlyc3RDaGFyIDMyIC9MYXN0Q2hhciAxMTEgL0VuY29k
	aW5nIC9NYWNSb21hbkVuY29kaW5nCj4+CmVuZG9iago0OCAwIG9iago8PCAvTGVuZ3Ro
	IDQ5IDAgUiAvTGVuZ3RoMSA4NDYwIC9GaWx0ZXIgL0ZsYXRlRGVjb2RlID4+CnN0cmVh
	bQp4Ab06C3iU1ZXn3P85jyQzk0kyj2RmfobJgwTyIkAgNUOYhPCK4SHMoMEkJJBQoinG
	FNiFjS0PiZRFEbTiaqmr8ijNEKgOICy1uIBrFdtqFW3Vimi3zdp2wSow/+z5Z0IK+aof
	37d+/f+ce8+55z7OPfecc+/cP10r7m2FJOgBDuoXNnUugfjjPgSAwcUdTZ0J2vI55S8v
	7u7yJGghF4BbvqRzaUeClh8G0GctXb5qsH3qJwCGlrbWppYEH65SPq6NChI0jqV8ZFtH
	18oEbaHxILD87sWD/NSTGr+jaeXg+PAu0Z67mjpaE/XdWzV+5933dA3SMykPdK5oHayP
	QZLvNUAqNcPdoINvggQMTPQ2AEif6LOAJ67Gp6elwLDtzpSKS2CW4/Sds/41nv+n8sKb
	n7dezTE8KH9BBbpr9bVczFPzAIxI/AHDg0OceDtKzBGYmx+BaQSVBGUE+fmTbdCDz8BW
	gh8QcNCOD8Aqgk0E3yfgh7A9RB3GB/p52X8EV4EDp/sNvHue1e626Q3uX0RQPPSE+23b
	h0fRTqv3Adr7k0A3WY8/wCehBdz4NPhwNdRCLj52MG+5u5FYe6CToIeAi6eIe/pdJe7j
	WAA+HqlNNrh4fM79cfFo90fFEYb97hdzIjxlP3UR5U9xn8h6wv0fWUvdxwn2JVh786jG
	c+49Wcvd21wRfKzf/VBWBKnNg4ns3ixq+py7I2+Hu6U4zp+5I8L29bvLiT/fb3CPm6C4
	y7LOuwtzIjISPTprpntU8c/dI6khVfNQpz6/2Z2Ztc09kViurOqciQRHcS/uhFG4s983
	3X2EUJruwWl5E3ZE8J8O1uYW+yK42j+uNndHXm2OL2+m25dXk5ND+PzT0jrpdmmyVCLl
	S7lStqRITskqW2STnCwbZb0sy1IEf9Rf6RaP4j6oJLXsOyiLshDBH1MhfxT3xwv3Py/z
	MpNBtkZi75PxIlgjuO+QScMIeU6MY2IE9x9MFO33u3kN4+MME9NwSigFhjKD6RDG70VE
	WJ/eXWmrtNxiLq8JfFnSGOdcS/O//LFhVnjHjLnB8N6sULhEQ2JZoWvVbdeQL8277iVW
	a1V+/ow5qw52dy5bUt3qrW70VrcSNIYf6G6zhXuaPZ4Dyzo1hifMZTc2L27T8qbWcKe3
	NRBe5g14DnTH2w1jL9HY3d7AAVhSPS94YIm/NdDf7e+u9jYFQgebq1Y03DDWpqGxVlT9
	nbGqtM5WaGM1x9sNG6tBYzdrYzVoYzVoYzX7m+NjaZOvbp9bdU8XWaenun2GJ5w7Nzxt
	9sJg2NMUCkTwGSoM3AvCCTAJxyBX6AEHXwhugNjbBOe0XL0tdkE4BSa1I/ZnbhIt6mEN
	mFpZASfge7AT+kCE3YTnwiJ4FM7gMvLtO+AQvIkuGEOxl4cIzIRXMBZ7HZbAv1P9LngR
	tsMBMFKbDkgj7hb0xVYT7Se8GdbFfggjYQJsgGNQTr1ugYHYnthB4s6B22Av7KP2/4Ve
	doBPjf04dh5kmE19riPO67GZsT6wQAFUQT2VroPj6OPOxdrABpNIusfhSdgFP4U/4nfw
	UKwt1h07G/uATNUGmTCX3jV4CD/g+vgNscdj/x1TSRO5MIpGbYRt8BT130fvCQqt1fhN
	7MJtuJ352XfYIX69kKFGSQ95MJXeWorK95MGDsNJ+At8gZ8yG2fiuriXYmWx/wUDzKBZ
	ajNphW56N9K7heZ0FEUswilYj2vwYdyOv2Sj2G0syL7NVrILXB13B7eK+yV/D98vbBYe
	FQ3qpdjR2KnYG5ABWXA7rIC1NLsX4SxchMvIUV+Z6MNJWIWL6O3Bneww7sLDrB5P4Fm2
	F9/DD/FTvMIEZmRpLJ91sW1sH3uRvcq1c9u573PvcZf4WwQm7BI+En3SO2qzukl9NTYp
	9kHscwqxMii0MlVQB3dCE822E8bCv9As9tPbR6t2El6CM/H3Q8yEAfictABoQQeW4Cx6
	6/BWXILt+AQeofd4XJbPGC0E0zEzy2CZbC5rZh2sh73BejgnN4qbzi3k+ug9zb3JXeGu
	8AKfyqfxU/lpsJnv4B+j9xl+N9/PvyaUC7cIdcJ8oUfYJGzmFguvC2+Ka8UtYr/4qfgn
	CoszpbulzbQ6Z8hmf0q2/LeHx5EkfQncBYsxgM2wg1ZjFzZBL1lXC95P+uqE3FgDt5ab
	yorIGo7DP5G1PgZrYBN3B+yKvcXthV+TpSynLnvgWb4KsoRHaHW+A0VkRYOvP29UXm5O
	tm+kd4TioZCf6XTYbRnpadZUi9mUZDTodbIkCjzHEAqqvTWNnnB2Y5jP9tbWjtZobxMV
	NF1X0Eiu7AnX3Fgn7NHaNRHrhpp+qrlkWE1/oqZ/qCaaPBVQMbrAU+31hH8e8HoiuHB2
	kPDvBbwhT3ggjs+K41vjeBLhikINPNW2toAnjI2e6nBNd1tvdWNgdAEe9pM69KMLtMDh
	B4PWcRimNK2hAAtTtBrVYYc3UB22ewknHuerbmoJ188OVgecihKiMiqaE6QxRhe0h0lO
	eMDY4m15IOKH5kYNa7ojGOaaQmHWqPVlzg9neAPhjNUf2f5GXsOqN1/HDDNfTVNrb03Y
	3/gAKVcjGzWqaTNRM+Z6qFu2PhQM4/pBITQZl5GkmriJPcHXuMwT1nmrvG29yxpJuTAn
	2O/wO+LBNwz1wX673x4nRhcctq2dpNDsD4+ePHqylk9SbGsT+cffTZT/4oSW29aefJ/y
	GXOGFICaBrzTSM6wZ3F8EC8JO0FLWidA7+IJpCd6QkjTbCd5poQZ2QznCwu+aU3hnrnX
	xGgLJIRrXBbo19kd8U2oKkT1G3tNE2mlqL7J6+m9RLt1o3fgjzeWNA2WiD7TJdCY2kIP
	2UoYm67h3dpm6aNZt9m8bdr6dsfXlGivrfq6AqI11Wgyh620gdcHlbAnRAV0miyYEQFd
	ffAA4pZQBGPrIxDIOkxnVO7ORcQu0EytPUDjEzG6gApGKYSNKfDU0Mg1mq14ej2901p6
	PTWeNjIm3hfPidHaGyokDc4Nkp5gHo3oDzmH0NZQaCL1U6j1Q02oem+Ielg22APl8aLC
	KFUqKqDNlMuuD84OhnsCzrA/EKJVIPM9UR8MnyDLDYWoVvGQpCTxmnbboMwlJHPxKOKX
	Jnqhs0sPdRHq7dX6nBv0KuETvb3OXs3fEnQEYXiBf7AgAloVTeUR7KmntpR5FWd8DRSv
	QmKFNJ2OJZO+ZlF0Zv9qDY8bkptajidpx8U1POFr0nD5zWh44k1peNKQpDdouIJknqRp
	+Bv/OA3fcoOGK79aw/4huUnIySStP67hqq9Jw1NuRsOBm9Jw9ZCkN2i4hmSu1jQ89R+n
	4dobNDztqzU8fUhuEnIGSTs9ruGZX5OGZ92MhutuSsO3Dkl6g4brSeZbNQ3P/sdpeM4N
	Gp771RqeNyQ3CXkbSTsvruH5X5OGF9yMhoM3peHQkKQ3aHghyRzSNHz7kIb9zjBcH4d7
	hoVd+NoD8x3XqZxOSoIFqlg55fPBRdDHfwh9YjksprIz9DvsccLrtXLCDxFPIXwUfw+s
	I5hEdTLoh7iBurl2/2OkXyUvEO2BkPZz/P/5sMH2HP2qEwgXh/qT6DeBjig9gTa+9hgT
	2U2lSZA8VC9lEKPbhPgzln5dnEAex+Fv8TM2k73Mebi93Fv8PD4s5AorhX764VAFwJ+l
	37Ac3YVVJu6n5EI6RBDIpgjAWQKNJpx7NwI8ARAuvQtHqAXA/Pwj1ItAeVFxqVkx5xBU
	8VsiV38nHLs8JcLPukJ3HdS7i1bmGzQOo5ka4Jy/vhaD2Ibc/dwj/KP6PfqILqIXc/UI
	kigik3U6SvQgCbgZOd5j1et9FiqzCoLPQhUMBoHT6XlRQANDDphLkiMY8uvo+C/q9JxA
	1G6/JSkpI8MhPIFP6O3GpF3K5kV0u2Gvu2ibFY3a66pbAxdqAjaozKiorJgVrYhWmMsr
	0WwpL6c/c3nhxjH5a0wz6JTCn3CG+ZOhjWNsgwUcFXAnQ/mDdTeaKiokguIibGiABjRg
	ail6OYXzIrflvYH1H7C0c9ujR598hW1lC9mm6Le5xZenYEStjWujj/Q/QJiBVr3OP1Jy
	8byBc9E1kE526Q2ykRmNDMR2NknnSOZkH9iTkiNoOKhs30STqbtIgteZPpt18TwJUwiV
	lTSNyooBwqPFRalKmmIeBOzjC69u4/KvvsH985UXmVs4dkit2qsm99HQ9CD9fgN+LxE6
	KPfbNCl0g1KI30SHIT6y3hDBBTTyu9ePfF4bdPiA3j7uytVX2OvRwlPxgfqiLXSjBYtj
	b2u/i2EEeOF1/y1OcQOuZ1wWuoUNuCnzOY/gl1P4tHTO1JG+Np2lpJuT+A0jTGZXqsWS
	Jk0cwaXJSRMdOi/zejmXJYIz/CaOL+YmmXypDp++2GUfSRd8Sw8qyzoTAg5omhm4GDUN
	DCpnoLJSk5R0FC8qbxhc7uKiKav8BU4FjJk+Tzb6jE59CcgKJSLwJcg4gTdkJZWAzi2V
	oMAooXvbfDRVmCq0m6L8/Pvua8AGyEhP9Y5B7wgwmyxKychUpUwxe3NESfR6crLNpvHj
	lBzuwiPvFL808nc/ekX9/QXkT6HAqWPZ+p6i1rrvvqxeeeHnp4/jGEX4sP4e9f1d29RX
	1dfVy+rzHyN7+ur/HLs7f/qeX+EK/Na5s1o8QfolDvgQrRkHGdpN8pF4oBqTT/dGmsuy
	wqLi1FKz98yZM5orkv4fJx8cFV9jPXT7reNxgsgkzMAcnIpBJkjIWAR3+jPIwyQmyRy5
	oCjrOb0eRZlxGu8nAu8war6206/Xgd1g/IHSndC25kKkby3T/CJhjJUVhPLkSRvXvFRc
	RL7RQK5hJpmQ/h7/A7tw7L1oynE2UTh2ZSH/zOUp/NNXbo8bJH13iL0hfEK2kgJOumPq
	9RdspMB9Cn/GTstn9OIUOW1iCuecKOkyWWamwVLMOVy2YoM9y/WWsmzJ9cufcIuByoH4
	IpeAI4kWWOcTstOTbSVgBUsJOmTCTCJhGca0EkxllNj1zhIw85Ro64taoj330ZeCjHSz
	SWJKYk0tCljKTKAtutWicPzOow89e1Ldru5/cf/Dx+mqy/kH9c9/OK++/1dMSxY+uvwz
	9az6/LkYvP8WTsdRv0LT5R/iqkt07VShnlJfu6geEBbROmm++DnpQU/yNfnL2o3tllXG
	1Ra+1hq0tllXW3lJdplNJj0mp2iRQi8z0WLkdVZrMe9IT9FRkEhL/ztBImq2ZJQnYoQp
	4QbYUFzUkKqU0C2K6CVjhZxsypSScWV9bPvJP735W7XkFNezsuoetQs3b3hWOPab0z+K
	Rbfxhye6VW7FVs2n6ZqbdhItbuTAw36LlDQNa4UQBoV2ocW6UpDTj9LlnB2cmOmv8iqe
	7EbLtyz3WjmLy23NTOMUV7qVz7aM9LlAp3NKLgPLznTKHl+a25fOFae0Ox15crYvR2/P
	zXtT2Z5Y2EFLm3Vx4Ff0QmVFRWU0MZ1yc0b5tQjeQFaYr4VkpNmMjc+LU0q0+yJRcqEb
	0zPSM9LIXQsxO870clM3P7XiG0tUxym2e3fHax3N8xcIEmewjLmoN/JGqaV8tTrpFJfZ
	+dC/lbtUPdtVvCi6bnepd0XPS/PyaqxKasX8S1uLndFeckKF1o98jvbHsX4Hii6QGC/r
	aC+CK4zzCfwV0S5rm1Gd6eKsixSNLsY3I21lyHtIZgrbXrNSxp9RzS+rZuFY3+W/CMlk
	FJrPjyJ9j6W+DfCCvzMXx7GpbAG3gF/KLeW72Ur5ftzAG3IM49l4YYLcJgjktsj5LDwv
	yJKsk2jzFETKZZ3PojfoGcU29Fno5MEE2cBzKInaBRrtoCDrRR60UlmHks6RxCHtoRE0
	HlS2DG6js2wnTXX2zyhL2JW2j1aQ89MabJw1Jl82nRDI8+OZ6bosEQcUrw5L439eRMef
	WLKa+gV+G7sG1FQm/FXtYn9mr0RfZSXRsdEUdgfZ2To6RAzQraUdHLDIX/y8eEpkvGgV
	c6zdYpckWI3MajNlCRKINoPeITkcYMzTOTJxjC3PDnZnJn0cO6g0V8VDA6n5fHxvSASH
	CtoWzOXlaCm/FqPSbsFSzVZoGTTTGSGJUjIShev2zdzbdr6+4PmsorX+vOkTRjsP4bN8
	4aOL5jy54IfR2eyp5oqWpPSqsm+1R18jYSkyT4q9zSt8He3uNpJ9q7/0UXmH6fvpT/O7
	5WdMe9Ij8mn51/xHyb+3GifKYpZNMmZZDHbJbk9jOSkOpy4nze5wRlB3UFnR8OVhrYCO
	r9mGVF02fWNk2ShlECYkEaa3GrMBTZTI6WI2csmUaHEsEdAomo20xJ1jhJhmTS+10M7F
	lBFAAa1UYu+vL5p55OkdO56iDxJX1b/+Rr2Klo/FLkx5Zseih6/27zvPnVP/qF5Uo+qP
	Mf8qJqNfIPvMiH3KdMJCmu2cnySN0Z9IxghW+n18enkGJybrzQ5yA7plz4O05LQUzs0x
	7mq63e64qixdk5hftKH8ZHyLTjhFYcIrBkzR83SiKTWXpnnN1oz00pJx49OSabPNLjN7
	y0p3P7dvX3ZacZLL6p6Ss3bhgw8KC9U3tkWrJ6QakG3RyfctZS9ti/sPnbG5D/hC8p8y
	7bQ7uFdytF+K2jG3UDvN0umcoscROqtfw+RBTNtRnZihQzJfL7p+/9kX76iP4KoL6meq
	eh5X8YXqRlwlRK9E38GH1LuYT/PZwbN/rJW+W/y9x0yFHH23yaWvIKPoC0wx3aSXQhmM
	py8vs+l7xwL6SqF5voVAe0T6Xg1zqqtm1E/Or21d3t3a1b64KV4jzqZE++4dJGgjWElw
	P8GjBLsJ6GwApwneIviE4HPqWiawEeQSTCCojQ0+xIchHMEzjJ48jK4eRtcMo6cOo8lB
	bui/bhh96zC6fhg9Zxg9bxgdHEbfNYy+exgd/7+B6+a7UuP/H1XfuP4KZW5kc3RyZWFt
	CmVuZG9iago0OSAwIG9iago1MTkwCmVuZG9iago1MCAwIG9iago8PCAvVHlwZSAvRm9u
	dERlc2NyaXB0b3IgL0FzY2VudCA3NzAgL0NhcEhlaWdodCA3MzcgL0Rlc2NlbnQgLTIz
	MCAvRmxhZ3MgMzIKL0ZvbnRCQm94IFstOTUxIC00ODEgMTQ0NSAxMTIyXSAvRm9udE5h
	bWUgL1JFQktQQStIZWx2ZXRpY2EgL0l0YWxpY0FuZ2xlIDAKL1N0ZW1WIDAgL01heFdp
	ZHRoIDE1MDAgL1hIZWlnaHQgNTQwIC9Gb250RmlsZTIgNDggMCBSID4+CmVuZG9iago1
	MSAwIG9iagpbIDI3OCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAw
	IDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgNjY3CjAgMCAwIDY2NyA2MTEgNzc4IDAg
	Mjc4IDAgMCAwIDAgNzIyIDc3OCA2NjcgMCA3MjIgMCA2MTEgMCAwIDAgNjY3IDAgMCAw
	IDAKMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDU1NiA1NTYgMCAwIDAg
	MCAyNzggMCAwIDAgNTAwIF0KZW5kb2JqCjE5IDAgb2JqCjw8IC9UeXBlIC9Gb250IC9T
	dWJ0eXBlIC9UcnVlVHlwZSAvQmFzZUZvbnQgL1JFQktQQStIZWx2ZXRpY2EgL0ZvbnRE
	ZXNjcmlwdG9yCjUwIDAgUiAvV2lkdGhzIDUxIDAgUiAvRmlyc3RDaGFyIDMyIC9MYXN0
	Q2hhciAxMjAgL0VuY29kaW5nIC9NYWNSb21hbkVuY29kaW5nCj4+CmVuZG9iago1MiAw
	IG9iagooTWFjIE9TIFggMTAuNi44IFF1YXJ0eiBQREZDb250ZXh0KQplbmRvYmoKNTMg
	MCBvYmoKKEQ6MjAxMjA2MjExODQ0NTJaMDAnMDAnKQplbmRvYmoKMSAwIG9iago8PCAv
	UHJvZHVjZXIgNTIgMCBSIC9DcmVhdGlvbkRhdGUgNTMgMCBSIC9Nb2REYXRlIDUzIDAg
	UiA+PgplbmRvYmoKeHJlZgowIDU0CjAwMDAwMDAwMDAgNjU1MzUgZiAKMDAwMDAzNjI0
	OSAwMDAwMCBuIAowMDAwMDEwNjQwIDAwMDAwIG4gCjAwMDAwMDI1NDIgMDAwMDAgbiAK
	MDAwMDAxMDQ3NyAwMDAwMCBuIAowMDAwMDAwMDIyIDAwMDAwIG4gCjAwMDAwMDI1MjIg
	MDAwMDAgbiAKMDAwMDAwMjY0NiAwMDAwMCBuIAowMDAwMDAzNjE2IDAwMDAwIG4gCjAw
	MDAwMDQ0ODAgMDAwMDAgbiAKMDAwMDAyMjgxMiAwMDAwMCBuIAowMDAwMDAyNzU2IDAw
	MDAwIG4gCjAwMDAwMDM1OTYgMDAwMDAgbiAKMDAwMDAwMzY1MiAwMDAwMCBuIAowMDAw
	MDA0NDYwIDAwMDAwIG4gCjAwMDAwMDYwNDMgMDAwMDAgbiAKMDAwMDAwNDUxNiAwMDAw
	MCBuIAowMDAwMDA2MDIyIDAwMDAwIG4gCjAwMDAwMDYxNTAgMDAwMDAgbiAKMDAwMDAz
	NTk4MCAwMDAwMCBuIAowMDAwMDEwMjQ2IDAwMDAwIG4gCjAwMDAwMDYyNjEgMDAwMDAg
	biAKMDAwMDAxMDIyNSAwMDAwMCBuIAowMDAwMDEwMzUzIDAwMDAwIG4gCjAwMDAwMzAw
	NDkgMDAwMDAgbiAKMDAwMDAxNzAyNyAwMDAwMCBuIAowMDAwMDEwNTc0IDAwMDAwIG4g
	CjAwMDAwMTExNjggMDAwMDAgbiAKMDAwMDAxMDY4OCAwMDAwMCBuIAowMDAwMDExMTQ2
	IDAwMDAwIG4gCjAwMDAwMTA3OTkgMDAwMDAgbiAKMDAwMDAxMTEyNCAwMDAwMCBuIAow
	MDAwMDExMDg3IDAwMDAwIG4gCjAwMDAwMTA5MjIgMDAwMDAgbiAKMDAwMDAxMTA2NSAw
	MDAwMCBuIAowMDAwMDExMDI4IDAwMDAwIG4gCjAwMDAwMTEyNzQgMDAwMDAgbiAKMDAw
	MDAxNjU1MyAwMDAwMCBuIAowMDAwMDE2NTc0IDAwMDAwIG4gCjAwMDAwMTY4MDUgMDAw
	MDAgbiAKMDAwMDAxNzIwNyAwMDAwMCBuIAowMDAwMDIyNDEwIDAwMDAwIG4gCjAwMDAw
	MjI0MzEgMDAwMDAgbiAKMDAwMDAyMjY1NiAwMDAwMCBuIAowMDAwMDIyOTg2IDAwMDAw
	IG4gCjAwMDAwMjk1NzkgMDAwMDAgbiAKMDAwMDAyOTYwMCAwMDAwMCBuIAowMDAwMDI5
	ODI1IDAwMDAwIG4gCjAwMDAwMzAyMjQgMDAwMDAgbiAKMDAwMDAzNTUwNCAwMDAwMCBu
	IAowMDAwMDM1NTI1IDAwMDAwIG4gCjAwMDAwMzU3NTAgMDAwMDAgbiAKMDAwMDAzNjE1
	NSAwMDAwMCBuIAowMDAwMDM2MjA3IDAwMDAwIG4gCnRyYWlsZXIKPDwgL1NpemUgNTQg
	L1Jvb3QgMjYgMCBSIC9JbmZvIDEgMCBSIC9JRCBbIDxkODRkYWFiM2VkN2FjMWUxZTdm
	MjI3OTM3NzdiYzg3Yz4KPGQ4NGRhYWIzZWQ3YWMxZTFlN2YyMjc5Mzc3N2JjODdjPiBd
	ID4+CnN0YXJ0eHJlZgozNjMyNAolJUVPRgozIDAgb2JqCjw8L1R5cGUgL1BhZ2UgL0Nv
	bnRlbnRzIDUgMCBSIC9NZWRpYUJveCBbMCAwIDU1OSA3ODNdIC9QYXJlbnQgNCAwIFIg
	L1Jlc291cmNlcyA3IDAgUiA+PgplbmRvYmoKMTUgMCBvYmoKPDwvVHlwZSAvUGFnZSAv
	Q29udGVudHMgMTYgMCBSIC9NZWRpYUJveCBbMCAwIDU1OSA3ODNdIC9QYXJlbnQgNCAw
	IFIgL1Jlc291cmNlcyAxOCAwIFIgPj4KZW5kb2JqCjIwIDAgb2JqCjw8L1R5cGUgL1Bh
	Z2UgL0NvbnRlbnRzIDIxIDAgUiAvTWVkaWFCb3ggWzAgMCA1NTkgNzgzXSAvUGFyZW50
	IDQgMCBSIC9SZXNvdXJjZXMgMjMgMCBSID4+CmVuZG9iagoxIDAgb2JqCjw8L0F1dGhv
	ciAoTWFyYyBIb2ZmbWFubikvQ3JlYXRpb25EYXRlIChEOjIwMTExMTAzMTA1NjAwWikv
	Q3JlYXRvciAoT21uaUdyYWZmbGUgUHJvZmVzc2lvbmFsIDUuMy42KS9Nb2REYXRlIChE
	OjIwMTIwNjIxMDg1NjAwWikvUHJvZHVjZXIgNTIgMCBSIC9UaXRsZSAoZmxvdy5ncmFm
	ZmxlKT4+CmVuZG9iagp4cmVmCjEgMQowMDAwMDM3ODc3IDAwMDAwIG4gCjMgMQowMDAw
	MDM3NTYyIDAwMDAwIG4gCjE1IDEKMDAwMDAzNzY2NSAwMDAwMCBuIAoyMCAxCjAwMDAw
	Mzc3NzEgMDAwMDAgbiAKdHJhaWxlcgo8PC9JRCBbPGQ4NGRhYWIzZWQ3YWMxZTFlN2Yy
	Mjc5Mzc3N2JjODdjPiA8ZDg0ZGFhYjNlZDdhYzFlMWU3ZjIyNzkzNzc3YmM4N2M+XSAv
	SW5mbyAxIDAgUiAvUHJldiAzNjMyNCAvUm9vdCAyNiAwIFIgL1NpemUgNTQ+PgpzdGFy
	dHhyZWYKMzgwNjAKJSVFT0YK
	</data>
	<key>QuickLookThumbnail</key>
	<data>
	TU0AKgAACPCAOSBP93wUABWEABowsAB6HAB1xEAP+KAALRcAPaNAANx0AR+PwV3gAIyU
	ABeUSCVSuWS2XS+YTGZTMAO6bTWbg2dStoT0ABSgAB90MAAKjAAHUmMxuOhuVvSoAAGV
	OTymXQJyQSDQgKgBWV8AAWxAAA2WJxUIWkABK2AAW2+VyKSSaUBeaXe8Xm9S+bO6cX6d
	A2zv8AKLDVKqVMGAB8Y2Gw8CZG3XCVVB6YjF3WX1h/urPAAD6GXxTCWUA3l+amq3bNXv
	Xa/YSy+gB07XBzPSWSza4J73Vy9/cF/uziADgv6YO5sx98OqPhoe3nW7HqdXY8R2AB+9
	uZ5ahUSShHpVaY6TCXh1tCPvVyx8QEvrfH5fP4uj7AB7/kACH+fSQPMvL0vW9oAPe/0D
	wRBKWlJBgAG1B4AEFCUEQA9D1AA9j3PhBUOQ6vD8nuAB5nAZAAAGf59AAeJ5MueJ5nqA
	AOg0CzjACAiwguFySAnGjXQqu8BQxAkDQ9IsjJA7Z+gAZBLCuAATniVrdI+ch4I+B4Eo
	+BYDI+06PnaCIeRiMJcgABUzrzH6aSDDMCw3I84Q4ds5qEUocAADB/G4lZem0j4Rgo9w
	JJYeEQsYMJzzwDAMTS3MLQHDU40lBJ50qABxlC+AMHiYbjPOe59o+BICpgdQAg6AAJjI
	ZbfrxNSZzZIc30nWj/HWcRrgAeholIqQDOQfJ9H47R/MIBQEVJE1SH8D74AuEobtfV6Z
	HGXaPmaRyPigW9a27OBhXAAE5naAAo3NBDsWmlx6QIa9egAGo7ryoFA29WtxtumRjX2A
	Bt38AAv4DeagpkZ+DH+pIHQ6uSm0VRl7Q82Zv4mubxQ4uQO4yiyMJcbmPH+xSVl5kYAB
	NkyIIkfWVAAD+WgBlUU4Sta2rkwKOI9iEOtmzznPC25daAAANaGAAEaNEVLUXRj7HQAA
	ZaelZ4akAC0ghVqW48bmQKolRf6827crFUjQgOADU2GGG0gABe2ABmqd4bnMOZ2z+fNy
	XG8AAyMbpVowEIOhJxcEAAjcLqOp6rq6WGvxh/uPDuz1S3247lBLZnLzETAGAcOyTjca
	Omll089IyLx7ysjuw4zgyP0zcUd1HY9lD11dn23bup2vcd33iad13vgeClXf+F4veeJ4
	3k9n5HlebuXmed6NuvNfHYQTenpQSjR7KX7iZcf1zqHP8Z/nH83JAnBGMY18PstibxoF
	64xqE+kgEyV1iXn4f8bnSCIQwAAvCIF83SXi7mcLkVwAA3oGGgNEzxpA83AFdJUCmCxc
	SDARHyn4C4FWLFkP+c8HZIABPuJUpWCQ4BNBALcAMZxPECAQSygVQZKxwj4UCA0Mxyy2
	Q1gOQOBJCRpRDAA+NRLdiKw9QwPVGDaQYQYJGBEfA2CTweJAl4dAySPgVBeR8DwRoTEg
	LkPMUC0QPABG2SsY44CPguAylpspKx2j3c4PoMA4wAAZj0XkzkEGyIIciZp0JH4skfH0
	PKLwRYwyGZWNIVAc0YjiEoiYu5uxtANCYAAGYaEpAGk8o0ijqnHocfaS2QrL5EENkVIs
	lRQ1QjtHQOJEyKGXlEggBxnAA0uAKAgoxtgC1pPWblKeQ8iZWF4HzMkAA5pmH7P6gd6C
	cZiSpA9KuY5MxSzZAANmbiEUJoHNqOkf7kUjNDA0TCacxnswQnITE7x3mlEzKNCWeJMh
	lz3H+AmfSl3zt7AAA+gCKh4jxc05xvzKB1m3BJQsADUkrH8BC0ICYCiPgHasACdMqnnG
	zG7R1MyaCVQQcSbM7zPo/shMqVGiDnyXtZa2YsadMVxJ0LkyFxLgpZA3p024gxbwW08J
	GA0BSXANi1BkR8IonCPgEhmjYj4Hwj0bJubNmxKp7qspwgUEAIG9GSc8aYAAMaxErHlW
	U76oTNTCcYNdxzrDGj4JWiBorR6B0En9XKBVB3Im9UGBsBUEiM0JI+ecDZ0SPwleabOc
	K+SQNMbW22JiMHE1vLCWOX5K3H1grSRU2cRh/zJHykchwHoCzXJhEZl7K0OTzRixolxs
	5o2mtka62EwrZtyeqRVIz2LXk3tjbdnRN4GDeYqh2h1rVUSlL+YM89wF7N0Z6SZfYxqu
	o3iU5E70/olOqafUe47j7FgsvE2Y1Vv7nOWJvBBnzWQADhvdQWBzZTZ11AAD6+xjDHUL
	BJQ1qZPT1DUwAAAN2AyV3mvOgdy7mXNucQ45510g3h22wOvdOko0iwKJlgbCeG8I26w5
	h+UFzcQYjwzhLEmJyVLpwsh7DGKCQX0H2PVch2kUvfAE2UCIFgOWVWSXuq8+Z9zCrAbn
	IZFawEuhQAAEWS6WYgrkNMTgVQAAjHkLVT1gzzm7JUbsdQJw6AABKE8Q2PC80ubEAARu
	aQAAozYAAFWbwAMTG/fEAEEJXGYXKufJLNsIYHIic4fwmVGAXAZiIAAmbqAACcCsj4GQ
	HmyASCoAA+QnC8jzHsvFLkz0UsdceczqzkWOniXKfzLQPwRABnw8mHF8D0EyCVlgCiRk
	qHajBVMwCXjVH+DaPIX1uRKLwv4bY/8Fk0H494fqoTQaPJk57VRdsQOPGkLwUJJx4PyA
	KAJ/CxSMj5WGAxUekx/JcHqCILbJQaRgNdip/JMh0qsAAPKNmYMpF4xbi428ELWWgowf
	fU1pbeGxt+MsQpHxtCsI+FsZ++EFCA4cAC/QAAucTQpiYlxcihopIvtDhhMiI0J4sSrY
	RPygs+JlWDe5o+QkfvcOEldrCVMZVRx0lRs5uHLZlh0l+RTS5aJZZTmWTSWvIo6N1oTR
	KD4AGoZOn/NCQXQABVmtd5Fh5v0k5g9tK2DcLJUBzrwAAg9hPwfqfSWc+5Yw8S+ssiIh
	jS5IoHsrLGXdOI/1A3JubF6fc9fRxOpDJb/rl3Hs9zO6IINmvjM733WPbzxiVYyaPB4a
	8KXhfGKyX78sdv/exCfHaG8mvYP3oUngnBOAALfp+K9p8+rRzwzPXUspXNDlfq0PELGi
	+h7vb2Zw+Ot5L2h8UHp+4izAAHRQAAr+QfP33vzq2LfNHiy8eo3uJPkQEAAADgEAAAMA
	AAABADUAAAEBAAMAAAABAEQAAAECAAMAAAAEAAAJngEDAAMAAAABAAUAAAEGAAMAAAAB
	AAIAAAERAAQAAAABAAAACAESAAMAAAABAAEAAAEVAAMAAAABAAQAAAEWAAMAAAABAEQA
	AAEXAAQAAAABAAAI5wEcAAMAAAABAAEAAAE9AAMAAAABAAIAAAFSAAMAAAABAAEAAAFT
	AAMAAAAEAAAJpgAAAAAACAAIAAgACAABAAEAAQAB
	</data>
	<key>ReadOnly</key>
	<string>NO</string>
	<key>Sheets</key>
	<array>
		<dict>
			<key>ActiveLayerIndex</key>
			<integer>0</integer>
			<key>AutoAdjust</key>
			<true/>
			<key>BackgroundGraphic</key>
			<dict>
				<key>Bounds</key>
				<string>{{0, 0}, {559, 783}}</string>
				<key>Class</key>
				<string>SolidGraphic</string>
				<key>ID</key>
				<integer>2</integer>
				<key>Style</key>
				<dict>
					<key>shadow</key>
					<dict>
						<key>Draws</key>
						<string>NO</string>
					</dict>
					<key>stroke</key>
					<dict>
						<key>Draws</key>
						<string>NO</string>
					</dict>
				</dict>
			</dict>
			<key>CanvasOrigin</key>
			<string>{0, 0}</string>
			<key>ColumnAlign</key>
			<integer>1</integer>
			<key>ColumnSpacing</key>
			<real>36</real>
			<key>DisplayScale</key>
			<string>1.000 cm = 1.000 cm</string>
			<key>GraphicsList</key>
			<array>
				<dict>
					<key>Bounds</key>
					<string>{{71, 384.52}, {18, 18}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>FontInfo</key>
					<dict>
						<key>Color</key>
						<dict>
							<key>w</key>
							<string>0</string>
						</dict>
						<key>Font</key>
						<string>Helvetica</string>
						<key>NSKern</key>
						<real>0.0</real>
						<key>Size</key>
						<real>12</real>
					</dict>
					<key>ID</key>
					<integer>44</integer>
					<key>Shape</key>
					<string>Circle</string>
					<key>Style</key>
					<dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>VerticalPad</key>
						<integer>0</integer>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{45, 342}, {70, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>41</integer>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 RETURN}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{296, 427.039}, {18, 18}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>FontInfo</key>
					<dict>
						<key>Color</key>
						<dict>
							<key>w</key>
							<string>0</string>
						</dict>
						<key>Font</key>
						<string>Helvetica</string>
						<key>NSKern</key>
						<real>0.0</real>
						<key>Size</key>
						<real>12</real>
					</dict>
					<key>ID</key>
					<integer>28</integer>
					<key>Shape</key>
					<string>Circle</string>
					<key>Style</key>
					<dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>VerticalPad</key>
						<integer>0</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>28</integer>
					</dict>
					<key>ID</key>
					<integer>39</integer>
					<key>Points</key>
					<array>
						<string>{305, 407.697}</string>
						<string>{305, 426.539}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>shadow</key>
						<dict>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>36</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>36</integer>
					</dict>
					<key>ID</key>
					<integer>38</integer>
					<key>Points</key>
					<array>
						<string>{305, 365.177}</string>
						<string>{305, 384.02}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>shadow</key>
						<dict>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>35</integer>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{270, 384.52}, {70, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>36</integer>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 RETURN}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{281, 342}, {48, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>35</integer>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>b</key>
								<string>0</string>
								<key>g</key>
								<string>0.5</string>
								<key>r</key>
								<string>1</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
						<key>stroke</key>
						<dict>
							<key>CornerRadius</key>
							<real>9</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 P}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{282, 198}, {48, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>34</integer>
					<key>Magnets</key>
					<array>
						<string>{0, 1}</string>
						<string>{0, -1}</string>
						<string>{1, 0}</string>
						<string>{-1, 0}</string>
					</array>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>b</key>
								<string>0</string>
								<key>g</key>
								<string>0.5</string>
								<key>r</key>
								<string>1</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
						<key>stroke</key>
						<dict>
							<key>CornerRadius</key>
							<real>9</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 P}</string>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>32</integer>
					</dict>
					<key>ID</key>
					<integer>33</integer>
					<key>OrthogonalBarAutomatic</key>
					<true/>
					<key>OrthogonalBarPoint</key>
					<string>{0, 0}</string>
					<key>OrthogonalBarPosition</key>
					<real>-1</real>
					<key>Points</key>
					<array>
						<string>{341.5, 254.339}</string>
						<string>{369, 288}</string>
						<string>{342.5, 294.662}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>2</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>31</integer>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{270, 283.323}, {72, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>32</integer>
					<key>Magnets</key>
					<array>
						<string>{0, 1}</string>
						<string>{0, -1}</string>
						<string>{1, 0}</string>
						<string>{-1, 0}</string>
					</array>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 TARGET}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{271, 243}, {70, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>31</integer>
					<key>Magnets</key>
					<array>
						<string>{0, 1}</string>
						<string>{0, -1}</string>
						<string>{1, 0}</string>
						<string>{-1, 0}</string>
					</array>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 GOTO}</string>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>31</integer>
						<key>Info</key>
						<integer>2</integer>
					</dict>
					<key>ID</key>
					<integer>25</integer>
					<key>Points</key>
					<array>
						<string>{306, 220.677}</string>
						<string>{306, 243}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>shadow</key>
						<dict>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>34</integer>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{45, 243}, {72, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>21</integer>
					<key>Magnets</key>
					<array>
						<string>{0, 1}</string>
						<string>{0, -1}</string>
						<string>{1, 0}</string>
						<string>{-1, 0}</string>
					</array>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 TARGET}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{46, 198}, {70, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>20</integer>
					<key>Magnets</key>
					<array>
						<string>{0, 1}</string>
						<string>{0, -1}</string>
						<string>{1, 0}</string>
						<string>{-1, 0}</string>
					</array>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 GOTO}</string>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>16</integer>
					</dict>
					<key>ID</key>
					<integer>18</integer>
					<key>Points</key>
					<array>
						<string>{305.999, 95.1772}</string>
						<string>{306, 111.823}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>shadow</key>
						<dict>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>14</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>14</integer>
					</dict>
					<key>ID</key>
					<integer>17</integer>
					<key>Points</key>
					<array>
						<string>{306, 50.1772}</string>
						<string>{305.999, 71.5}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>shadow</key>
						<dict>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>15</integer>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{270, 112.323}, {72, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>16</integer>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 INSN 2}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{271, 27}, {70, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>15</integer>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 INSN 1}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{281.999, 72}, {48, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>14</integer>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>b</key>
								<string>0</string>
								<key>g</key>
								<string>0.5</string>
								<key>r</key>
								<string>1</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
						<key>stroke</key>
						<dict>
							<key>CornerRadius</key>
							<real>9</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 P}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{45, 112.323}, {72, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>10</integer>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 INSN 2}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{46, 27}, {70, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>1</integer>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 INSN 1}</string>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>10</integer>
					</dict>
					<key>ID</key>
					<integer>19</integer>
					<key>Points</key>
					<array>
						<string>{81, 50.1772}</string>
						<string>{81, 111.823}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>b</key>
								<string>0</string>
								<key>g</key>
								<string>0.5</string>
								<key>r</key>
								<string>1</string>
							</dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>HeadScale</key>
							<real>0.5</real>
							<key>TailArrow</key>
							<string>0</string>
							<key>Width</key>
							<real>3</real>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>1</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>44</integer>
					</dict>
					<key>ID</key>
					<integer>43</integer>
					<key>Points</key>
					<array>
						<string>{80, 365.177}</string>
						<string>{80, 384.02}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>b</key>
								<string>0</string>
								<key>g</key>
								<string>0.5</string>
								<key>r</key>
								<string>1</string>
							</dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>HeadScale</key>
							<real>0.5</real>
							<key>TailArrow</key>
							<string>0</string>
							<key>Width</key>
							<real>3</real>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>41</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>21</integer>
					</dict>
					<key>ID</key>
					<integer>29</integer>
					<key>OrthogonalBarAutomatic</key>
					<true/>
					<key>OrthogonalBarPoint</key>
					<string>{0, 0}</string>
					<key>OrthogonalBarPosition</key>
					<real>-1</real>
					<key>Points</key>
					<array>
						<string>{116, 209.339}</string>
						<string>{141.929, 234}</string>
						<string>{117, 254.339}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>b</key>
								<string>0</string>
								<key>g</key>
								<string>0.5</string>
								<key>r</key>
								<string>1</string>
							</dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>HeadScale</key>
							<real>0.5</real>
							<key>LineType</key>
							<integer>2</integer>
							<key>TailArrow</key>
							<string>0</string>
							<key>Width</key>
							<real>3</real>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>20</integer>
					</dict>
				</dict>
			</array>
			<key>GridInfo</key>
			<dict>
				<key>SnapsToGrid</key>
				<string>YES</string>
			</dict>
			<key>HPages</key>
			<integer>1</integer>
			<key>HorizontalGuides</key>
			<array>
				<real>27</real>
				<real>198</real>
				<real>342</real>
			</array>
			<key>KeepToScale</key>
			<false/>
			<key>Layers</key>
			<array>
				<dict>
					<key>Lock</key>
					<string>NO</string>
					<key>Name</key>
					<string>Layer 1</string>
					<key>Print</key>
					<string>YES</string>
					<key>View</key>
					<string>YES</string>
				</dict>
			</array>
			<key>LayoutInfo</key>
			<dict>
				<key>Animate</key>
				<string>NO</string>
				<key>circoMinDist</key>
				<real>18</real>
				<key>circoSeparation</key>
				<real>0.0</real>
				<key>layoutEngine</key>
				<string>dot</string>
				<key>neatoSeparation</key>
				<real>0.0</real>
				<key>twopiSeparation</key>
				<real>0.0</real>
			</dict>
			<key>Orientation</key>
			<integer>2</integer>
			<key>PrintOnePage</key>
			<false/>
			<key>RowAlign</key>
			<integer>1</integer>
			<key>RowSpacing</key>
			<real>36</real>
			<key>SheetTitle</key>
			<string>Unconditional</string>
			<key>UniqueID</key>
			<integer>1</integer>
			<key>VPages</key>
			<integer>1</integer>
			<key>VerticalGuides</key>
			<array>
				<real>45</real>
				<real>270</real>
			</array>
		</dict>
		<dict>
			<key>ActiveLayerIndex</key>
			<integer>0</integer>
			<key>AutoAdjust</key>
			<true/>
			<key>BackgroundGraphic</key>
			<dict>
				<key>Bounds</key>
				<string>{{0, 0}, {559, 783}}</string>
				<key>Class</key>
				<string>SolidGraphic</string>
				<key>ID</key>
				<integer>2</integer>
				<key>Style</key>
				<dict>
					<key>shadow</key>
					<dict>
						<key>Draws</key>
						<string>NO</string>
					</dict>
					<key>stroke</key>
					<dict>
						<key>Draws</key>
						<string>NO</string>
					</dict>
				</dict>
			</dict>
			<key>CanvasOrigin</key>
			<string>{0, 0}</string>
			<key>ColumnAlign</key>
			<integer>1</integer>
			<key>ColumnSpacing</key>
			<real>36</real>
			<key>DisplayScale</key>
			<string>1.000 cm = 1.000 cm</string>
			<key>GraphicsList</key>
			<array>
				<dict>
					<key>Bounds</key>
					<string>{{270, 27}, {72, 36}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>FontInfo</key>
					<dict>
						<key>Color</key>
						<dict>
							<key>w</key>
							<string>0</string>
						</dict>
						<key>Font</key>
						<string>Helvetica</string>
						<key>NSKern</key>
						<real>0.0</real>
						<key>Size</key>
						<real>12</real>
					</dict>
					<key>ID</key>
					<integer>49</integer>
					<key>Magnets</key>
					<array>
						<string>{0, 1}</string>
						<string>{0, -1}</string>
						<string>{1, 0}</string>
						<string>{-1, 0}</string>
					</array>
					<key>Shape</key>
					<string>Diamond</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc

\f0\fs24 \cf0 \expnd0\expndtw0\kerning0
not IFxx}</string>
						<key>VerticalPad</key>
						<integer>0</integer>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{270, 170.079}, {70, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>48</integer>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 NEXT}</string>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>ID</key>
					<integer>47</integer>
					<key>OrthogonalBarAutomatic</key>
					<true/>
					<key>OrthogonalBarPoint</key>
					<string>{0, 0}</string>
					<key>OrthogonalBarPosition</key>
					<real>-1</real>
					<key>Points</key>
					<array>
						<string>{342, 45}</string>
						<string>{369, 117}</string>
						<string>{340.5, 182}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>2</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>49</integer>
						<key>Info</key>
						<integer>3</integer>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{270, 212.598}, {72, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>46</integer>
					<key>Magnets</key>
					<array>
						<string>{0, 1}</string>
						<string>{0, -1}</string>
						<string>{1, 0}</string>
						<string>{-1, 0}</string>
					</array>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 TARGET}</string>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>34</integer>
						<key>Info</key>
						<integer>2</integer>
					</dict>
					<key>ID</key>
					<integer>45</integer>
					<key>Points</key>
					<array>
						<string>{306, 63}</string>
						<string>{305.999, 85.3228}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>49</integer>
						<key>Info</key>
						<integer>1</integer>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{45, 27}, {72, 36}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>FontInfo</key>
					<dict>
						<key>Color</key>
						<dict>
							<key>w</key>
							<string>0</string>
						</dict>
						<key>Font</key>
						<string>Helvetica</string>
						<key>NSKern</key>
						<real>0.0</real>
						<key>Size</key>
						<real>12</real>
					</dict>
					<key>ID</key>
					<integer>3</integer>
					<key>Magnets</key>
					<array>
						<string>{0, 1}</string>
						<string>{0, -1}</string>
						<string>{1, 0}</string>
						<string>{-1, 0}</string>
					</array>
					<key>Shape</key>
					<string>Diamond</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc

\f0\fs24 \cf0 \expnd0\expndtw0\kerning0
IFxx}</string>
						<key>VerticalPad</key>
						<integer>0</integer>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{45, 85.0394}, {70, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>41</integer>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 NEXT}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{281.999, 85.3228}, {48, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>34</integer>
					<key>Magnets</key>
					<array>
						<string>{0, 1}</string>
						<string>{0, -1}</string>
						<string>{1, 0}</string>
						<string>{-1, 0}</string>
					</array>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>b</key>
								<string>0</string>
								<key>g</key>
								<string>0.5</string>
								<key>r</key>
								<string>1</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
						<key>stroke</key>
						<dict>
							<key>CornerRadius</key>
							<real>9</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 P}</string>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>ID</key>
					<integer>33</integer>
					<key>OrthogonalBarAutomatic</key>
					<true/>
					<key>OrthogonalBarPoint</key>
					<string>{0, 0}</string>
					<key>OrthogonalBarPosition</key>
					<real>-1</real>
					<key>Points</key>
					<array>
						<string>{340, 138.898}</string>
						<string>{396, 207}</string>
						<string>{342.5, 224}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>2</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>31</integer>
						<key>Info</key>
						<integer>3</integer>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{270, 127.559}, {70, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>31</integer>
					<key>Magnets</key>
					<array>
						<string>{0, 1}</string>
						<string>{0, -1}</string>
						<string>{1, 0}</string>
						<string>{-1, 0}</string>
					</array>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 GOTO}</string>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>21</integer>
					</dict>
					<key>ID</key>
					<integer>29</integer>
					<key>OrthogonalBarAutomatic</key>
					<true/>
					<key>OrthogonalBarPoint</key>
					<string>{0, 0}</string>
					<key>OrthogonalBarPosition</key>
					<real>-1</real>
					<key>Points</key>
					<array>
						<string>{117, 45}</string>
						<string>{144, 117}</string>
						<string>{117, 138.898}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>b</key>
								<string>0</string>
								<key>g</key>
								<string>0.5</string>
								<key>r</key>
								<string>1</string>
							</dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>HeadScale</key>
							<real>0.5</real>
							<key>LineType</key>
							<integer>2</integer>
							<key>TailArrow</key>
							<string>0</string>
							<key>Width</key>
							<real>3</real>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>3</integer>
						<key>Info</key>
						<integer>3</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>ID</key>
					<integer>25</integer>
					<key>Points</key>
					<array>
						<string>{306, 108}</string>
						<string>{306, 127.5}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>shadow</key>
						<dict>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{45, 127.559}, {72, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>21</integer>
					<key>Magnets</key>
					<array>
						<string>{0, 1}</string>
						<string>{0, -1}</string>
						<string>{1, 0}</string>
						<string>{-1, 0}</string>
					</array>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 TARGET}</string>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>41</integer>
					</dict>
					<key>ID</key>
					<integer>19</integer>
					<key>Points</key>
					<array>
						<string>{81, 63}</string>
						<string>{80.3547, 84.5396}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>3</integer>
						<key>Info</key>
						<integer>1</integer>
					</dict>
				</dict>
			</array>
			<key>GridInfo</key>
			<dict>
				<key>SnapsToGrid</key>
				<string>YES</string>
			</dict>
			<key>HPages</key>
			<integer>1</integer>
			<key>HorizontalGuides</key>
			<array>
				<real>27</real>
			</array>
			<key>KeepToScale</key>
			<false/>
			<key>Layers</key>
			<array>
				<dict>
					<key>Lock</key>
					<string>NO</string>
					<key>Name</key>
					<string>Layer 1</string>
					<key>Print</key>
					<string>YES</string>
					<key>View</key>
					<string>YES</string>
				</dict>
			</array>
			<key>LayoutInfo</key>
			<dict>
				<key>Animate</key>
				<string>NO</string>
				<key>circoMinDist</key>
				<real>18</real>
				<key>circoSeparation</key>
				<real>0.0</real>
				<key>layoutEngine</key>
				<string>dot</string>
				<key>neatoSeparation</key>
				<real>0.0</real>
				<key>twopiSeparation</key>
				<real>0.0</real>
			</dict>
			<key>Orientation</key>
			<integer>2</integer>
			<key>PrintOnePage</key>
			<false/>
			<key>RowAlign</key>
			<integer>1</integer>
			<key>RowSpacing</key>
			<real>36</real>
			<key>SheetTitle</key>
			<string>Conditional</string>
			<key>UniqueID</key>
			<integer>3</integer>
			<key>VPages</key>
			<integer>1</integer>
			<key>VerticalGuides</key>
			<array>
				<real>45</real>
				<real>270</real>
			</array>
		</dict>
		<dict>
			<key>ActiveLayerIndex</key>
			<integer>0</integer>
			<key>AutoAdjust</key>
			<true/>
			<key>BackgroundGraphic</key>
			<dict>
				<key>Bounds</key>
				<string>{{0, 0}, {559, 783}}</string>
				<key>Class</key>
				<string>SolidGraphic</string>
				<key>ID</key>
				<integer>2</integer>
				<key>Style</key>
				<dict>
					<key>shadow</key>
					<dict>
						<key>Draws</key>
						<string>NO</string>
					</dict>
					<key>stroke</key>
					<dict>
						<key>Draws</key>
						<string>NO</string>
					</dict>
				</dict>
			</dict>
			<key>CanvasOrigin</key>
			<string>{0, 0}</string>
			<key>ColumnAlign</key>
			<integer>1</integer>
			<key>ColumnSpacing</key>
			<real>36</real>
			<key>DisplayScale</key>
			<string>1.000 cm = 1.000 cm</string>
			<key>GraphicsList</key>
			<array>
				<dict>
					<key>Bounds</key>
					<string>{{270, 49}, {80, 14}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>FitText</key>
					<string>YES</string>
					<key>Flow</key>
					<string>Resize</string>
					<key>FontInfo</key>
					<dict>
						<key>Color</key>
						<dict>
							<key>b</key>
							<string>0.4</string>
							<key>g</key>
							<string>0.4</string>
							<key>r</key>
							<string>0.4</string>
						</dict>
						<key>Font</key>
						<string>Helvetica</string>
						<key>Size</key>
						<real>12</real>
					</dict>
					<key>ID</key>
					<integer>101</integer>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
						</dict>
						<key>stroke</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Pad</key>
						<integer>0</integer>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;\red102\green102\blue102;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\b\fs24 \cf2 Instrumented }</string>
						<key>VerticalPad</key>
						<integer>0</integer>
					</dict>
					<key>Wrap</key>
					<string>NO</string>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{45, 49}, {49, 14}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>FitText</key>
					<string>YES</string>
					<key>Flow</key>
					<string>Resize</string>
					<key>FontInfo</key>
					<dict>
						<key>Color</key>
						<dict>
							<key>b</key>
							<string>0.4</string>
							<key>g</key>
							<string>0.4</string>
							<key>r</key>
							<string>0.4</string>
						</dict>
						<key>Font</key>
						<string>Helvetica</string>
						<key>Size</key>
						<real>12</real>
					</dict>
					<key>ID</key>
					<integer>100</integer>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
						</dict>
						<key>stroke</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Pad</key>
						<integer>0</integer>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;\red102\green102\blue102;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\b\fs24 \cf2 Original }</string>
						<key>VerticalPad</key>
						<integer>0</integer>
					</dict>
					<key>Wrap</key>
					<string>NO</string>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>74</integer>
						<key>Info</key>
						<integer>3</integer>
					</dict>
					<key>ID</key>
					<integer>96</integer>
					<key>OrthogonalBarAutomatic</key>
					<true/>
					<key>OrthogonalBarPoint</key>
					<string>{0, 0}</string>
					<key>OrthogonalBarPosition</key>
					<real>-1</real>
					<key>Points</key>
					<array>
						<string>{369, 353.977}</string>
						<string>{414, 423}</string>
						<string>{369, 481.536}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>2</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>77</integer>
						<key>Info</key>
						<integer>3</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>77</integer>
					</dict>
					<key>ID</key>
					<integer>95</integer>
					<key>Points</key>
					<array>
						<string>{319.5, 322.795}</string>
						<string>{319.5, 342.638}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>93</integer>
						<key>Info</key>
						<integer>1</integer>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{295.5, 300.118}, {48, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>93</integer>
					<key>Magnets</key>
					<array>
						<string>{0, 1}</string>
						<string>{0, -1}</string>
						<string>{1, 0}</string>
						<string>{-1, 0}</string>
					</array>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>b</key>
								<string>0</string>
								<key>g</key>
								<string>0.5</string>
								<key>r</key>
								<string>1</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
						<key>stroke</key>
						<dict>
							<key>CornerRadius</key>
							<real>9</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 P}</string>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>75</integer>
					</dict>
					<key>ID</key>
					<integer>92</integer>
					<key>Points</key>
					<array>
						<string>{319.5, 535.894}</string>
						<string>{319.5, 554.736}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>91</integer>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{295.5, 512.717}, {48, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>91</integer>
					<key>Magnets</key>
					<array>
						<string>{0, 1}</string>
						<string>{0, -1}</string>
						<string>{1, 0}</string>
						<string>{-1, 0}</string>
					</array>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>b</key>
								<string>0</string>
								<key>g</key>
								<string>0.5</string>
								<key>r</key>
								<string>1</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
						<key>stroke</key>
						<dict>
							<key>CornerRadius</key>
							<real>9</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 P}</string>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>74</integer>
					</dict>
					<key>ID</key>
					<integer>90</integer>
					<key>Points</key>
					<array>
						<string>{319.5, 450.354}</string>
						<string>{319.5, 470.197}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>89</integer>
						<key>Info</key>
						<integer>1</integer>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{295.5, 427.677}, {48, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>89</integer>
					<key>Magnets</key>
					<array>
						<string>{0, 1}</string>
						<string>{0, -1}</string>
						<string>{1, 0}</string>
						<string>{-1, 0}</string>
					</array>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>b</key>
								<string>0</string>
								<key>g</key>
								<string>0.5</string>
								<key>r</key>
								<string>1</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
						<key>stroke</key>
						<dict>
							<key>CornerRadius</key>
							<real>9</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 P}</string>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>73</integer>
						<key>Info</key>
						<integer>3</integer>
					</dict>
					<key>ID</key>
					<integer>87</integer>
					<key>OrthogonalBarAutomatic</key>
					<true/>
					<key>OrthogonalBarPoint</key>
					<string>{0, 0}</string>
					<key>OrthogonalBarPosition</key>
					<real>-1</real>
					<key>Points</key>
					<array>
						<string>{355.5, 218.906}</string>
						<string>{396, 234}</string>
						<string>{369, 396.496}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>2</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>78</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>76</integer>
					</dict>
					<key>ID</key>
					<integer>86</integer>
					<key>Points</key>
					<array>
						<string>{319.5, 578.413}</string>
						<string>{319.5, 597.256}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>75</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>91</integer>
					</dict>
					<key>ID</key>
					<integer>85</integer>
					<key>Points</key>
					<array>
						<string>{319.5, 493.374}</string>
						<string>{319.5, 512.717}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>74</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>89</integer>
						<key>Info</key>
						<integer>2</integer>
					</dict>
					<key>ID</key>
					<integer>84</integer>
					<key>Points</key>
					<array>
						<string>{319.5, 407.834}</string>
						<string>{319.5, 427.677}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>73</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>93</integer>
					</dict>
					<key>ID</key>
					<integer>83</integer>
					<key>Points</key>
					<array>
						<string>{319.5, 280.775}</string>
						<string>{319.5, 300.118}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>72</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>72</integer>
					</dict>
					<key>ID</key>
					<integer>82</integer>
					<key>Points</key>
					<array>
						<string>{319.5, 237.406}</string>
						<string>{319.5, 257.098}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>78</integer>
						<key>Info</key>
						<integer>1</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>78</integer>
					</dict>
					<key>ID</key>
					<integer>81</integer>
					<key>Points</key>
					<array>
						<string>{319.5, 181.563}</string>
						<string>{319.5, 200.406}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>71</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>71</integer>
					</dict>
					<key>ID</key>
					<integer>80</integer>
					<key>Points</key>
					<array>
						<string>{319.5, 139.043}</string>
						<string>{319.5, 157.886}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>70</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>70</integer>
					</dict>
					<key>ID</key>
					<integer>79</integer>
					<key>Points</key>
					<array>
						<string>{319.5, 90.5}</string>
						<string>{319.5, 115.366}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>69</integer>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{283.5, 200.906}, {72, 36}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>FontInfo</key>
					<dict>
						<key>Color</key>
						<dict>
							<key>w</key>
							<string>0</string>
						</dict>
						<key>Font</key>
						<string>Helvetica</string>
						<key>NSKern</key>
						<real>0.0</real>
						<key>Size</key>
						<real>12</real>
					</dict>
					<key>ID</key>
					<integer>78</integer>
					<key>Magnets</key>
					<array>
						<string>{0, 1}</string>
						<string>{0, -1}</string>
						<string>{1, 0}</string>
						<string>{-1, 0}</string>
					</array>
					<key>Shape</key>
					<string>Diamond</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc

\f0\fs24 \cf0 \expnd0\expndtw0\kerning0
IFEQ}</string>
						<key>VerticalPad</key>
						<integer>0</integer>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{270, 342.638}, {99, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>77</integer>
					<key>Magnets</key>
					<array>
						<string>{0, 1}</string>
						<string>{0, -1}</string>
						<string>{1, 0}</string>
						<string>{-1, 0}</string>
					</array>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 GOTO}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{310.5, 597.756}, {18, 18}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>FontInfo</key>
					<dict>
						<key>Color</key>
						<dict>
							<key>w</key>
							<string>0</string>
						</dict>
						<key>Font</key>
						<string>Helvetica</string>
						<key>NSKern</key>
						<real>0.0</real>
						<key>Size</key>
						<real>12</real>
					</dict>
					<key>ID</key>
					<integer>76</integer>
					<key>Shape</key>
					<string>Circle</string>
					<key>Style</key>
					<dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>VerticalPad</key>
						<integer>0</integer>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{270, 555.236}, {99, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>75</integer>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 RETURN}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{270, 470.197}, {99, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>74</integer>
					<key>Magnets</key>
					<array>
						<string>{0, 1}</string>
						<string>{0, -1}</string>
						<string>{1, 0}</string>
						<string>{-1, 0}</string>
					</array>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 INVOKE d()}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{270, 385.157}, {99, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>73</integer>
					<key>Magnets</key>
					<array>
						<string>{0, 1}</string>
						<string>{0, -1}</string>
						<string>{1, 0}</string>
						<string>{-1, 0}</string>
					</array>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 INVOKE c()}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{270, 257.598}, {99, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>72</integer>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 INVOKE b()}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{270, 158.386}, {99, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>71</integer>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 INVOKE cond()}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{270, 115.866}, {99, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>70</integer>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 INVOKE a()}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{310.5, 72}, {18, 18}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>FontInfo</key>
					<dict>
						<key>Color</key>
						<dict>
							<key>w</key>
							<string>0</string>
						</dict>
						<key>Font</key>
						<string>Helvetica</string>
						<key>NSKern</key>
						<real>0.0</real>
						<key>Size</key>
						<real>12</real>
					</dict>
					<key>ID</key>
					<integer>69</integer>
					<key>Shape</key>
					<string>Circle</string>
					<key>Style</key>
					<dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>VerticalPad</key>
						<integer>0</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>54</integer>
						<key>Info</key>
						<integer>3</integer>
					</dict>
					<key>ID</key>
					<integer>68</integer>
					<key>OrthogonalBarAutomatic</key>
					<true/>
					<key>OrthogonalBarPoint</key>
					<string>{0, 0}</string>
					<key>OrthogonalBarPosition</key>
					<real>-1</real>
					<key>Points</key>
					<array>
						<string>{144, 311.457}</string>
						<string>{189, 360}</string>
						<string>{144, 396.496}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>2</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>57</integer>
						<key>Info</key>
						<integer>3</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>53</integer>
						<key>Info</key>
						<integer>3</integer>
					</dict>
					<key>ID</key>
					<integer>67</integer>
					<key>OrthogonalBarAutomatic</key>
					<true/>
					<key>OrthogonalBarPoint</key>
					<string>{0, 0}</string>
					<key>OrthogonalBarPosition</key>
					<real>-1</real>
					<key>Points</key>
					<array>
						<string>{130.5, 218.906}</string>
						<string>{171, 243}</string>
						<string>{144, 353.977}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>2</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>58</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>56</integer>
					</dict>
					<key>ID</key>
					<integer>66</integer>
					<key>Points</key>
					<array>
						<string>{94.5, 450.854}</string>
						<string>{94.5, 469.697}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>55</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>55</integer>
					</dict>
					<key>ID</key>
					<integer>65</integer>
					<key>Points</key>
					<array>
						<string>{94.5, 408.334}</string>
						<string>{94.5, 427.177}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>54</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>54</integer>
					</dict>
					<key>ID</key>
					<integer>64</integer>
					<key>Points</key>
					<array>
						<string>{94.5, 365.815}</string>
						<string>{94.5, 385.157}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>53</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>57</integer>
					</dict>
					<key>ID</key>
					<integer>63</integer>
					<key>Points</key>
					<array>
						<string>{94.5, 280.775}</string>
						<string>{94.5, 300.118}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>52</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>52</integer>
					</dict>
					<key>ID</key>
					<integer>62</integer>
					<key>Points</key>
					<array>
						<string>{94.5, 237.406}</string>
						<string>{94.5, 257.098}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>58</integer>
						<key>Info</key>
						<integer>1</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>58</integer>
					</dict>
					<key>ID</key>
					<integer>61</integer>
					<key>Points</key>
					<array>
						<string>{94.5, 181.563}</string>
						<string>{94.5, 200.406}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>51</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>51</integer>
					</dict>
					<key>ID</key>
					<integer>60</integer>
					<key>Points</key>
					<array>
						<string>{94.5, 139.043}</string>
						<string>{94.5, 157.886}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>50</integer>
					</dict>
				</dict>
				<dict>
					<key>Class</key>
					<string>LineGraphic</string>
					<key>Head</key>
					<dict>
						<key>ID</key>
						<integer>50</integer>
					</dict>
					<key>ID</key>
					<integer>59</integer>
					<key>Points</key>
					<array>
						<string>{94.5, 90.5}</string>
						<string>{94.5, 115.366}</string>
					</array>
					<key>Style</key>
					<dict>
						<key>stroke</key>
						<dict>
							<key>HeadArrow</key>
							<string>FilledArrow</string>
							<key>LineType</key>
							<integer>1</integer>
							<key>TailArrow</key>
							<string>0</string>
						</dict>
					</dict>
					<key>Tail</key>
					<dict>
						<key>ID</key>
						<integer>44</integer>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{58.5, 200.906}, {72, 36}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>FontInfo</key>
					<dict>
						<key>Color</key>
						<dict>
							<key>w</key>
							<string>0</string>
						</dict>
						<key>Font</key>
						<string>Helvetica</string>
						<key>NSKern</key>
						<real>0.0</real>
						<key>Size</key>
						<real>12</real>
					</dict>
					<key>ID</key>
					<integer>58</integer>
					<key>Magnets</key>
					<array>
						<string>{0, 1}</string>
						<string>{0, -1}</string>
						<string>{1, 0}</string>
						<string>{-1, 0}</string>
					</array>
					<key>Shape</key>
					<string>Diamond</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc

\f0\fs24 \cf0 \expnd0\expndtw0\kerning0
IFEQ}</string>
						<key>VerticalPad</key>
						<integer>0</integer>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{45, 300.118}, {99, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>57</integer>
					<key>Magnets</key>
					<array>
						<string>{0, 1}</string>
						<string>{0, -1}</string>
						<string>{1, 0}</string>
						<string>{-1, 0}</string>
					</array>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 GOTO}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{85.5, 470.197}, {18, 18}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>FontInfo</key>
					<dict>
						<key>Color</key>
						<dict>
							<key>w</key>
							<string>0</string>
						</dict>
						<key>Font</key>
						<string>Helvetica</string>
						<key>NSKern</key>
						<real>0.0</real>
						<key>Size</key>
						<real>12</real>
					</dict>
					<key>ID</key>
					<integer>56</integer>
					<key>Shape</key>
					<string>Circle</string>
					<key>Style</key>
					<dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>VerticalPad</key>
						<integer>0</integer>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{45, 427.677}, {99, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>55</integer>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 RETURN}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{45, 385.157}, {99, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>54</integer>
					<key>Magnets</key>
					<array>
						<string>{0, 1}</string>
						<string>{0, -1}</string>
						<string>{1, 0}</string>
						<string>{-1, 0}</string>
					</array>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 INVOKE d()}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{45, 342.638}, {99, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>53</integer>
					<key>Magnets</key>
					<array>
						<string>{0, 1}</string>
						<string>{0, -1}</string>
						<string>{1, 0}</string>
						<string>{-1, 0}</string>
					</array>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 INVOKE c()}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{45, 257.598}, {99, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>52</integer>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 INVOKE b()}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{45, 158.386}, {99, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>51</integer>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 INVOKE cond()}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{45, 115.866}, {99, 22.6772}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>50</integer>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>catalog</key>
								<string>System</string>
								<key>name</key>
								<string>controlHighlightColor</string>
							</dict>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>Text</key>
						<string>{\rtf1\ansi\ansicpg1252\cocoartf1038\cocoasubrtf360
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\qc\pardirnatural

\f0\fs24 \cf0 INVOKE a()}</string>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{85.5, 72}, {18, 18}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>FontInfo</key>
					<dict>
						<key>Color</key>
						<dict>
							<key>w</key>
							<string>0</string>
						</dict>
						<key>Font</key>
						<string>Helvetica</string>
						<key>NSKern</key>
						<real>0.0</real>
						<key>Size</key>
						<real>12</real>
					</dict>
					<key>ID</key>
					<integer>44</integer>
					<key>Shape</key>
					<string>Circle</string>
					<key>Style</key>
					<dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>2.6766984462738037</real>
						</dict>
					</dict>
					<key>Text</key>
					<dict>
						<key>VerticalPad</key>
						<integer>0</integer>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{27, 36}, {189, 594}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>97</integer>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
						</dict>
						<key>stroke</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>b</key>
								<string>0.752941</string>
								<key>g</key>
								<string>0.752941</string>
								<key>r</key>
								<string>0.752941</string>
							</dict>
						</dict>
					</dict>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{252, 36}, {189, 594}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>98</integer>
					<key>Shape</key>
					<string>Rectangle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
						</dict>
						<key>stroke</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>b</key>
								<string>0.752941</string>
								<key>g</key>
								<string>0.752941</string>
								<key>r</key>
								<string>0.752941</string>
							</dict>
						</dict>
					</dict>
				</dict>
			</array>
			<key>GridInfo</key>
			<dict>
				<key>SnapsToGrid</key>
				<string>YES</string>
			</dict>
			<key>HPages</key>
			<integer>1</integer>
			<key>HorizontalGuides</key>
			<array>
				<real>27</real>
			</array>
			<key>KeepToScale</key>
			<false/>
			<key>Layers</key>
			<array>
				<dict>
					<key>Lock</key>
					<string>NO</string>
					<key>Name</key>
					<string>Layer 1</string>
					<key>Print</key>
					<string>YES</string>
					<key>View</key>
					<string>YES</string>
				</dict>
			</array>
			<key>LayoutInfo</key>
			<dict>
				<key>Animate</key>
				<string>NO</string>
				<key>circoMinDist</key>
				<real>18</real>
				<key>circoSeparation</key>
				<real>0.0</real>
				<key>layoutEngine</key>
				<string>dot</string>
				<key>neatoSeparation</key>
				<real>0.0</real>
				<key>twopiSeparation</key>
				<real>0.0</real>
			</dict>
			<key>Orientation</key>
			<integer>2</integer>
			<key>PrintOnePage</key>
			<false/>
			<key>RowAlign</key>
			<integer>1</integer>
			<key>RowSpacing</key>
			<real>36</real>
			<key>SheetTitle</key>
			<string>Example</string>
			<key>UniqueID</key>
			<integer>4</integer>
			<key>VPages</key>
			<integer>1</integer>
			<key>VerticalGuides</key>
			<array>
				<real>45</real>
				<real>270</real>
			</array>
		</dict>
	</array>
	<key>SmartAlignmentGuidesActive</key>
	<string>YES</string>
	<key>SmartDistanceGuidesActive</key>
	<string>YES</string>
	<key>UseEntirePage</key>
	<false/>
	<key>WindowInfo</key>
	<dict>
		<key>CurrentSheet</key>
		<integer>0</integer>
		<key>ExpandedCanvases</key>
		<array>
			<dict>
				<key>name</key>
				<string>Unconditional</string>
			</dict>
			<dict>
				<key>name</key>
				<string>Conditional</string>
			</dict>
		</array>
		<key>Frame</key>
		<string>{{14, 4}, {1196, 874}}</string>
		<key>ListView</key>
		<false/>
		<key>OutlineWidth</key>
		<integer>142</integer>
		<key>RightSidebar</key>
		<true/>
		<key>ShowRuler</key>
		<true/>
		<key>Sidebar</key>
		<true/>
		<key>SidebarWidth</key>
		<integer>120</integer>
		<key>VisibleRegion</key>
		<string>{{-172, 0}, {904, 720}}</string>
		<key>Zoom</key>
		<real>1</real>
		<key>ZoomValues</key>
		<array>
			<array>
				<string>Unconditional</string>
				<real>1</real>
				<real>1</real>
			</array>
			<array>
				<string>Conditional</string>
				<real>1</real>
				<real>2</real>
			</array>
			<array>
				<string>Example</string>
				<real>1</real>
				<real>1</real>
			</array>
		</array>
	</dict>
	<key>saveQuickLookFiles</key>
	<string>YES</string>
</dict>
</plist>
