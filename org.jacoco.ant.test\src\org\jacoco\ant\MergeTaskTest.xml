<?xml version="1.0" encoding="UTF-8"?>

<!--
   Copyright (c) 2009, 2025 Mountainminds GmbH & Co. KG and Contributors
   This program and the accompanying materials are made available under
   the terms of the Eclipse Public License 2.0 which is available at
   http://www.eclipse.org/legal/epl-2.0

   SPDX-License-Identifier: EPL-2.0

   Contributors:
      <PERSON> - initial API and implementation
-->

<project name="JaCoCo Merge Task Tests" xmlns:au="antlib:org.apache.ant.antunit" xmlns:jacoco="antlib:org.jacoco.ant">

	<target name="setUp">
		<tempfile property="temp.dir" prefix="jacocoTest" destdir="${java.io.tmpdir}" />
		<mkdir dir="${temp.dir}"/>
		<property name="exec.file" location="${temp.dir}/exec.file" />
	</target>

	<target name="tearDown">
		<delete dir="${temp.dir}" quiet="false" failonerror="true"/>
	</target>

	<target name="testMergeNoDestination">
		<au:expectfailure expectedMessage="Destination file must be supplied">
			<jacoco:merge/>
		</au:expectfailure>
	</target>

	<target name="testMergeToDirectory">
		<au:expectfailure expectedMessage="Unable to write merged file ${temp.dir}">
			<jacoco:merge destfile="${temp.dir}"/>
		</au:expectfailure>
	</target>

	<target name="testMergeEmptySet">
		<jacoco:merge destfile="${exec.file}"/>

		<au:assertFileExists file="${exec.file}"/>
	</target>

	<target name="testMergeMultipleFiles">
		<jacoco:merge destfile="${exec.file}">
			<fileset dir="${basedir}/data" includes="*.exec"/>
		</jacoco:merge>

		<property name="sample1.file" location="${basedir}/data/sample1.exec"/>
		<property name="sample2.file" location="${basedir}/data/sample2.exec"/>
		<au:assertLogContains text="Loading execution data file ${sample1.file}"/>
		<au:assertLogContains text="Loading execution data file ${sample2.file}"/>
		<au:assertLogContains text="Writing merged execution data to ${exec.file}"/>
		<au:assertFileExists file="${exec.file}"/>
	</target>

	<target name="testMergeBadFiles">
		<property name="bad.file" location="${basedir}/data/sample.bad"/>
		<au:expectfailure expectedMessage="Unable to read ${bad.file}">
		<jacoco:merge destfile="${exec.file}">
			<file file="${basedir}/data/sample.bad"/>
		</jacoco:merge>
		</au:expectfailure>
	</target>

	<target name="testMergeDirectory">
		<jacoco:merge destfile="${exec.file}">
			<dirset dir="${basedir}/data"/>
		</jacoco:merge>

		<au:assertFileExists file="${exec.file}"/>
	</target>
</project>
