<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <link rel="stylesheet" href="resources/doc.css" charset="UTF-8" type="text/css" />
  <link rel="shortcut icon" href="resources/report.gif" type="image/gif" />
  <title>JaCoCo - API Usage Examples</title>
</head>
<body>

<div class="breadcrumb">
  <a href="../index.html" class="el_report">JaCoCo</a> &gt;
  <a href="index.html" class="el_group">Documentation</a> &gt;
  <span class="el_source">API Usage Examples</span>
</div>
<div id="content">

<h1>API Usage Examples</h1>

<p>
  To get familiar with the API these examples demonstrate different aspects of
  the JaCoCo API. Each example can be separately compiled and executed as a Java
  main program. Some examples require additional command line arguments.
</p>

<p>
  To compile and run these example you need
  <a href="https://asm.ow2.io/">ASM</a> ${asm.version} in addition to the JaCoCo
  libraries.
</p>

<table class="coverage">
  <thead>
    <tr>
      <td>File</td>
      <td>Description</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><a class="el_source" href="examples/java/CoreTutorial.java">CoreTutorial.java</a></td>
      <td>This tutorial-like example instruments, executes and analyzes a single
          target class. Finally line coverage information is printed to the
          console.
      </td>
    </tr>
    <tr>
      <td><a class="el_source" href="examples/java/ClassInfo.java">ClassInfo.java</a></td>
      <td>This example writes JaCoCo specific information for given Java class
          files.
      </td>
    </tr>
    <tr>
      <td><a class="el_source" href="examples/java/ExecDump.java">ExecDump.java</a></td>
      <td>Utility to dump the content of execution data files in readable form.
      </td>
    </tr>
    <tr>
      <td><a class="el_source" href="examples/java/MBeanClient.java">MBeanClient.java</a></td>
      <td>This example connects to a coverage agent to collect execution data
          over the JMX.
      </td>
    </tr>
    <tr>
      <td><a class="el_source" href="examples/java/ExecutionDataClient.java">ExecutionDataClient.java</a></td>
      <td>This example connects to a coverage agent to collect execution data
          over the remote protocol.
      </td>
    </tr>
    <tr>
      <td><a class="el_source" href="examples/java/ExecutionDataServer.java">ExecutionDataServer.java</a></td>
      <td>This example starts a socket server to collect execution data from
          agents over the remote protocol.
      </td>
    </tr>
    <tr>
      <td><a class="el_source" href="examples/java/ReportGenerator.java">ReportGenerator.java</a></td>
      <td>This example generates HTML reports based on a simple project layout and well known execution
          data store file name.</td>
    </tr>
  </tbody>
</table>


</div>
<div class="footer">
  <span class="right"><a href="${jacoco.home.url}">JaCoCo</a> ${qualified.bundle.version}</span>
  <a href="license.html">Copyright</a> &copy; ${copyright.years} Mountainminds GmbH &amp; Co. KG and Contributors
</div>

</body>
</html>
