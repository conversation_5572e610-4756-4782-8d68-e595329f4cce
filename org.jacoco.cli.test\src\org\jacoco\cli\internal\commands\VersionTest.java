/*******************************************************************************
 * Copyright (c) 2009, 2025 Mountainminds GmbH & Co. KG and Contributors
 * This program and the accompanying materials are made available under
 * the terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 * Contributors:
 *    Marc R<PERSON> Hoffmann - initial API and implementation
 *
 *******************************************************************************/
package org.jacoco.cli.internal.commands;

import org.jacoco.cli.internal.CommandTestBase;
import org.jacoco.core.JaCoCo;
import org.junit.Test;

/**
 * Unit tests for {@link Version}.
 */
public class VersionTest extends CommandTestBase {

	@Test
	public void should_print_version() throws Exception {
		execute("version");

		assertOk();
		assertContains(JaCoCo.VERSION, out);
	}

}
