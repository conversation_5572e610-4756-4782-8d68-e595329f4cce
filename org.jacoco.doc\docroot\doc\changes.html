<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <link rel="stylesheet" href="resources/doc.css" charset="UTF-8" type="text/css" />
  <link rel="shortcut icon" href="resources/report.gif" type="image/gif" />
  <title>JaCoCo - Change History</title>
</head>
<body>

<div class="breadcrumb">
  <a href="../index.html" class="el_report">JaCoCo</a> &gt;
  <a href="index.html" class="el_group">Documentation</a> &gt;
  <span class="el_source">Change History</span>
</div>
<div id="content">

<h1>Change History</h1>

<h2>Snapshot Build @qualified.bundle.version@ (@build.date@)</h2>

<h3>New Features</h3>
<ul>
  <li>Experimental support for Java 26 class files
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1807">#1870</a>).</li>
  <li>Branches added by the Kotlin compiler for default argument number 33 or higher
      are filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1655">#1655</a>).</li>
  <li>Part of bytecode generated by the Kotlin compiler for elvis operator that
      follows safe call operator is filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1814">#1814</a>).</li>
</ul>

<h3>Fixed bugs</h3>
<ul>
  <li>Fixed handling of implicit <code>else</code> clause of <code>when</code> with
      <code>String</code> subject in Kotlin
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1813">#1813</a>).</li>
  <li>Fixed handling of implicit <code>default</code> clause of <code>switch</code>
      by <code>String</code> in Java when compiled by ECJ
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1813">#1813</a>).</li>
  <li>Fixed handling of exceptions in chains of safe call operators in Kotlin
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1819">#1819</a>).</li>
</ul>

<h2>Release 0.8.13 (2025/04/02)</h2>

<h3>New Features</h3>
<ul>
  <li>JaCoCo now officially supports Java 23 and Java 24
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1757">#1757</a>,
      <a href="https://github.com/jacoco/jacoco/issues/1631">#1631</a>,
      <a href="https://github.com/jacoco/jacoco/issues/1867">#1867</a>).</li>
  <li>Experimental support for Java 25 class files
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1807">#1807</a>).</li>
  <li>Calculation of line coverage for Kotlin <code>inline</code> functions
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1670">#1670</a>).</li>
  <li>Calculation of line coverage for Kotlin <code>inline</code> functions
      with <code>reified</code> type parameter
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1670">#1670</a>,
      <a href="https://github.com/jacoco/jacoco/issues/1700">#1700</a>).</li>
  <li>Calculation of coverage for Kotlin <code>JvmSynthetic</code> functions
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1700">#1700</a>).</li>
  <li>Part of bytecode generated by the Kotlin Compose compiler plugin is
      filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1616">#1616</a>).</li>
  <li>Part of bytecode generated by the Kotlin compiler for inline value classes is
      filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1475">#1475</a>).</li>
  <li>Part of bytecode generated by the Kotlin compiler for suspending lambdas
      without suspension points is filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1283">#1283</a>).</li>
  <li>Part of bytecode generated by the Kotlin compiler for <code>when</code>
      expressions and statements with nullable enum subject is filtered out during
      generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1774">#1774</a>).</li>
  <li>Part of bytecode generated by the Kotlin compiler for <code>when</code>
      expressions and statements with nullable String subject is filtered out during
      generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1769">#1769</a>).</li>
  <li>Part of bytecode generated by the Kotlin compiler for chains of safe call
      operators is filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1810">#1810</a>,
      <a href="https://github.com/jacoco/jacoco/issues/1818">#1818</a>).</li>
  <li>Method <code>getEntries</code> generated by the Kotlin compiler for enum
      classes is filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1625">#1625</a>).</li>
  <li>Methods generated by the Kotlin compiler for constructors and functions
      with <code>JvmOverloads</code> annotation are filtered out
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1768">#1768</a>).</li>
</ul>

<h3>Fixed bugs</h3>
<ul>
  <li>Fixed interpretation of Kotlin SMAP
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1525">#1525</a>).</li>
  <li>File extensions are preserved in HTML report in case of clashes of normalized
      file names
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1660">#1660</a>).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>JaCoCo build now uses Maven Wrapper and requires at least Maven 3.9.9
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1708">#1708</a>,
      <a href="https://github.com/jacoco/jacoco/issues/1707">#1707</a>,
      <a href="https://github.com/jacoco/jacoco/issues/1681">#1681</a>).</li>
  <li>JaCoCo now depends on ASM 9.8
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1862">#1862</a>).</li>
  <li>More context information when <code>IllegalArgumentException</code> occurs
      during reading of zip file
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1833">#1833</a>).</li>
</ul>

<h2>Release 0.8.12 (2024/03/31)</h2>

<h3>New Features</h3>
<ul>
  <li>JaCoCo now officially supports Java 22
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1596">#1596</a>).</li>
  <li>Experimental support for Java 23 class files
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1553">#1553</a>).</li>
</ul>

<h3>Fixed bugs</h3>
<ul>
  <li>Branches added by the Kotlin compiler for functions with default arguments and
      having more than 32 parameters are filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1556">#1556</a>).</li>
  <li>Branch added by the Kotlin compiler version 1.5.0 and above for reading from
      <code>lateinit</code> property is filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1568">#1568</a>).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>JaCoCo now depends on ASM 9.7
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1600">#1600</a>).</li>
</ul>

<h2>Release 0.8.11 (2023/10/14)</h2>

<h3>New Features</h3>
<ul>
  <li>JaCoCo now officially supports Java 21
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1520">#1520</a>).</li>
  <li>Experimental support for Java 22 class files
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1479">#1479</a>).</li>
  <li>Part of bytecode generated by the Java compilers for exhaustive switch
      expressions is filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1472">#1472</a>).</li>
  <li>Part of bytecode generated by the Java compilers for record patterns is
      filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1473">#1473</a>).</li>
</ul>

<h3>Fixed bugs</h3>
<ul>
  <li>Instrumentation should not cause <code>VerifyError</code> when the last
      local variable of method parameters is overridden in the method body to
      store a value of type long or double
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/893">#893</a>).</li>
  <li>Restore exec file compatibility with versions from 0.7.5 to 0.8.8
      in case of class files with zero line numbers
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1492">#1492</a>).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>jacoco-maven-plugin now requires at least Java 8
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1466">#1466</a>,
      <a href="https://github.com/jacoco/jacoco/issues/1468">#1468</a>).</li>
  <li>JaCoCo build now requires at least Maven 3.5.4
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1467">#1467</a>).</li>
  <li>Maven 3.9.2 should not produce warnings for jacoco-maven-plugin
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1468">#1468</a>).</li>
  <li>JaCoCo build now requires JDK 17
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1482">#1482</a>).</li>
  <li>JaCoCo now depends on ASM 9.6
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1518">#1518</a>).</li>
</ul>

<h2>Release 0.8.10 (2023/04/24)</h2>

<h3>Fixed bugs</h3>
<ul>
  <li>Agent should not require configuration of permissions for
      <code>SecurityManager</code> outside of its <code>codeBase</code>
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1425">#1425</a>).</li>
</ul>

<h2>Release 0.8.9 (2023/03/31)</h2>

<h3>New Features</h3>
<ul>
  <li>JaCoCo now officially supports Java 19 and 20
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1371">#1371</a>,
      <a href="https://github.com/jacoco/jacoco/issues/1386">#1386</a>).</li>
  <li>Experimental support for Java 21 class files
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1386">#1386</a>).</li>
  <li>Add parameter to include the current project in the <code>report-aggregate</code>
      Maven goal
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1007">#1007</a>).</li>
  <li>Component accessors generated by the Java compilers for records are filtered
      out during generation of report. Contributed by Tesla Zhang
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1393">#1393</a>).</li>
</ul>

<h3>Fixed bugs</h3>
<ul>
  <li>Agent should not open <code>java.lang</code> package to unnamed module of the
      application class loader
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1334">#1334</a>).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>JaCoCo now depends on ASM 9.5
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1299">#1299</a>,
      <a href="https://github.com/jacoco/jacoco/issues/1368">#1368</a>,
      <a href="https://github.com/jacoco/jacoco/issues/1416">#1416</a>).</li>
  <li>JaCoCo build now requires JDK 11
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1413">#1413</a>).</li>
</ul>

<h2>Release 0.8.8 (2022/04/05)</h2>

<h3>New Features</h3>
<ul>
  <li>JaCoCo now officially supports Java 17 and 18
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1282">#1282</a>,
      <a href="https://github.com/jacoco/jacoco/issues/1198">#1198</a>).</li>
  <li>Experimental support for Java 19 class files
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1264">#1264</a>).</li>
  <li>Part of bytecode generated by the Java compilers for <code>assert</code>
      statement is filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1196">#1196</a>).</li>
  <li>Branch added by the Kotlin compiler version 1.6.0 and above for "unsafe" cast
      operator is filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1266">#1266</a>).</li>
  <li>Improved support for multiple JaCoCo runtimes in the same VM
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1057">#1057</a>).</li>
</ul>

<h3>Fixed bugs</h3>
<ul>
  <li>Fixed <code>NullPointerException</code> during filtering
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1189">#1189</a>).</li>
  <li>Fix range for debug symbols of method parameters
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1246">#1246</a>).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>JaCoCo now depends on ASM 9.2
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1206">#1206</a>).</li>
  <li>Messages of exceptions occurring during analysis or instrumentation now include
      JaCoCo version
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1217">#1217</a>).</li>
</ul>

<h2>Release 0.8.7 (2021/05/04)</h2>

<h3>New Features</h3>
<ul>
  <li>JaCoCo now officially supports Java 15 and 16
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1094">#1094</a>,
      <a href="https://github.com/jacoco/jacoco/issues/1097">#1097</a>,
      <a href="https://github.com/jacoco/jacoco/issues/1176">#1176</a>).</li>
  <li>Experimental support for Java 17 class files
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1132">#1132</a>).</li>
  <li>New <code>formats</code> parameter for Maven report goals to specify the
      generated report formats. Contributed by troosan.
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1175">#1175</a>).</li>
  <li>Branch added by the Kotlin compiler version 1.4.0 and above for "unsafe" cast
      operator is filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1143">#1143</a>,
      <a href="https://github.com/jacoco/jacoco/issues/1178">#1178</a>).</li>
  <li><code>synthetic</code> methods added by the Kotlin compiler version 1.5.0 and
      above for <code>private</code> suspending functions are filtered out
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1174">#1174</a>).</li>
  <li>Branches added by the Kotlin compiler version 1.4.20 and above for suspending
      lambdas are filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1149">#1149</a>).</li>
  <li>Branches added by the Kotlin compiler version 1.5.0 and above for functions
      with default arguments are filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1162">#1162</a>).</li>
  <li>Branch added by the Kotlin compiler version 1.5.0 and above for reading from
      <code>lateinit</code> property is filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1166">#1166</a>).</li>
  <li>Additional bytecode generated by the Kotlin compiler version 1.5.0 and above
      for <code>when</code> expressions on <code>kotlin.String</code> values
      is filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1172">#1172</a>).</li>
  <li>Improved filtering of bytecode generated by Kotlin compiler versions below
      1.5.0 for <code>when</code> expressions on <code>kotlin.String</code> values
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1156">#1156</a>).</li>
</ul>

<h3>Fixed bugs</h3>
<ul>
  <li>Fixed parsing of SMAP generated by Kotlin compiler version 1.5.0 and above
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1164">#1164</a>).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>JaCoCo now depends on ASM 9.1
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1094">#1094</a>,
      <a href="https://github.com/jacoco/jacoco/issues/1097">#1097</a>,
      <a href="https://github.com/jacoco/jacoco/issues/1153">#1153</a>).</li>
  <li>Maven plug-in has no dependency on <code>maven-reporting-impl</code> any more
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1121">#1121</a>).</li>
</ul>

<h2>Release 0.8.6 (2020/09/15)</h2>

<h3>New Features</h3>
<ul>
  <li>JaCoCo now officially supports Java 14.</li>
  <li>Experimental support for Java 15 class files
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/992">#992</a>).</li>
  <li>Experimental support for Java 16 class files
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1059">#1059</a>).</li>
  <li>Methods <code>toString</code>, <code>hashCode</code> and <code>equals</code>
      generated by compiler for records are filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/990">#990</a>).</li>
  <li>Bridge methods are filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1010">#1010</a>).</li>
  <li>Methods generated by Kotlin compiler for non-overridden non-abstract methods
      of interfaces are filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1012">#1012</a>).</li>
  <li>Branches added by the Kotlin compiler version 1.3.60 for suspending functions
      with tail call optimization are filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1016">#1016</a>).</li>
</ul>

<h3>Fixed bugs</h3>
<ul>
  <li>Compression method of zip entries is now preserved when instrumenting archives.
      This allows to use JaCoCo with frameworks that expect uncompressed entries
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1018">#1018</a>).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>Support for Pack200 was removed in JDK 14. JaCoCo will now throw a detailed
      exception when Pack200 archives are processed with the latest JDKs
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/984">#984</a>).</li>
  <li>JaCoCo now depends on ASM 8.0.1
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/1032">#1032</a>,
      <a href="https://github.com/jacoco/jacoco/issues/1043">#1043</a>).</li>
</ul>

<h3>API Changes</h3>
<ul>
  <li><code>URLStreamHandlerRuntime</code> removed
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/471">#471</a>).</li>
</ul>

<h2>Release 0.8.5 (2019/10/11)</h2>

<h3>New Features</h3>
<ul>
  <li>JaCoCo now officially supports Java 13</li>
  <li>Experimental support for Java 14 class files
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/897">#897</a>).</li>
  <li>Branches added by the Kotlin compiler for <code>open</code> functions with
      default arguments are filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/887">#887</a>).</li>
</ul>

<h3>Fixed bugs</h3>
<ul>
  <li><code>synthetic</code> constructors that contain values of default arguments
      in Kotlin should not be ignored
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/888">#888</a>).</li>
  <li>Instrumentation should update indexes of local variables in annotations
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/894">#894</a>).</li>
  <li>Branches added by the Kotlin compiler for functions with default arguments
      and containing arguments of type <code>long</code> or <code>double</code>
      should be filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/908">#908</a>).</li>
  <li><code>synthetic</code> methods that contain bodies of anonymous functions
      in Scala should not be ignored
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/912">#912</a>).</li>
  <li>To avoid failures with invalid class files report generation now checks
      that source references are actually files
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/941">#941</a>).</li>
  <li><code>NullPointerException</code> during filtering
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/942">#942</a>,
      <a href="https://github.com/jacoco/jacoco/issues/944">#944</a>).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>JaCoCo now distributed under the terms and conditions of the
      Eclipse Public License Version 2.0
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/943">#943</a>).</li>
  <li>Prevent startup when JaCoCo runtime cannot be initialized to avoid
      subsequent faults
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/910">#910</a>).</li>
  <li>JaCoCo now depends on ASM 7.2
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/947">#947</a>).</li>
</ul>

<h3>API Changes</h3>
<ul>
    <li>The coverage check API and tools (Ant, Maven) now report an error, when
        a coverage ratio limit is configured outside the range [0,1] to avoid
        common configuration mistakes
        (GitHub <a href="https://github.com/jacoco/jacoco/issues/783">#783</a>).</li>
  <li>Unsupported class file versions are now consistently reported as exceptions
      by all methods of <code>Analyzer</code> and <code>Instrumenter</code> and
      thus also during report generation and offline instrumentation
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/952">#952</a>).</li>
</ul>

<h2>Release 0.8.4 (2019/05/08)</h2>

<h3>New Features</h3>
<ul>
  <li>JaCoCo now officially supports Java 12</li>
  <li>Instrumentation does not add synthetic field to Java 11+ class files,
      however still adds synthetic method
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/845">#845</a>).</li>
  <li>Branches added by the Kotlin compiler version 1.3.30 for suspending lambdas
      and functions are filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/849">#849</a>).</li>
</ul>

<h3>Fixed bugs</h3>
<ul>
  <li>Fixed incorrect update of frames caused by bug in ASM library in case of
      arrays with more than 7 dimensions
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/839">#839</a>).</li>
  <li>Fixed regression, which was introduced in 0.8.3 -
      <code>module-info.class</code> should be excluded from analysis to not cause
      <code>IllegalStateException</code>
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/859">#859</a>).</li>
</ul>

<h3>API Changes</h3>
<ul>
    <li>Methods <code>Instrumenter.instrument(org.objectweb.asm.ClassReader)</code>
        and <code>Analyzer.analyzeClass(org.objectweb.asm.ClassReader)</code>
        were removed
        (GitHub <a href="https://github.com/jacoco/jacoco/issues/850">#850</a>).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>JaCoCo now depends on ASM 7.1
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/851">#851</a>).</li>
</ul>

<h2>Release 0.8.3 (2019/01/23)</h2>

<h3>New Features</h3>
<ul>
  <li>jacoco-maven-plugin now requires at least Maven 3.0
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/821">#821</a>).</li>
  <li>JaCoCo now officially supports Java 11
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/760">#760</a>).</li>
  <li>Experimental support for Java 13 class files
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/835">#835</a>).</li>
  <li>Branch added by the Kotlin compiler for "unsafe" cast operator is filtered
      out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/761">#761</a>).</li>
  <li>Branch added by the Kotlin compiler for not-null assertion operator is
      filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/815">#815</a>).</li>
  <li>Instructions inlined by Kotlin compiler are filtered out during generation
      of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/764">#764</a>).</li>
  <li>Branches added by the Kotlin compiler for suspending lambdas and functions
      are filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/802">#802</a>,
      <a href="https://github.com/jacoco/jacoco/issues/803">#803</a>,
      <a href="https://github.com/jacoco/jacoco/issues/809">#809</a>).</li>
  <li>Classes and methods annotated by annotation whose retention policy is
      <code>runtime</code> or <code>class</code> and whose simple name contains
      "Generated" (previously equality was required) are filtered out during
      generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/822">#822</a>).</li>
  <li>HTML report shows message when source file can't be found
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/801">#801</a>).</li>
  <li>HTML report shows message when class has no debug information
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/818">#818</a>).</li>
  <li>HTML report shows message when analyzed class does not match executed
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/819">#819</a>).</li>
  <li>HTML report shows message when no class files specified and when
      none of the analyzed classes contain code relevant for code coverage
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/833">#833</a>).</li>
  <li>Empty class and sourcefile nodes are preserved and available in XML report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/817">#817</a>).</li>
  <li>Agent avoids conflicts with other agents when running on Java 9+
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/829">#829</a>).</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li><code>synthetic</code> methods that contain values of default arguments
      in Kotlin should not be ignored
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/774">#774</a>).</li>
  <li><code>synthetic</code> methods that represent <code>suspend</code>
      functions in Kotlin should not be ignored
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/804">#804</a>).</li>
  <li>Removed misleading parameters <code>includes</code> and
      <code>excludes</code> from <code>dump</code>, <code>merge</code> and
      <code>restore-instrumented-classes</code> goals of jacoco-maven-plugin,
      because they have no effect
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/827">#827</a>).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>JaCoCo now depends on ASM 7.0
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/760">#760</a>).</li>
</ul>

<h2>Release 0.8.2 (2018/08/21)</h2>

<h3>New Features</h3>
<ul>
  <li>Experimental support for Java 11 and Java 12 class files, including
      JEP 12 "preview features"
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/719">#719</a>,
      <a href="https://github.com/jacoco/jacoco/issues/738">#738</a>,
      <a href="https://github.com/jacoco/jacoco/issues/743">#743</a>).</li>
  <li>Branches and instructions generated by javac 11 for try-with-resources
      statement are filtered out
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/669">#669</a>).</li>
  <li>Synthetic classes are filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/668">#668</a>).</li>
  <li>Part of bytecode generated by ECJ for <code>switch</code> statements on
      <code>java.lang.String</code> values is filtered out during generation of
      report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/735">#735</a>,
      <a href="https://github.com/jacoco/jacoco/issues/741">#741</a>,
      <a href="https://github.com/jacoco/jacoco/issues/746">#746</a>).</li>
  <li>Methods added by the Kotlin compiler that do not have line numbers are
      filtered out during generation of report. Idea and implementation by
      Nikolay Krasko
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/689">#689</a>).</li>
  <li>Branch added by the Kotlin compiler for reading from <code>lateinit</code>
      property is filtered out during generation of report. Implementation by
      Fabian Mastenbroek
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/707">#707</a>).</li>
  <li>Bytecode generated by Kotlin compiler for implicit <code>else</code> of
      <code>when</code> expressions that list all cases of <code>enum</code> or
      <code>sealed class</code> is filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/721">#721</a>,
      <a href="https://github.com/jacoco/jacoco/issues/729">#729</a>,
      <a href="https://github.com/jacoco/jacoco/issues/749">#749</a>).</li>
  <li>Additional bytecode generated by Kotlin compiler for <code>when</code>
      expressions on <code>kotlin.String</code> values is filtered out during
      generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/737">#737</a>,
      <a href="https://github.com/jacoco/jacoco/issues/746">#746</a>).</li>
  <li>Classes and methods annotated with annotation whose retention policy
      is <code>runtime</code> or <code>class</code> and whose simple name
      is <code>Generated</code> are filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/731">#731</a>).</li>
  <li>Maven goal <code>report-aggregate</code> now also considers dependencies
      specified using version range. Idea and implementation by Lukas Krejc
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/658">#658</a>).</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li>Don't insert stackmap frames into class files with version &lt; 1.6,
      this fixes regression which was introduced in version 0.6.5
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/667">#667</a>).</li>
  <li>Question mark in filter expressions now correctly matches exactly one character
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/672">#672</a>).</li>
  <li>Part of bytecode that javac generates for <code>switch</code> statement on
      <code>java.lang.String</code> values with a small number cases is now correctly
      filtered out during generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/730">#730</a>).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>JaCoCo now depends on ASM 6.2.1
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/706">#706</a>,
      <a href="https://github.com/jacoco/jacoco/issues/725">#725</a>).</li>
  <li>Improved error message when already instrumented classes are used for
      instrumentation or analysis
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/703">#703</a>).</li>
  <li>JaCoCo build now requires at least Maven 3.3.9 and JDK 8
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/711">#711</a>).</li>
</ul>

<h3>API Changes</h3>
<ul>
  <li>The XML report now has an optional attribute <code>sourcefilename</code>
      on the <code>class</code> element to allow unambiguously relate classes
      to source files. The JaCoCo DTD version has been updated to 1.1
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/702">#702</a>).</li>
</ul>

<h2>Release 0.8.1 (2018/03/21)</h2>

<h3>New Features</h3>
<ul>
  <li>JaCoCo now supports Java 10
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/629">#629</a>).</li>
  <li>Empty constructor without parameters in enum is filtered out during
      generation of report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/649">#649</a>).</li>
</ul>

<h2>Release 0.8.0 (2018/01/02)</h2>

<h3>New Features</h3>
<ul>
  <li>JaCoCo now officially supports Java 9
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/600">#600</a>).</li>
  <li>JaCoCo now comes with a simple command line interface
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/525">#525</a>).</li>
  <li>Limit configuration of Maven <code>check</code> goal and Ant
      <code>check</code> element now also supports percentage values
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/534">#534</a>).</li>
  <li>Manifests of JAR files now have <code>Automatic-Module-Name</code> for Java 9
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/565">#565</a>).</li>
  <li>Maven aggregated reports will now also include modules of <code>runtime</code>
      and <code>provided</code> dependencies
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/498">#498</a>,
      <a href="https://github.com/jacoco/jacoco/issues/572">#572</a>).</li>
</ul>

<p>
  During creation of reports various compiler generated artifacts are filtered
  out, which otherwise require unnecessary and sometimes impossible tricks to
  not have partial or missed coverage:
</p>

<ul>
  <li>Methods <code>valueOf</code> and <code>values</code> of enum types
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/491">#491</a>).</li>
  <li>Private empty no-argument constructors
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/529">#529</a>).</li>
  <li>Methods annotated with <code>@lombok.Generated</code> to better integrate
      with Lombok >= 1.16.14. Initial analysis and contribution by Rüdiger zu Dohna
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/513">#513</a>).</li>
  <li>Methods annotated with <code>@groovy.transform.Generated</code> to better
      integrate with Groovy >= 2.5.0. Thanks to Andres Almiray for adding the annotation to Groovy
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/610">#610</a>).</li>
  <li>Part of bytecode for <code>synchronized</code> blocks
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/501">#501</a>).</li>
  <li>Part of bytecode for try-with-resources statements
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/500">#500</a>).</li>
  <li>Part of bytecode for <code>finally</code> blocks
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/604">#604</a>).</li>
  <li>Part of bytecode for <code>switch</code> statements on
      <code>java.lang.String</code> values
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/596">#596</a>).</li>
</ul>

<p>
  Note: Tools that directly read <code>exec</code> files and embed JaCoCo for
  this (such as SonarQube or Jenkins) will provide filtering functionality only
  after they updated to this version of JaCoCo.
</p>

<h3>Fixed Bugs</h3>
<ul>
  <li>Fixed bug in instrumentation of exception handlers, which was causing damage
      of structured locking in certain situations and as consequence poor
      performance of instrumented methods, analysis and fix contributed by Allen Hair
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/627">#627</a>).</li>
  <li><code>dump</code> commands now report error when server unexpectedly
      closes connection without sending response
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/538">#538</a>).</li>
  <li>Reduced chance of conflict with other agents
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/555">#555</a>).</li>
  <li>Restored Maven <code>help</code> goal that was missing in version 0.7.9
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/559">#559</a>).</li>
  <li><code>NullPointerException</code> during offline instrumentation of
      <code>module-info.class</code>
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/600">#600</a>,
      GitHub <a href="https://github.com/jacoco/jacoco/issues/634">#634</a>).</li>
  <li>Incorrect update of frames caused by bug in ASM library
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/600">#600</a>).</li>
  <li>Loss of <code>InnerClasses</code> attribute caused by bug in ASM library
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/600">#600</a>).</li>
  <li><code>NegativeArraySizeException</code> during instrumentation caused by bug
      in ASM library
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/600">#600</a>).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>JaCoCo now depends on ASM 6.0
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/600">#600</a>).</li>
  <li>More information about context is provided when unable to read input during
      instrumentation
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/527">#527</a>).</li>
  <li>More information about context is provided when unable to read stream during
      analysis
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/541">#541</a>).</li>
</ul>

<h2>Release 0.7.9 (2017/02/05)</h2>

<h3>Fixed Bugs</h3>
<ul>
  <li>"<code>java.lang.ClassFormatError: Short length on BootstrapMethods in class file</code>"
      caused by bug in ASM library
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/462">#462</a>).</li>
  <li>Do not recompute frames in case of large methods, otherwise
      <code>java.lang.ClassNotFoundException</code> might be thrown
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/177">#177</a>).</li>
  <li><code>ExecutionDataWriter.FORMAT_VERSION</code> is not a compile-time constant
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/474">#474</a>).</li>
  <li>Maven goal "prepare-agent" should not overwrite existing property value if execution was skipped
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/486">#486</a>).</li>
</ul>

<h3>API Changes</h3>
<ul>
  <li><code>JaCoCo.ASM_API_VERSION</code> removed
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/474">#474</a>).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>JaCoCo now depends on ASM 5.2.</li>
  <li>OSGi metadata now generated automatically and additionally include
      <code>uses</code> directives, <code>Require-Capability</code> attribute,
      and export of all internal packages with <code>x-internal:=true</code> directive
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/211">#211</a>).</li>
  <li>Removed OSGi attributes that were mistakenly added in version 0.6.1 into
      <code>jacocoant.jar</code> that contains all dependencies
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/211">#211</a>).</li>
</ul>

<h2>Release 0.7.8 (2016/12/09)</h2>

<h3>New Features</h3>
<ul>
  <li>User property <code>jacoco.dataFile</code> for parameter <code>dataFile</code>
      of Maven <code>report</code> goal
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/322">#322</a>).</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li>Use <code>RoundingMode#FLOOR</code> instead of
      <code>RoundingMode#HALF_EVEN</code> for percentages in HTML report, so that
      "99.5" is displayed as "99%", not as "100%"
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/452">#452</a>).</li>
  <li>Do not add useless members into Java 8 interfaces that have only interface
      initialization and abstract methods
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/441">#441</a>).</li>
  <li>Fix instrumentation to not violate Java Virtual Machine Specification regarding
      initialization of final fields, otherwise <code>IllegalAccessError</code>
      will be thrown starting from OpenJDK 9 EA b127
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/434">#434</a>).</li>
  <li>Fix instrumentation of interfaces with default methods to not create incorrect
      constant pool entries, which lead to <code>IncompatibleClassChangeError</code>
      starting from OpenJDK 9 EA b122
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/428">#428</a>).</li>
  <li>Add Maven goal <code>report-aggregate</code> to lifecycle-mapping-metadata.xml
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/427">#427</a>).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>Released JaCoCo JARs are not signed any more. Signed versions of JaCoCo are
      now available from the Eclipse Orbit project
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/466">#466</a>).</li>
  <li>Simplified numbering of versions - JaCoCo JARs in Maven Central repository
      do not have qualifier any more
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/468">#468</a>).</li>
</ul>

<h2>Release 0.7.7 (2016/06/06)</h2>

<h3>New Features</h3>
<ul>
  <li>New Maven goal <code>report-aggregate</code> to create reports for
      multi-module projects
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/388">#388</a>).</li>
  <li>New parameters <code>title</code> and <code>footer</code> for Maven
      reporting goals allow customization of generated reports.</li>
  <li>Renamed "dot" resources in generated HTML reports to become more web
      hosting friendly
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/401">#401</a>).</li>
  <li>Experimental support for Java 9 class files
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/406">#406</a>).</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li>Don't suppress EOF errors in case of truncated execution data files
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/397">#397</a>).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>JaCoCo now depends on ASM 5.1.</li>
  <li>Empty probe arrays are not written to execution data files any more. This
      reduces exec file size significantly for per-test data dumps.
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/387">#387</a>).</li>
  <li>More information about context is provided when unable to read input during
      analysis.
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/400">#400</a>).</li>
  <li>Require at least Maven 3.0 for build of JaCoCo.</li>
</ul>


<h2>Release 0.7.6 (2016/02/18)</h2>

<h3>New Features</h3>
<ul>
  <li>New agent option <code>inclnolocationclasses</code> to support execution
      environments like Android where no source location is provided with classes
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/288">#288</a>).</li>
  <li>Improved error message in case of incompatible execution data files.
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/319">#319</a>).</li>
  <li>Command line agent options now supports comma in file names. Contributed
      by Jochen Berger.
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/358">#358</a>).</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li>Fix <code>MBeanClient</code> example
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/333">#333</a>).</li>
  <li>Avoid <code>ConcurrentModificationException</code> during shutdown
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/364">#364</a>).</li>
</ul>

<h3>API Changes</h3>
<ul>
  <li>In case of incompatible execution data formats read from another JaCoCo
  version <code>ExecutionDataReader.read()</code> now throws a
  <code>IncompatibleExecDataVersionException</code>.</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>JaCoCo now depends on ASM 5.0.4.</li>
</ul>


<h2>Release 0.7.5 (2015/05/24)</h2>

<h3>New Features</h3>
<ul>
  <li>Better detection of coverage in code blocks with implicit exceptions.
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/310">#310</a>).</li>
  <li>Added lifecycle-mapping-metadata.xml for M2E
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/203">#203</a>).</li>
  <li>Allow locales with country and variant for Ant report task
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/289">#289</a>).</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li>For the Ant tasks <code>coverage</code> and <code>agent</code> the
      <code>destfile</code> attribute is now passed as an absolute path also in
      the default case
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/301">#301</a>).</li>
</ul>

<h3>API Changes</h3>
<ul>
  <li>The exec file version has been updated and is not compatible with previous
      versions.</li>
</ul>

<h2>Release 0.7.4 (2015/02/26)</h2>

<h3>Fixed Bugs</h3>
<ul>
  <li>Restored exec file compatibility with version 0.7.2 by fixing missing
      probes in case of try/catch blocks which are jump targets.
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/286">#286</a>).</li>
</ul>


<h2>Release 0.7.3 (2015/02/19)</h2>

<h3>New Features</h3>
<ul>
  <li>For offline instrumemtation agent configuration supports system properties
      replacements. Implementation based on pull request of GitHub user 'debugger'
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/262">#262</a>).</li>
  <li>Exclude dynamically generated classes from instrumentation for better
      interoperability with JMockit, analysis contributed by Rogério Liesenfeld
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/272">#272</a>).</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li>Instrumented bytecode now compatible with Android ART runtime, analysis
      and fix contributed by Allen Hair
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/265">#265</a>).</li>
</ul>

<h2>Release 0.7.2 (2014/09/12)</h2>

<h3>Fixed Bugs</h3>
<ul>
  <li>Do not ignore synthetic lambda methods to get code coverage for Java 8
      lambda expressions
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/232">#232</a>).</li>
</ul>

<h3>New Features</h3>
<ul>
  <li>New configuration option for the JaCoCo agent
      <code>inclbootstrapclasses</code> to also instrument classes from the
      bootstrap class loader
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/49">#49</a>).</li>
  <li>Agent uses unique file names to dump class files
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/225">#225</a>).</li>
</ul>

<h2>Release 0.7.1 (2014/05/08)</h2>

<h3>Fixed Bugs</h3>
<ul>
  <li>Fixed failure with default methods in Java 8 interfaces
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/201">#201</a>).</li>
  <li>Better interoperability with JMockit, analysis and fix contributed by Rogério
      Liesenfeld (GitHub <a href="https://github.com/jacoco/jacoco/issues/35">#35</a>
      and <a href="https://github.com/jacoco/jacoco/issues/54">#54</a>).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>JaCoCo now depends on ASM 5.0.1
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/201">#201</a>).</li>
</ul>

<h2>Release 0.7.0 (2014/03/18)</h2>

<h3>New Features</h3>
<ul>
  <li>JaCoCo now supports Java 8
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/74">#74</a>).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>JaCoCo now depends on ASM 5.0 using asm-debug-all instead of asm-all
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/199">#199</a>).</li>
</ul>

<h2>Release 0.6.5 (2014/03/03)</h2>

<h3>New Features</h3>
<ul>
  <li>Warnings are logged during report generation if different versions of
      classes are used than at runtime (GitHub <a href="https://github.com/jacoco/jacoco/issues/185">#185</a>).</li>
  <li>Signatures are removed from instrumented JAR files
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/186">#186</a>).</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li>Skip jacoco instrumentation for mvn modules with package type ear (GitHub <a href="https://github.com/jacoco/jacoco/issues/169">#169</a>).</li>
  <li>Align skip conditions and messages for Maven goals and give reasons. This
      includes removal of the specific skip condition for packages e.g. POMs and
      instead checks existence of <tt>target/classes</tt> in appropriate goals
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/171">#171</a>).</li>
  <li>GitHub #44: (Regression) Agent Mojo should set empty property, if execution was skipped
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/192">#192</a>).</li>
</ul>

<h3>API Changes</h3>
<ul>
  <li>Restrict visibility of methods in abstract classes of jacoco-maven-plugin (GitHub <a href="https://github.com/jacoco/jacoco/issues/175">#175</a>).</li>
</ul>

<h2>Release 0.6.4 (2013/12/10)</h2>

<h3>New Features</h3>
<ul>
  <li>Support for Android <i>Strict Mode</i> (GitHub <a href="https://github.com/jacoco/jacoco/issues/113">#113</a>).</li>
  <li>New dump Mojo for Maven plug-in (GitHub <a href="https://github.com/jacoco/jacoco/issues/107">#107</a>).</li>
  <li>New merge Mojo for Maven plug-in, contributed by Mads Mohr Christensen
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/126">#126</a>).</li>
  <li>Additional list of source files for every package in HTML report
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/142">#142</a>).</li>
  <li>Additional report-integration and prepare-agent-integration goals for
      integration tests. (GitHub <a href="https://github.com/jacoco/jacoco/issues/152">#152</a>, <a href="https://github.com/jacoco/jacoco/issues/161">#161</a>).</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li>Multiple executions of Maven goal "prepare-agent" should not lead to
      incorrect configuration (GitHub <a href="https://github.com/jacoco/jacoco/issues/40">#40</a>).</li>
  <li>Avoid direct dependency on java.lang.management APIs to allow usage on
      Android (GitHub <a href="https://github.com/jacoco/jacoco/issues/150">#150</a>).</li>
  <li>All JaCoCo Maven goals are marked as thread-safe (GitHub <a href="https://github.com/jacoco/jacoco/issues/133">#133</a>).</li>
  <li>check and merge goal failures in jacoco-maven-plugin with Maven 2.2.1
      due to incompatible configuration (GitHub <a href="https://github.com/jacoco/jacoco/issues/129">#129</a>).</li>
</ul>

<h3>API Changes</h3>
<ul>
  <li>New API package <code>org.jacoco.core.tools</code> for shared high-level
      tools. (GitHub <a href="https://github.com/jacoco/jacoco/issues/159">#159</a>).</li>
</ul>

<h2>Release 0.6.3 (2013/06/03)</h2>

<h3>New Features</h3>
<ul>
  <li>Support for archives in Pack200 format (GitHub <a href="https://github.com/jacoco/jacoco/issues/91">#91</a>).</li>
  <li>The coverage check has been reworked to allow checks on all counter values
      on all element types (GitHub <a href="https://github.com/jacoco/jacoco/issues/106">#106</a>).</li>
  <li>Coverage checks are now also available in Ant (GitHub <a href="https://github.com/jacoco/jacoco/issues/106">#106</a>).</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li>Fixed inconsistent stackmap frames when instrumenting class files produced
      by certain tools like ProGuard (GitHub <a href="https://github.com/jacoco/jacoco/issues/85">#85</a>).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>More context information when exceptions occur during analysis or
      instrumentation (GitHub <a href="https://github.com/jacoco/jacoco/issues/104">#104</a>).</li>
  <li>If analysis is performed on offline instrumented classes - which is an
      build configuration error - an exception is now thrown (GitHub <a href="https://github.com/jacoco/jacoco/issues/108">#108</a>).</li>
</ul>

<h3>API Changes</h3>
<ul>
  <li>The configuration of the Maven check goal has been reworked to support
      checks on any element type (GitHub <a href="https://github.com/jacoco/jacoco/issues/106">#106</a>).</li>
  <li><code>Analyzer</code> and <code>Instrumenter</code> expect resource name
      as additional parameter for better error messages (GitHub <a href="https://github.com/jacoco/jacoco/issues/104">#104</a>).</li>
</ul>

<h2>Release 0.6.2 (2013/02/03)</h2>

<h3>New Features</h3>
<ul>
  <li>Support for offline instrumentation (GitHub <a href="https://github.com/jacoco/jacoco/issues/4">#4</a>, <a href="https://github.com/jacoco/jacoco/issues/64">#64</a>).</li>
  <li>JaCoCo agent exposes runtime API for direct integration with application
      under test (GitHub <a href="https://github.com/jacoco/jacoco/issues/61">#61</a>).</li>
  <li>Support for parallel test execution: Different agents can now safely write
      to the same <code>*.exec</code> file (GitHub <a href="https://github.com/jacoco/jacoco/issues/52">#52</a>).</li>
  <li>New output mode 'none' can be used when agent is only controlled via JMX
      or its new runtime API (GitHub <a href="https://github.com/jacoco/jacoco/issues/63">#63</a>).</li>
  <li>Better error message in case of invalid stackmap frames (GitHub <a href="https://github.com/jacoco/jacoco/issues/73">#73</a>).</li>
  <li>jacoco-maven-plugin: default phase for goal "report" - "verify"
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/72">#72</a>).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>For every build the corresponding Git commit hash is included as
      <code>Eclipse-SourceReferences</code> manifest headers (GitHub <a href="https://github.com/jacoco/jacoco/issues/7">#7</a>, <a href="https://github.com/jacoco/jacoco/issues/59">#59</a>).</li>
  <li>Adjust saturation of red source highlighting in HTML reports to make
      JaCoCo reports more accessible to red/green blind users.</li>
</ul>

<h3>API Changes</h3>
<ul>
  <li>The <code>output</code> mode <code>mbean</code> of the agent has been
      dropped in favor of the new boolean option <code>jmx</code>. This allows
      to combine JMX access with any other output mode (GitHub <a href="https://github.com/jacoco/jacoco/issues/62">#62</a>).</li>
</ul>

<h2>Release 0.6.1 (2012/12/23)</h2>

<h3>New Features</h3>
<ul>
  <li>Add a coverage check Mojo, contributed by Kyle Lieber (GitHub <a href="https://github.com/jacoco/jacoco/issues/6">#6</a>).</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li>jacocoant.jar should not conflict with ASM 3.x (GitHub <a href="https://github.com/jacoco/jacoco/issues/39">#39</a>).</li>
  <li>Agent Mojo should set empty property, if execution was skipped
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/44">#44</a>).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>Upgrade to ASM 4.1 (GitHub <a href="https://github.com/jacoco/jacoco/issues/37">#37</a>).</li>
  <li>Log location of merged execution data file in Ant <code>merge</code> task
      (GitHub <a href="https://github.com/jacoco/jacoco/issues/29">#29</a>).</li>
</ul>

<h2>Release 0.6.0 (2012/10/06)</h2>

<h3>New Features</h3>
<ul>
  <li>Full support for Java 7 including INVOKEDYNAMIC as JaCoCo is now based
      on ASM 4.0 (GitHub <a href="https://github.com/jacoco/jacoco/issues/5">#5</a>).</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li>Maven plugin should skip creation of report if the execution data file is
      missing. This avoids appearance of stack traces in case when JVM was not
      forked, e.g. if there are no tests to run (SF #3563431, GitHub <a href="https://github.com/jacoco/jacoco/issues/19">#19</a>).</li>
  <li>Maven plugin should fail if unable to read execution data file or create
      report (GitHub <a href="https://github.com/jacoco/jacoco/issues/19">#19</a>).</li>
</ul>

<h3>API Changes</h3>
<ul>
  <li>Only colon as separator character supported in agent parameters. The
      deprecated pipe character is no longer supported.</li>
</ul>

<h2>Release 0.5.10 (2012/08/31)</h2>

<h3>Fixed Bugs</h3>
<ul>
  <li>Correct stackmap frames for new <code>long[]</code> arrays (SF #3553210).</li>
  <li>Properly resolve the <code>fork</code> variable for tasks wrapped by the
      <code>coverage</code> Ant task (Trac #213).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>Source tree has been migrated to Git and is now hosted on Github at
  <a href="https://github.com/jacoco/jacoco">https://github.com/jacoco/jacoco</a>.</li>
</ul>

<h2>Release 0.5.9 (2012/07/30)</h2>

<h3>Fixed Bugs</h3>
<ul>
  <li>Don't produce invalid class files in case of unexpected stackmap frames
      (SF #3543758).</li>
</ul>

<h2>Release 0.5.8 (2012/07/11)</h2>

<h3>New Features</h3>
<ul>
  <li>Support for parallel Maven builds (Trac #191).</li>
  <li>New agent option <code>classdumpdir</code> to dump all class files seen
      by the JaCoCo agent to disk. This option is also available for Ant and
      Maven (Trac #208).</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li>Allow instrumentation of classes with dead code (SF #3538112).</li>
  <li>Reworked instrumentation strategy to avoid verifier error "Uninitialized
      object exists on backward branch" with certain Java 7 class files
      (Trac #154).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>Documentation now includes Maven example and Maven goal documentation
      (Trac #201, #202).</li>
</ul>

<h2>Release 0.5.7 (2012/04/19)</h2>

<h3>New Features</h3>
<ul>
  <li>Support for class redefinitions by other agents like JMockit (SF #3509409).</li>
  <li>Remove class file attributes with invalid code offsets caused by other
      byte code processing tools to avoid verifier errors (Trac #196).</li>
  <li>Improved logging for Ant tasks (SF #3518429).</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li>In case of failures the agent should log the original exception.</li>
</ul>

<h2>Release 0.5.6 (2012/01/23)</h2>

<h3>New Features</h3>
<ul>
  <li>jacoco-maven-plugin can be used together with maven-site-plugin (Trac #181).</li>
  <li>The <code>report</code> Ant task now also supports directory resources to
      specify source folders. This allows reading source files from multiple
      directories. Contributed by Dominik Stadler (Trac #119).</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li>Don't insert stackmap frames for class files before version 1.6.</li>
  <li>Regression, which was introduced in 0.5.4 - restored compatibility of jacoco-maven-plugin with Maven 2 (Trac #182).</li>
</ul>

<h2>Release 0.5.5 (2011/12/15)</h2>

<h3>Fixed Bugs</h3>
<ul>
  <li>Correct default value for the <code>append</code> property of the
      <code>dump</code> Ant task is <code>true</code> (Trac #178).</li>
  <li>Try/catch blocks must not be counted as instructions (Trac #179).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>Upgrade to ASM 3.3.1.</li>
</ul>

<h2>Release 0.5.4 (2011/11/11)</h2>

<h3>New Features</h3>
<ul>
  <li>Maven plug-in: respect <code>includes</code> and <code>excludes</code>
      properties in report Mojo (Trac #160).
      Also note that:
<pre class="source lang-xml">
&lt;configuration&gt;
  &lt;includes&gt;org.foo.*:org.bar.*&lt;/includes&gt;
  &lt;excludes&gt;com.foo.*:com.bar.*&lt;/excludes&gt;
&lt;/configuration&gt;
</pre>
      must be replaced on:
<pre class="source lang-xml">
&lt;configuration&gt;
  &lt;includes&gt;
    &lt;include>org.foo.*&lt;/include&gt;
    &lt;include>org.bar.*&lt;/include&gt;
  &lt;/includes&gt;
  &lt;excludes&gt;
    &lt;exclude&gt;com.foo.*&lt;/exclude&gt;
    &lt;exclude&gt;com.bar.*&lt;/exclude&gt;
  &lt;/excludes&gt;
&lt;/configuration&gt;
</pre>
  </li>
</ul>

<h3>API Changes</h3>
<ul>
  <li><code>Instrumenter</code> now only requires
      <code>IExecutionDataAccessorGenerator</code> instead of
      <code>IRuntime</code> (Trac #174).</li>
  <li>Removed reference to internal implementation classes from
      <code>CoverageBuilder</code> API.</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>Release bundles from now on signed again.</li>
  <li>Several documentation updates.</li>
</ul>

<h2>Release 0.5.3 (2011/07/06)</h2>

<h3>New Features</h3>
<ul>
  <li>New Maven plug-in for JaCoCo agent setup and basic reporting (Trac #25).</li>
  <li>Additional output mode implementation for JaCoCo agent based on JMX.</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li>Fixed problem with reporting of nested group structures (Trac #157).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>JaCoCo build based on Maven (Trac #136).</li>
  <li>JaCoCo published to Maven repository (Trac #149).</li>
</ul>

<h2>Release 0.5.2 (2011/05/19)</h2>

<h3>New Features</h3>
<ul>
  <li>JaCoCo reports cyclomatic complexity numbers (Trac #129).</li>
  <li>For HTML reports the tab width can be specified on the
      <code>sourcefiles</code> attribute of the <code>report</code> Ant task
      (Track #152).</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li>Removed duplicate counters in the root node of XML reports (Trac #155).</li>
  <li>Avoid <code>StackOverflowException</code> when analyzing methods with very
      long instruction sequences (Trac #156). Many thanks to Jean-Eric Cuendet
      for reporting and analyzing this!</li>
</ul>

<h3>API Changes</h3>
<ul>
  <li><code>ICoverageNode</code> API has been extended for cyclomatic
      complexity (Trac #129).</li>
  <li>XML and CSV report includes new counter type <code>COMPLEXITY</code>
      (Trac #129).</li>
  <li>New method <code>getTabWidth()</code> in callback interface
      <code>ISourceFileLocator</code> instead of
      <code>HTMLReportFormatter.setTabWidth()</code> which has been removed.
      (Trac #152).</li>
</ul>

<h2>Release 0.5.1 (2011/03/21)</h2>

<h3>New Features</h3>
<ul>
  <li>Duplicate classes with identical identifiers are now ignored during
      analysis.</li>
  <li>Added support for TestNG to Ant task Coverage (Track #144).</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li>Calculate correct stackmap frames for Java 1.6 branches (Trac #139).</li>
  <li>Link source files also for classes in default package (Trac #151).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>API documentation cleanup (Track #140).</li>
  <li>Removed obsolete examples from documentation (Trac #141).</li>
  <li>Added reporting API example (Trac #146).</li>
  <li>Reduced file size of HTML report source pages (Trac #148).</li>
</ul>

<h3>API Changes</h3>
<ul>
  <li>Simplified reporting API (Trac #53).</li>
  <li>Use colon as separator character in agent parameters (Trac #143).</li>
  <li>Now also empty files are accepted as <code>*.exec</code> files (Trac #150).</li>
</ul>

<h2>Release 0.5.0 (2011/01/19)</h2>

<h3>New Features</h3>
<ul>
  <li>JaCoCo now reports branch coverage (Track #66).</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li>Only process actual Java class files during analysis (SF #3106102).</li>
  <li>Fix broken source links due to sort hash (Track #125).</li>
  <li>Fixed invalid OSGi headers in MANIFEST.MF files (Track #127).</li>
  <li>Try to avoid interference with Hibernate (SF #3134190).</li>
  <li>Provide proper error message in case of duplicate class names in the same
      group (SF #3110219).</li>
  <li>Allow any number of probes in static interface initializers (SF #3161106).</li>
</ul>

<h3>API Changes</h3>
<ul>
  <li>All analysis specific APIs have been moved to package
      <code>org.jacoco.core.analysis</code>.</li>
  <li>The <code>IStructureVisitor</code> interface has been replaced by a
      simplified version called <code>ICoverageVisitor</code> (Track #132).</li>
  <li>All counter creation and update APIs now accept <i>missed items</i> and
      <i>covered items</i> as parameters (Track #131).</li>
  <li>Instructions are now counted on a per line basis. Therefore some
      interfaces in the <code>org.jacoco.core.analysis</code> package have
      changed as well as the <code>line</code> element in the XML report
      (Track #130).</li>
  <li>Several internal implementation classes have removed from the core APIs or
      have been replaced by new interfaces (Track #133).</li>
</ul>

<h2>Release 0.4.1 (2010/10/07)</h2>

<h3>New Features</h3>
<ul>
  <li>New attribute <code>line</code> for <code>method</code> elements in the
      XML report containing the first source line number of the method.
      (Track #120).</li>
  <li>Optional <code>locale</code> attribute for number rendering HTML reports,
      also available as an attribute of the <code>html</code> tag of the
      <code>report</code> Ant task (Track #122).</li>
  <li>Coverage tables in HTML report are now sortable (Track #98).</li>
  <li>The <code>report</code> Ant task issues a warning if source files are
      provided but class files do not contain debug information to collect line
      numbers (SF #3024486).</li>
</ul>

<h3>Non-functional Changes</h3>
<ul>
  <li>Reduced memory footprint for coverage data by 30% (Track #106).</li>
  <li>Moved to Eclipse 3.6.x as development environment (Trac #115).</li>
  <li>All delivered bundles and JAR files are signed (Trac #118).</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li>Better error message when multiple JaCoCo agents are specified
      (Track #103).</li>
  <li>Fixed potential NPE at shutdown when running agent in
      <code>tcpserver</code> mode (Track #117).</li>
  <li>Agent now fails at startup when execution data file can't be opened
      (Track #121).</li>
</ul>

<h2>Release 0.4.0 (2010/06/04)</h2>

<h3>New Features</h3>
<ul>
  <li>Execution data now includes session information: an arbitrary identifier,
      the start time and dump time (Trac #88).</li>
  <li>Added session information to XML report (Trac #92).</li>
  <li>New "Sessions" page in the HTML report shows information about each
      sessions wherein execution data has been collected for the report
      (Trac #93). Additionally all classes with execution data available are
      listed on this page (Trac #94).</li>
  <li>The agent now supports remote dumps via TCP/IP connections (Trac #82).</li>
  <li>New Ant task <code>dump</code> to remotely collect coverage data from
      agents (Trac #100).</li>
</ul>

<h3>API Changes</h3>
<ul>
  <li>Execution data file header is written and read in any case (Trac #72).</li>
  <li>Added <code>dumponexit</code> to agent options (Trac #82).</li>
  <li>Added <code>sessionid</code> to agent options (Trac #88).</li>
  <li>Added <code>output, address and port</code> to agent options (Trac #82).</li>
  <li>Additional and modified methods in <code>IRuntime</code> to produce
      session information (Trac #88).</li>
  <li>Coverage element type <code>SESSION</code> removed (Trac #95).</li>
  <li>Removed several internal APIs from package
      <code>org.jacoco.core.instr</code> used for class instrumentation.</li>
  <li>Renamed class <code>org.jacoco.report.csv.CsvFormatter</code> to
      <code>CSVFormatter</code> (upper case) for consistency with other
      formatters.</li>
</ul>

<h2>Release 0.3.3 (2010/04/19)</h2>

<h3>New Features</h3>
<ul>
  <li>Support for different archives (jar, war, ear etc.) and nested archives
      (Trac #78).</li>
  <li>XML report with line level coverage information (requested for Sonar).</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li>Correct stackmap frames for Java 1.6 class files. (Track #81).</li>
  <li>Avoid usage of <code>LocalVariableSorter</code> due to ASM bug #314563
      (Track #69).</li>
  <li>Nested Java/JUnit Ant tasks not being executed when coverage task disabled. (Track #85).</li>
</ul>

<h2>Release 0.3.2 (2010/04/01)</h2>

<h3>New Features</h3>
<ul>
  <li>New HTML report option to directly create a zip file containing the report
      (Trac #12).</li>
  <li>Code coverage for static initializers in interfaces (Trac #21).</li>
  <li>Better error handling for <code>report</code> Ant task (Trac #71).</li>
  <li>Classes without instructions are excluded from reports (Trac #73).</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li>XML and CSV report output now also works for structures without groups
      (Track #76).</li>
</ul>

<h3>API Changes</h3>
<ul>
  <li>Consistent usage of the term "Missed" instead of "NotCovered" in all APIs
      (Trac #72).</li>
  <li>To support "off-line" instrumentation scenarios it is not required any
      more to register instrumented classes with the runtime (Trac #74).</li>
</ul>

<h2>Release 0.3.1 (2010/02/09)</h2>

<h3>Fixed Bugs</h3>
<ul>
  <li>Ant tasks broken on Linux (Trac #68).</li>
</ul>

<h2>Release 0.3.0 (2010/02/02)</h2>

<h3>New Features</h3>
<ul>
  <li>Report renders anonymous classes with type information (Trac #46).</li>
  <li><code>enabled</code> property added to <code>agent</code> and
      <code>coverage</code> Ant tasks (Trac #63).</li>
  <li>Ant task <code>merge</code> added (Trac #52).</li>
</ul>

<h3>Fixed Bugs</h3>
<ul>
  <li>New <code>IRuntime</code> implementation enables JaCoCo usage for J2EE
  application servers like Glassfish.</li>
</ul>

<h3>API Changes</h3>
<ul>
  <li>Agent option and Ant task parameter <code>file</code> changed to
      <code>destfile</code> (Trac #59).</li>
  <li>Agent option and Ant task parameter <code>merge</code> changed to
      <code>append</code> (Trac #51).</li>
</ul>

<h2>Release 0.2.0 (2010/01/08)</h2>

<h3>New Features</h3>

<ul>
  <li>Simplified probe data structure reduces memory usage (Trac #47).</li>
  <li>Performance test becomes part of the build.</li>
  <li>New bundle <code>org.jacoco.agent</code> that provides the Java agent
      as a resource (Trac #50).</li>
</ul>

<h3>Fixed Bugs</h3>

<ul>
  <li><code>ArrayIndexOutOfBoundsException</code> due to inconsistent processing
      while instrumentation and analysis (Trac #44).</li>
</ul>

<h2>Release 0.1.0 (2009/10/28)</h2>

<p>
  The very first JaCoCo release.
</p>

</div>
<div class="footer">
  <span class="right"><a href="${jacoco.home.url}">JaCoCo</a> ${qualified.bundle.version}</span>
  <a href="license.html">Copyright</a> &copy; ${copyright.years} Mountainminds GmbH &amp; Co. KG and Contributors
</div>

</body>
</html>
