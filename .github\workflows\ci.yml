name: CI
on:
  - push
  - pull_request
jobs:
  Linux:
    strategy:
      fail-fast: false
      matrix:
        include:
          - jdk: 8
          - jdk: 8
            ecj: true
          - jdk: 11
          - jdk: 11
            ecj: true
          - jdk: 17
          - jdk: 17
            ecj: true
          - jdk: 21
          - jdk: 21
            ecj: true
    name: JDK ${{ matrix.jdk }}${{ matrix.ecj && ' with ECJ' || ''}}
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
        with:
          distribution: 'zulu'
          java-version: |
            ${{ matrix.jdk }}
            17
      - name: 'Generate toolchains.xml'
        env:
          JDK_VERSION: ${{ matrix.jdk }}
          JDK_HOME_VARIABLE_NAME: JAVA_HOME_${{ matrix.jdk }}_X64
        run: |
          echo "
          <toolchains>
            <toolchain>
              <type>jdk</type>
              <provides>
                <id>$JDK_VERSION</id>
                <version>$JDK_VERSION</version>
              </provides>
              <configuration>
                <jdkHome>${!JDK_HOME_VARIABLE_NAME}</jdkHome>
              </configuration>
            </toolchain>
          </toolchains>
          " > toolchains.xml
      - name: 'Build'
        run: |
          ./mvnw -V -B -e --no-transfer-progress \
            verify -Djdk.version=${{ matrix.jdk }} -Dbytecode.version=${{ matrix.jdk }} \
            ${{ matrix.ecj && '-Decj' || ''}} \
            --toolchains=toolchains.xml
  Windows:
    runs-on: windows-2022
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
        with:
          distribution: 'zulu'
          java-version: |
            6
            17
      - name: 'Generate toolchains.xml'
        env:
          JDK_VERSION: 6
          JDK_HOME_VARIABLE_NAME: JAVA_HOME_6_X64
        shell: bash
        run: |
          echo "
          <toolchains>
            <toolchain>
              <type>jdk</type>
              <provides>
                <id>$JDK_VERSION</id>
                <version>$JDK_VERSION</version>
              </provides>
              <configuration>
                <jdkHome>${!JDK_HOME_VARIABLE_NAME}</jdkHome>
              </configuration>
            </toolchain>
          </toolchains>
          " > toolchains.xml
      - name: 'Build'
        shell: bash
        run: |
          ./mvnw -V -B -e --no-transfer-progress \
            verify -Djdk.version=6 -Dbytecode.version=5 \
            --toolchains=toolchains.xml
      - uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        id: artifact-upload-step
        with:
          name: jacoco
          path: jacoco/target/*.zip
          if-no-files-found: error
