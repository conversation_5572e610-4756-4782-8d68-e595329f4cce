/*******************************************************************************
 * Copyright (c) 2009, 2025 Mountainminds GmbH & Co. KG and Contributors
 * This program and the accompanying materials are made available under
 * the terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 * Contributors:
 *    <PERSON>, <PERSON> - initial API and implementation
 *
 *******************************************************************************/
package package2;

import org.junit.Test;

public class Example2Test {

	@Test
	public void test() {
		new Example2().a();
	}

}
