.el_jar {
  padding-left:18px;
  background-image:url(jar.gif);
  background-position:left center;
  background-repeat:no-repeat;
}

.el_test {
  padding-left:18px;
  background-image:url(test.gif);
  background-position:left center;
  background-repeat:no-repeat;
}

.el_testsuite {
  padding-left:18px;
  background-image:url(testsuite.gif);
  background-position:left center;
  background-repeat:no-repeat;
}

p.intro {
  margin-left:20px;
  padding-left:4px;
  border-left:#cccca0 4px solid;
  font-style:italic
}

p.hint {
  margin-left:20px;
  padding-left:4px;
  border-left:#cccca0 4px solid;
  font-style:italic
}

.high {
  background-color:#ffff80;
}

div#content a[href^='http://'], a[href^='https://'] {
  display:inline-block;
  padding-left:15px;
  background:transparent url(extern.gif) center left no-repeat;
}

/* === Definitions from report.css start here: === */

body, td {
  font-family:sans-serif;
  font-size:10pt;
}

h1 {
  font-weight:bold;
  font-size:18pt;
}

.breadcrumb {
  border:#d6d3ce 1px solid;
  padding:2px 4px 2px 4px;
}


.el_report {
  padding-left:18px;
  background-image:url(report.gif);
  background-position:left center;
  background-repeat:no-repeat;
}

.el_group {
  padding-left:18px;
  background-image:url(group.gif);
  background-position:left center;
  background-repeat:no-repeat;
}

.el_bundle {
  padding-left:18px;
  background-image:url(bundle.gif);
  background-position:left center;
  background-repeat:no-repeat;
}

.el_package {
  padding-left:18px;
  background-image:url(package.gif);
  background-position:left center;
  background-repeat:no-repeat;
}

.el_class {
  padding-left:18px;
  background-image:url(class.gif);
  background-position:left center;
  background-repeat:no-repeat;
}

.el_source {
  padding-left:18px;
  background-image:url(source.gif);
  background-position:left center;
  background-repeat:no-repeat;
}

.el_method {
  padding-left:18px;
  background-image:url(method.gif);
  background-position:left center;
  background-repeat:no-repeat;
}

.el_session {
  padding-left:18px;
  background-image:url(session.gif);
  background-position:left center;
  background-repeat:no-repeat;
}

pre.source {
  border:#d6d3ce 1px solid;
  font-family:monospace;
}

pre.source ol {
  margin-bottom: 0px;
  margin-top: 0px;
}

pre.source li {
  border-left: 1px solid #D6D3CE;
  color: #A0A0A0;
  padding-left: 0px;
}

pre.source span.fc {
  background-color:#ccffcc;
}

pre.source span.nc {
  background-color:#ffcccc;
}

pre.source span.pc {
  background-color:#ffffcc;
}


table.coverage {
  empty-cells:show;
  border-collapse:collapse;
}

table.coverage thead {
  background-color:#e0e0e0;
}

table.coverage thead td {
  white-space:nowrap;
  padding:2px 8px 0px 8px;
  border-bottom:#b0b0b0 1px solid;
}

table.coverage thead td.ctr1 {
  text-align:right;
  padding-right:4px;
  border-left:#cccccc 1px solid;
}

table.coverage thead td.ctr2 {
  text-align:right;
  padding-left:4px;
}

table.coverage tbody td {
  vertical-align:top;
  padding:2px 8px 2px 8px;
  border-bottom:#d6d3ce 1px solid;
}

table.coverage tbody tr:hover {
  background: #f0f0d0 !important;
}

table.coverage tbody td.ctr1 {
  text-align:right;
  padding-right:4px;
  border-left:#e8e8e8 1px solid;
}

table.coverage tbody td.ctr2 {
  text-align:right;
  padding-left:4px;
}

table.coverage tfoot td {
  padding:2px 8px 2px 8px;
}

table.coverage tfoot td.ctr1 {
  text-align:right;
  padding-right:4px;
  border-left:#e8e8e8 1px solid;
}

table.coverage tfoot td.ctr2 {
  text-align:right;
  padding-left:4px;
}

.footer {
  margin-top:20px;
  border-top:#d6d3ce 1px solid;
  padding-top:2px;
  font-size:8pt;
  color:#a0a0a0;
}

.footer a {
  color:#a0a0a0;
}

.right {
  float:right;
}
