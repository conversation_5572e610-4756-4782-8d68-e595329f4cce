<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <link rel="stylesheet" href="resources/doc.css" charset="UTF-8" type="text/css" />
  <link rel="shortcut icon" href="resources/report.gif" type="image/gif" />
  <title>JaCoCo - Mission</title>
</head>
<body>

<div class="breadcrumb">
  <a href="../index.html" class="el_report">JaCoCo</a> &gt;
  <a href="index.html" class="el_group">Documentation</a> &gt;
  <span class="el_source">Mission</span>
</div>
<div id="content">

<h1>Mission</h1>

<p class="intro">
  JaCoCo should provide the standard technology for code coverage analysis in
  Java VM based environments. The focus is providing a lightweight, flexible and
  well documented library for integration with various build and development
  tools.
</p>

<p>
  There are several open source coverage technologies for Java available. While
  implementing the Eclipse plug-in <a href="http://www.eclemma.org/">EclEmma</a>
  the observation was that none of them are really designed for integration.
  Most of them are specifically fit to a particular tool (Ant tasks, command
  line, IDE plug-in) and do not offer a documented API that allows embedding in
  different contexts. Two of the best and widely used available open source
  tools are <a href="http://emma.sourceforge.net/">EMMA</a> and
  <a href="http://cobertura.sourceforge.net/">Cobertura</a>. Both tools are not
  actively maintained by the original authors any more and do not support the
  current Java versions. Due to the lack of regression tests maintenance and
  feature additions is difficult.
</p>

<p>
  Therefore we started the JaCoCo project to provide a new standard technology
  for code coverage analysis in Java VM based environments. The focus is
  providing a lightweight, flexible and well documented library for integration
  with various build and development tools. <a href="ant.html">Ant tasks</a>, a
  <a href="maven.html">Maven plug-in</a> and the
  <a href="http://www.eclemma.org/">EclEmma Eclipse plug-in</a> are provided as
  reference usage scenarios. Also many other tool vendors and Open Source
  projects have <a href="integrations.html">integrated</a> JaCoCo into their
  tools.
</p>

<h2>Product Definition</h2>

<h3>Features</h3>
<ul>
  <li>Coverage <a href="counters.html">analysis</a> of instructions (C0),
      branches (C1), lines, methods, types and cyclomatic complexity.</li>
  <li>Based on Java byte code and therefore works also without source files.</li>
  <li>Simple integration through <a href="agent.html">Java agent</a> based
      on-the-fly instrumentation. Other integration scenarios like custom class
      loaders are possible through the API.</li>
  <li>Framework agnostic: Smoothly integrates with Java VM based applications
      like plain Java programs, OSGi frameworks, web containers or EJB servers.</li>
  <li>Compatible with all released Java class file versions.</li>
  <li>Support for different
      <a href="http://en.wikipedia.org/wiki/List_of_JVM_languages">JVM languages</a>.</li>
  <li>Several report formats (HTML, XML, CSV).</li>
  <li>Remote protocol and JMX control to request execution data dumps from the
      coverage agent at any point in time.</li>
  <li><a href="ant.html">Ant tasks</a> to collect and manage execution
      data and create structured coverage reports.</li>
  <li><a href="maven.html">Maven plug-in</a> to collect coverage information
      and create reports in Maven builds.</li>
</ul>

<h3>Non-Functional Characteristics</h3>
<ul>
  <li>Simple usage and integration with existing build scripts and tools.</li>
  <li>Good performance with minimal runtime overhead especially for large scale
      projects.</li>
  <li>Lightweight implementation with minimal dependencies on external libraries
      and system resources.</li>
  <li>Comprehensive documentation.</li>
  <li>Fully documented APIs (<a href="api/index.html">JavaDoc</a>) and
      <a href="api.html">examples</a> for <a href="integrations.html">integration</a>
      with other tools.</li>
  <li>Regression <a href="../test/index.html">tests</a> with full functional
      test <a href="../coverage/index.html">coverage</a> based on
      <a href="http://junit.org/">JUnit</a> test cases.</li>
</ul>

</div>
<div class="footer">
  <span class="right"><a href="${jacoco.home.url}">JaCoCo</a> ${qualified.bundle.version}</span>
  <a href="license.html">Copyright</a> &copy; ${copyright.years} Mountainminds GmbH &amp; Co. KG and Contributors
</div>

</body>
</html>
