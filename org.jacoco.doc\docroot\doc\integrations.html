<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <link rel="stylesheet" href="resources/doc.css" charset="UTF-8" type="text/css" />
  <link rel="shortcut icon" href="resources/report.gif" type="image/gif" />
  <title>JaCoCo - Integration Matrix</title>
</head>
<body>

<div class="breadcrumb">
  <a href="../index.html" class="el_report">JaCoCo</a> &gt;
  <a href="index.html" class="el_group">Documentation</a> &gt;
  <span class="el_source">Integration Matrix</span>
</div>
<div id="content">

<h1>Integration Matrix</h1>

<p>
  Currently JaCoCo is integrated with the following products and technologies.
</p>

<h3>Integrations provided by the JaCoCo/EclEmma project</h3>

<table class="coverage">
  <thead>
    <tr>
      <td>Technology</td>
      <td>Documentation</td>
      <td>Remarks</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Java API</td>
      <td><a href="api/index.html">JaCoCo JavaDoc</a></td>
      <td></td>
    </tr>
    <tr>
      <td>Java Agent</td>
      <td><a href="agent.html">JaCoCo Manual</a></td>
      <td></td>
    </tr>
    <tr>
      <td>Command Line Tools</td>
      <td><a href="cli.html">JaCoCo Manual</a></td>
      <td>Since version 0.8.0</td>
    </tr>
    <tr>
      <td>Apache Ant</td>
      <td><a href="ant.html">JaCoCo Manual</a></td>
      <td></td>
    </tr>
    <tr>
      <td>Apache Maven</td>
      <td><a href="maven.html">JaCoCo Manual</a></td>
      <td>Since version 0.5.3</td>
    </tr>
    <tr>
      <td>Eclipse</td>
      <td><a href="http://www.eclemma.org/">EclEmma Project</a></td>
      <td>Since version 2.0</td>
    </tr>
  </tbody>
</table>

<h3>Third-Party Integrations</h3>

<table class="coverage">
  <thead>
    <tr>
      <td>Product</td>
      <td>Remarks</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><a href="http://arquillian.org/">Arquillian</a></td>
      <td>Java EE testing framework, <a href="http://arquillian.org/modules/jacoco-extension/">JaCoCo extension</a></td>
    </tr>
    <tr>
      <td><a href="https://azure.microsoft.com/services/devops/">Azure DevOps</a></td>
      <td>Cloud-powered collaboration tools by Microsoft, see <a href="https://docs.microsoft.com/en-us/azure/devops/pipelines/tasks/test/publish-code-coverage-results">documentation</a></td>
    </tr>
    <tr>
      <td><a href="https://rife2.com/bld">bld</a></td>
      <td>Pure Java build System with JaCoCo extension, see <a href="https://github.com/rife2/bld-jacoco-report">documentation</a></td>
    </tr>
    <tr>
      <td><a href="https://www.codacy.com/">Codacy</a></td>
      <td>Platform to track code coverage and code quality, see <a href="https://support.codacy.com/hc/en-us/articles/207279819-Coverage">documentation</a></td>
    </tr>
    <tr>
      <td><a href="https://codecov.io/">Codecov</a></td>
      <td>Web service to track code coverage, see <a href="https://github.com/codecov/example-java">example</a></td>
    </tr>
    <tr>
      <td><a href="https://coveralls.io/">Coveralls</a></td>
      <td>Web service to track code coverage, see <a href="https://github.com/trautonen/coveralls-maven-plugin">coveralls-maven-plugin</a></td>
    </tr>
    <tr>
      <td><a href="https://www.stamp-project.eu/">STAMP</a></td>
      <td>EU research project with test generation tool for JUnit, see <a href="https://github.com/STAMP-project/dspot">DSpot project page</a></td>
    </tr>
    <tr>
      <td><a href="http://www.gradle.org/">Gradle</a></td>
      <td>Build System with JaCoCo plug-in, see <a href="http://www.gradle.org/docs/current/userguide/jacoco_plugin.html">documentation</a></td>
    </tr>
    <tr>
      <td><a href="http://www.jetbrains.com/idea/">IntelliJ IDEA</a></td>
      <td>Since version 11.1, see <a href="http://www.jetbrains.com/idea/webhelp/code-coverage.html">documentation</a></td>
    </tr>
    <tr>
      <td><a href="http://jenkins-ci.org/">Jenkins</a></td>
      <td>GSoC project of Shenyu Zheng, see <a href="https://github.com/jenkinsci/code-coverage-api-plugin">project page</a></td>
    </tr>
    <tr>
      <td><a href="http://jenkins-ci.org/">Jenkins</a></td>
      <td>GSoC project of Ognjen Bubalo, see <a href="https://wiki.jenkins-ci.org/display/JENKINS/JaCoCo+Plugin">documentation</a></td>
    </tr>
    <tr>
      <td><a href="https://www.eclipse.org/jubula/">Jubula</a></td>
      <td>Functional GUI testing tool</td>
    </tr>
    <tr>
      <td><a href="http://netbeans.org/">NetBeans</a></td>
      <td>Since version 7.2, see <a href="http://wiki.netbeans.org/MavenCodeCoverage">documentation</a>,
          <a href="http://plugins.netbeans.org/plugin/48570/tikione-jacocoverage">plug-in</a> for Ant based projects</td>
    </tr>
    <tr>
      <td><a href="https://www.scala-sbt.org/">sbt</a></td>
      <td>Scala Build Tool, see <a href="https://www.scala-sbt.org/sbt-jacoco/">JaCoCo plug-in</a></td>
    </tr>
    <tr>
      <td><a href="http://www.shippable.com/">Shippable</a></td>
      <td>Continuous integration and delivery platform, see <a href="http://docs.shippable.com/ci/jacoco-reports/">documentation</a></td>
    </tr>
    <tr>
      <td><a href="https://www.skippy.io/">Skippy</a></td>
      <td>Test Impact Analysis &amp; Predictive Test Selection framework for the JVM, see <a href="https://www.skippy.io/docs/">documentation</a></td>
    </tr>
    <tr>
      <td><a href="http://www.sonarqube.org/">SonarQube</a></td>
      <td>Continuous inspection platform with JaCoCo support, see <a href="https://docs.sonarqube.org/latest/analysis/coverage/">documentation</a></td>
    </tr>
    <tr>
      <td><a href="http://www.jetbrains.com/teamcity/">TeamCity</a></td>
      <td>Continuous integration server with JaCoCo support since version 8.1, see <a href="https://www.jetbrains.com/help/teamcity/jacoco.html">documentation</a></td>
    </tr>
    <tr>
      <td><a href="https://developer.ibm.com/urbancode/">Urban Code</a></td>
      <td>Continuous delivery platform by IBM with <a href="https://developer.ibm.com/urbancode/plugin/jacoco-3519516/">JaCoCo plug-in</a></td>
    </tr>
  </tbody>
</table>

<p>
  As <a href="license.html">always</a>, all trademarks listed above are the
  property of their respective owners.
</p>

</div>
<div class="footer">
  <span class="right"><a href="${jacoco.home.url}">JaCoCo</a> ${qualified.bundle.version}</span>
  <a href="license.html">Copyright</a> &copy; ${copyright.years} Mountainminds GmbH &amp; Co. KG and Contributors
</div>

</body>
</html>
