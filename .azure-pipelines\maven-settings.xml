<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
  <servers>
    <server>
      <id>sonatype-nexus-snapshots</id>
      <username>${env.SONATYPE_USERNAME}</username>
      <password>${env.SONATYPE_PASSWORD}</password>
    </server>
  </servers>
  <profiles>
    <profile>
      <id>SonarCloud</id>
      <properties>
        <sonar.host.url>https://sonarcloud.io/</sonar.host.url>
        <sonar.organization>default</sonar.organization>
        <sonar.token>${env.SONARQUBE_TOKEN}</sonar.token>
      </properties>
    </profile>
  </profiles>
  <activeProfiles>
    <activeProfile>SonarCloud</activeProfile>
  </activeProfiles>
</settings>
