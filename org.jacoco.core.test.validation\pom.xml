<?xml version="1.0" encoding="UTF-8"?>
<!--
   Copyright (c) 2009, 2025 Mountainminds GmbH & Co. KG and Contributors
   This program and the accompanying materials are made available under
   the terms of the Eclipse Public License 2.0 which is available at
   http://www.eclipse.org/legal/epl-2.0

   SPDX-License-Identifier: EPL-2.0

   Contributors:
      <PERSON><PERSON><PERSON> - initial API and implementation
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.jacoco</groupId>
    <artifactId>org.jacoco.tests</artifactId>
    <version>0.8.14-SNAPSHOT</version>
    <relativePath>../org.jacoco.tests</relativePath>
  </parent>

  <artifactId>org.jacoco.core.test.validation</artifactId>
  <packaging>pom</packaging>

  <name>JaCoCo :: Test :: Core :: Validation</name>

  <modules>
    <module>../org.jacoco.core.test.validation.java5</module>
  </modules>

  <properties>
    <jacoco.skip>true</jacoco.skip>

    <!-- when bytecode.version not specified -->
    <groovy.targetBytecode>1.8</groovy.targetBytecode>
  </properties>

  <profiles>
    <profile>
      <id>java5-bytecode</id>
      <activation>
        <property>
          <name>bytecode.version</name>
          <value>5</value>
        </property>
      </activation>
    </profile>

    <profile>
      <id>jdk5</id>
      <activation>
        <property>
          <name>jdk.version</name>
          <value>5</value>
        </property>
      </activation>
    </profile>

    <profile>
      <id>java6-bytecode</id>
      <activation>
        <property>
          <name>bytecode.version</name>
          <value>6</value>
        </property>
      </activation>
      <properties>
      </properties>
      <modules>
      </modules>
    </profile>

    <profile>
      <id>jdk6</id>
      <activation>
        <property>
          <name>jdk.version</name>
          <value>6</value>
        </property>
      </activation>
      <modules>
      </modules>
    </profile>

    <profile>
      <id>java7-bytecode</id>
      <activation>
        <property>
          <name>bytecode.version</name>
          <value>7</value>
        </property>
      </activation>
      <properties>
      </properties>
      <modules>
        <module>../org.jacoco.core.test.validation.java7</module>
      </modules>
    </profile>

    <profile>
      <id>jdk7</id>
      <activation>
        <property>
          <name>jdk.version</name>
          <value>7</value>
        </property>
      </activation>
      <modules>
        <module>../org.jacoco.core.test.validation.java7</module>
      </modules>
    </profile>

    <profile>
      <id>default</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <properties>
        <kotlin.compiler.jvmTarget>1.8</kotlin.compiler.jvmTarget>
      </properties>
      <modules>
        <module>../org.jacoco.core.test.validation.kotlin</module>
        <module>../org.jacoco.core.test.validation.java7</module>
        <module>../org.jacoco.core.test.validation.java8</module>
        <module>../org.jacoco.core.test.validation.groovy</module>
        <module>../org.jacoco.core.test.validation.scala</module>
      </modules>
    </profile>

    <profile>
      <id>java8-bytecode</id>
      <activation>
        <property>
          <name>bytecode.version</name>
          <value>8</value>
        </property>
      </activation>
      <properties>
        <kotlin.compiler.jvmTarget>1.8</kotlin.compiler.jvmTarget>
        <groovy.targetBytecode>1.8</groovy.targetBytecode>
      </properties>
      <modules>
        <module>../org.jacoco.core.test.validation.kotlin</module>
        <module>../org.jacoco.core.test.validation.java7</module>
        <module>../org.jacoco.core.test.validation.java8</module>
        <module>../org.jacoco.core.test.validation.groovy</module>
        <module>../org.jacoco.core.test.validation.scala</module>
      </modules>
    </profile>

    <profile>
      <id>java9-bytecode</id>
      <activation>
        <property>
          <name>bytecode.version</name>
          <value>9</value>
        </property>
      </activation>
      <properties>
        <kotlin.compiler.jvmTarget>9</kotlin.compiler.jvmTarget>
        <groovy.targetBytecode>9</groovy.targetBytecode>
      </properties>
      <modules>
        <module>../org.jacoco.core.test.validation.kotlin</module>
        <module>../org.jacoco.core.test.validation.java7</module>
        <module>../org.jacoco.core.test.validation.java8</module>
        <module>../org.jacoco.core.test.validation.groovy</module>
      </modules>
    </profile>

    <profile>
      <id>java10-bytecode</id>
      <activation>
        <property>
          <name>bytecode.version</name>
          <value>10</value>
        </property>
      </activation>
      <properties>
        <kotlin.compiler.jvmTarget>10</kotlin.compiler.jvmTarget>
        <groovy.targetBytecode>10</groovy.targetBytecode>
      </properties>
      <modules>
        <module>../org.jacoco.core.test.validation.kotlin</module>
        <module>../org.jacoco.core.test.validation.java7</module>
        <module>../org.jacoco.core.test.validation.java8</module>
        <module>../org.jacoco.core.test.validation.groovy</module>
      </modules>
    </profile>

    <profile>
      <id>java11-bytecode</id>
      <activation>
        <property>
          <name>bytecode.version</name>
          <value>11</value>
        </property>
      </activation>
      <properties>
        <kotlin.compiler.jvmTarget>11</kotlin.compiler.jvmTarget>
        <groovy.targetBytecode>11</groovy.targetBytecode>
      </properties>
      <modules>
        <module>../org.jacoco.core.test.validation.kotlin</module>
        <module>../org.jacoco.core.test.validation.java7</module>
        <module>../org.jacoco.core.test.validation.java8</module>
        <module>../org.jacoco.core.test.validation.groovy</module>
        <module>../org.jacoco.core.test.validation.scala</module>
      </modules>
    </profile>

    <profile>
      <id>java12-bytecode</id>
      <activation>
        <property>
          <name>bytecode.version</name>
          <value>12</value>
        </property>
      </activation>
      <properties>
        <kotlin.compiler.jvmTarget>12</kotlin.compiler.jvmTarget>
        <groovy.targetBytecode>12</groovy.targetBytecode>
      </properties>
      <modules>
        <module>../org.jacoco.core.test.validation.kotlin</module>
        <module>../org.jacoco.core.test.validation.java7</module>
        <module>../org.jacoco.core.test.validation.java8</module>
        <module>../org.jacoco.core.test.validation.groovy</module>
        <module>../org.jacoco.core.test.validation.scala</module>
      </modules>
    </profile>

    <profile>
      <id>java13-bytecode</id>
      <activation>
        <property>
          <name>bytecode.version</name>
          <value>13</value>
        </property>
      </activation>
      <properties>
        <kotlin.compiler.jvmTarget>13</kotlin.compiler.jvmTarget>
        <groovy.targetBytecode>13</groovy.targetBytecode>
        <!-- see respective profile in org.jacoco.build about this override -->
        <maven.compiler.source>13</maven.compiler.source>
        <maven.compiler.target>13</maven.compiler.target>
      </properties>
      <modules>
        <module>../org.jacoco.core.test.validation.kotlin</module>
        <module>../org.jacoco.core.test.validation.java7</module>
        <module>../org.jacoco.core.test.validation.java8</module>
        <module>../org.jacoco.core.test.validation.groovy</module>
        <module>../org.jacoco.core.test.validation.scala</module>
      </modules>
    </profile>

    <profile>
      <id>java14-bytecode</id>
      <activation>
        <property>
          <name>bytecode.version</name>
          <value>14</value>
        </property>
      </activation>
      <properties>
        <kotlin.compiler.jvmTarget>14</kotlin.compiler.jvmTarget>
        <groovy.targetBytecode>14</groovy.targetBytecode>
        <!-- see respective profile in org.jacoco.build about this override -->
        <maven.compiler.source>14</maven.compiler.source>
        <maven.compiler.target>14</maven.compiler.target>
      </properties>
      <modules>
        <module>../org.jacoco.core.test.validation.kotlin</module>
        <module>../org.jacoco.core.test.validation.java7</module>
        <module>../org.jacoco.core.test.validation.java8</module>
        <module>../org.jacoco.core.test.validation.java14</module>
        <module>../org.jacoco.core.test.validation.groovy</module>
        <module>../org.jacoco.core.test.validation.scala</module>
      </modules>
    </profile>

    <profile>
      <id>java15-bytecode</id>
      <activation>
        <property>
          <name>bytecode.version</name>
          <value>15</value>
        </property>
      </activation>
      <properties>
        <kotlin.compiler.jvmTarget>15</kotlin.compiler.jvmTarget>
        <groovy.targetBytecode>15</groovy.targetBytecode>
        <!-- see respective profile in org.jacoco.build about this override -->
        <maven.compiler.source>15</maven.compiler.source>
        <maven.compiler.target>15</maven.compiler.target>
      </properties>
      <modules>
        <module>../org.jacoco.core.test.validation.kotlin</module>
        <module>../org.jacoco.core.test.validation.java7</module>
        <module>../org.jacoco.core.test.validation.java8</module>
        <module>../org.jacoco.core.test.validation.java14</module>
        <module>../org.jacoco.core.test.validation.groovy</module>
        <module>../org.jacoco.core.test.validation.scala</module>
      </modules>
    </profile>

    <profile>
      <id>java16-bytecode</id>
      <activation>
        <property>
          <name>bytecode.version</name>
          <value>16</value>
        </property>
      </activation>
      <properties>
        <kotlin.compiler.jvmTarget>16</kotlin.compiler.jvmTarget>
        <groovy.targetBytecode>16</groovy.targetBytecode>
        <!-- see respective profile in org.jacoco.build about this override -->
        <maven.compiler.source>16</maven.compiler.source>
        <maven.compiler.target>16</maven.compiler.target>
      </properties>
      <modules>
        <module>../org.jacoco.core.test.validation.kotlin</module>
        <module>../org.jacoco.core.test.validation.java7</module>
        <module>../org.jacoco.core.test.validation.java8</module>
        <module>../org.jacoco.core.test.validation.java14</module>
        <module>../org.jacoco.core.test.validation.java16</module>
        <module>../org.jacoco.core.test.validation.groovy</module>
        <module>../org.jacoco.core.test.validation.scala</module>
      </modules>
    </profile>

    <profile>
      <id>java17-bytecode</id>
      <activation>
        <property>
          <name>bytecode.version</name>
          <value>17</value>
        </property>
      </activation>
      <properties>
        <kotlin.compiler.jvmTarget>17</kotlin.compiler.jvmTarget>
        <groovy.targetBytecode>17</groovy.targetBytecode>
        <!-- see respective profile in org.jacoco.build about this override -->
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
      </properties>
      <modules>
        <module>../org.jacoco.core.test.validation.kotlin</module>
        <module>../org.jacoco.core.test.validation.java7</module>
        <module>../org.jacoco.core.test.validation.java8</module>
        <module>../org.jacoco.core.test.validation.java14</module>
        <module>../org.jacoco.core.test.validation.java16</module>
        <module>../org.jacoco.core.test.validation.groovy</module>
        <module>../org.jacoco.core.test.validation.scala</module>
      </modules>
    </profile>

    <profile>
      <id>java18-bytecode</id>
      <activation>
        <property>
          <name>bytecode.version</name>
          <value>18</value>
        </property>
      </activation>
      <properties>
        <kotlin.compiler.jvmTarget>18</kotlin.compiler.jvmTarget>
        <!-- Groovy 3.0.22 does not support compilation into 18 -->
        <groovy.targetBytecode>16</groovy.targetBytecode>
        <!-- see respective profile in org.jacoco.build about this override -->
        <maven.compiler.source>18</maven.compiler.source>
        <maven.compiler.target>18</maven.compiler.target>
      </properties>
      <modules>
        <module>../org.jacoco.core.test.validation.kotlin</module>
        <module>../org.jacoco.core.test.validation.java7</module>
        <module>../org.jacoco.core.test.validation.java8</module>
        <module>../org.jacoco.core.test.validation.java14</module>
        <module>../org.jacoco.core.test.validation.java16</module>
        <module>../org.jacoco.core.test.validation.groovy</module>
        <module>../org.jacoco.core.test.validation.scala</module>
      </modules>
    </profile>

    <profile>
      <id>java19-bytecode</id>
      <activation>
        <property>
          <name>bytecode.version</name>
          <value>19</value>
        </property>
      </activation>
      <properties>
        <kotlin.compiler.jvmTarget>19</kotlin.compiler.jvmTarget>
        <!-- Groovy 3.0.22 does not support compilation into 19 -->
        <groovy.targetBytecode>16</groovy.targetBytecode>
        <!-- see respective profile in org.jacoco.build about this override -->
        <maven.compiler.source>19</maven.compiler.source>
        <maven.compiler.target>19</maven.compiler.target>
      </properties>
      <modules>
        <module>../org.jacoco.core.test.validation.kotlin</module>
        <module>../org.jacoco.core.test.validation.java7</module>
        <module>../org.jacoco.core.test.validation.java8</module>
        <module>../org.jacoco.core.test.validation.java14</module>
        <module>../org.jacoco.core.test.validation.java16</module>
        <module>../org.jacoco.core.test.validation.groovy</module>
        <module>../org.jacoco.core.test.validation.scala</module>
      </modules>
    </profile>

    <profile>
      <id>java20-bytecode</id>
      <activation>
        <property>
          <name>bytecode.version</name>
          <value>20</value>
        </property>
      </activation>
      <properties>
        <kotlin.compiler.jvmTarget>20</kotlin.compiler.jvmTarget>
        <!-- Groovy 3.0.22 does not support compilation into 20 -->
        <groovy.targetBytecode>16</groovy.targetBytecode>
        <!-- see respective profile in org.jacoco.build about this override -->
        <maven.compiler.source>20</maven.compiler.source>
        <maven.compiler.target>20</maven.compiler.target>
      </properties>
      <modules>
        <module>../org.jacoco.core.test.validation.kotlin</module>
        <module>../org.jacoco.core.test.validation.java7</module>
        <module>../org.jacoco.core.test.validation.java8</module>
        <module>../org.jacoco.core.test.validation.java14</module>
        <module>../org.jacoco.core.test.validation.java16</module>
        <module>../org.jacoco.core.test.validation.groovy</module>
        <module>../org.jacoco.core.test.validation.scala</module>
      </modules>
    </profile>

    <profile>
      <id>java21-bytecode</id>
      <activation>
        <property>
          <name>bytecode.version</name>
          <value>21</value>
        </property>
      </activation>
      <properties>
        <kotlin.compiler.jvmTarget>21</kotlin.compiler.jvmTarget>
        <!-- Groovy 3.0.22 does not support compilation into 21 -->
        <groovy.targetBytecode>16</groovy.targetBytecode>
        <!-- see respective profile in org.jacoco.build about this override -->
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
      </properties>
      <modules>
        <module>../org.jacoco.core.test.validation.kotlin</module>
        <module>../org.jacoco.core.test.validation.java7</module>
        <module>../org.jacoco.core.test.validation.java8</module>
        <module>../org.jacoco.core.test.validation.java14</module>
        <module>../org.jacoco.core.test.validation.java16</module>
        <module>../org.jacoco.core.test.validation.java21</module>
        <module>../org.jacoco.core.test.validation.groovy</module>
        <module>../org.jacoco.core.test.validation.scala</module>
      </modules>
    </profile>

    <profile>
      <id>java22-bytecode</id>
      <activation>
        <property>
          <name>bytecode.version</name>
          <value>22</value>
        </property>
      </activation>
      <properties>
        <kotlin.compiler.jvmTarget>22</kotlin.compiler.jvmTarget>
        <!-- Groovy 3.0.22 does not support compilation into 22 -->
        <groovy.targetBytecode>16</groovy.targetBytecode>
        <!-- see respective profile in org.jacoco.build about this override -->
        <maven.compiler.source>22</maven.compiler.source>
        <maven.compiler.target>22</maven.compiler.target>
      </properties>
      <modules>
        <module>../org.jacoco.core.test.validation.kotlin</module>
        <module>../org.jacoco.core.test.validation.java7</module>
        <module>../org.jacoco.core.test.validation.java8</module>
        <module>../org.jacoco.core.test.validation.java14</module>
        <module>../org.jacoco.core.test.validation.java16</module>
        <module>../org.jacoco.core.test.validation.java21</module>
        <module>../org.jacoco.core.test.validation.groovy</module>
        <module>../org.jacoco.core.test.validation.scala</module>
      </modules>
    </profile>

    <profile>
      <id>java23-bytecode</id>
      <activation>
        <property>
          <name>bytecode.version</name>
          <value>23</value>
        </property>
      </activation>
      <properties>
        <kotlin.compiler.jvmTarget>23</kotlin.compiler.jvmTarget>
        <!-- Groovy 3.0.22 does not support compilation into 23 -->
        <groovy.targetBytecode>16</groovy.targetBytecode>
        <!-- see respective profile in org.jacoco.build about this override -->
        <maven.compiler.source>23</maven.compiler.source>
        <maven.compiler.target>23</maven.compiler.target>
      </properties>
      <modules>
        <module>../org.jacoco.core.test.validation.kotlin</module>
        <module>../org.jacoco.core.test.validation.java7</module>
        <module>../org.jacoco.core.test.validation.java8</module>
        <module>../org.jacoco.core.test.validation.java14</module>
        <module>../org.jacoco.core.test.validation.java16</module>
        <module>../org.jacoco.core.test.validation.java21</module>
        <module>../org.jacoco.core.test.validation.groovy</module>
        <module>../org.jacoco.core.test.validation.scala</module>
      </modules>
    </profile>

    <profile>
      <id>java24-bytecode</id>
      <activation>
        <property>
          <name>bytecode.version</name>
          <value>24</value>
        </property>
      </activation>
      <properties>
        <!-- Kotlin 2.1.0 doesn't support compilation into 24 -->
        <kotlin.compiler.jvmTarget>23</kotlin.compiler.jvmTarget>
        <!-- Groovy 3.0.22 does not support compilation into 24 -->
        <groovy.targetBytecode>16</groovy.targetBytecode>
        <!-- see respective profile in org.jacoco.build about this override -->
        <maven.compiler.source>24</maven.compiler.source>
        <maven.compiler.target>24</maven.compiler.target>
      </properties>
      <modules>
        <module>../org.jacoco.core.test.validation.kotlin</module>
        <module>../org.jacoco.core.test.validation.java7</module>
        <module>../org.jacoco.core.test.validation.java8</module>
        <module>../org.jacoco.core.test.validation.java14</module>
        <module>../org.jacoco.core.test.validation.java16</module>
        <module>../org.jacoco.core.test.validation.java21</module>
        <!-- Groovy 3.0.22 does not support Java 24
        <module>../org.jacoco.core.test.validation.groovy</module>
        -->
        <module>../org.jacoco.core.test.validation.scala</module>
      </modules>
    </profile>

    <profile>
      <id>java25-bytecode</id>
      <activation>
        <property>
          <name>bytecode.version</name>
          <value>25</value>
        </property>
      </activation>
      <properties>
        <!-- Kotlin 2.1.0 doesn't support compilation into 25 -->
        <kotlin.compiler.jvmTarget>23</kotlin.compiler.jvmTarget>
        <!-- Groovy 3.0.22 does not support compilation into 25 -->
        <groovy.targetBytecode>16</groovy.targetBytecode>
        <!-- see respective profile in org.jacoco.build about this override -->
        <maven.compiler.source>25</maven.compiler.source>
        <maven.compiler.target>25</maven.compiler.target>
      </properties>
      <modules>
        <module>../org.jacoco.core.test.validation.kotlin</module>
        <module>../org.jacoco.core.test.validation.java7</module>
        <module>../org.jacoco.core.test.validation.java8</module>
        <module>../org.jacoco.core.test.validation.java14</module>
        <module>../org.jacoco.core.test.validation.java16</module>
        <module>../org.jacoco.core.test.validation.java21</module>
        <!-- Groovy 3.0.22 does not support Java 25
        <module>../org.jacoco.core.test.validation.groovy</module>
        -->
        <module>../org.jacoco.core.test.validation.scala</module>
      </modules>
    </profile>

    <profile>
      <id>java26-bytecode</id>
      <activation>
        <property>
          <name>bytecode.version</name>
          <value>26</value>
        </property>
      </activation>
      <properties>
        <!-- Kotlin 2.1.0 doesn't support compilation into 26 -->
        <kotlin.compiler.jvmTarget>23</kotlin.compiler.jvmTarget>
        <!-- Groovy 3.0.22 does not support compilation into 26 -->
        <groovy.targetBytecode>16</groovy.targetBytecode>
        <!-- see respective profile in org.jacoco.build about this override -->
        <maven.compiler.source>26</maven.compiler.source>
        <maven.compiler.target>26</maven.compiler.target>
      </properties>
      <modules>
        <module>../org.jacoco.core.test.validation.kotlin</module>
        <module>../org.jacoco.core.test.validation.java7</module>
        <module>../org.jacoco.core.test.validation.java8</module>
        <module>../org.jacoco.core.test.validation.java14</module>
        <module>../org.jacoco.core.test.validation.java16</module>
        <module>../org.jacoco.core.test.validation.java21</module>
        <!-- Groovy 3.0.22 does not support Java 26
        <module>../org.jacoco.core.test.validation.groovy</module>
        -->
        <module>../org.jacoco.core.test.validation.scala</module>
      </modules>
    </profile>

  </profiles>

</project>
