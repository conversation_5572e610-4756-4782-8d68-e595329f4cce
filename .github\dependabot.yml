version: 2
updates:
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
    labels:
      - "dependencies"
      - "component: build"
  - package-ecosystem: "maven"
    directory: "/org.jacoco.build"
    schedule:
      interval: "weekly"
    labels:
      - "dependencies"
      - "component: build"
    allow:
      - dependency-name: "org.apache.maven.plugins:*"
      - dependency-name: "org.codehaus.mojo:*"
      - dependency-name: "com.diffplug.spotless:*"
      - dependency-name: "org.apache.felix:*"
      - dependency-name: "org.sonarsource.scanner.maven:*"
    ignore:
      # It is known that upgrade from current version requires additional changes:
      - dependency-name: "org.apache.maven.plugins:maven-plugin-plugin"
      # Because of
      # https://github.com/apache/maven-compiler-plugin/blob/maven-compiler-plugin-3.13.0/pom.xml#L71
      # https://github.com/codehaus-plexus/plexus-compiler/blob/plexus-compiler-2.15.0/plexus-compilers/plexus-compiler-javac/src/main/java/org/codehaus/plexus/compiler/javac/JavacCompiler.java#L149-L163
      # requires javac version to be at least 6:
      - dependency-name: "org.apache.maven.plugins:maven-compiler-plugin"
        versions: ">=3.13.0"
      # Requires tests to be executed with Java 6:
      - dependency-name: "org.apache.maven.plugins:maven-surefire-plugin"
        versions: ">=2.20.0"
  - package-ecosystem: "maven"
    directory: "/org.jacoco.core"
    schedule:
      interval: "daily"
    labels:
      - "dependencies"
      - "component: core"
    allow:
      - dependency-name: "org.ow2.asm:*"
  - package-ecosystem: "maven"
    directory: "/org.jacoco.core.test.validation.kotlin"
    schedule:
      interval: "weekly"
    labels:
      - "dependencies"
      - "component: test"
      - "language: Kotlin"
    allow:
      - dependency-name: "org.jetbrains.kotlin:*"
