/*******************************************************************************
 * Copyright (c) 2009, 2025 Mountainminds GmbH & Co. KG and Contributors
 * This program and the accompanying materials are made available under
 * the terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 * Contributors:
 *    Evgeny <PERSON>v - initial API and implementation
 *
 *******************************************************************************/
import java.io.*;
import org.codehaus.plexus.util.*;

String agentOptions = "excludes=**/FileUtil*:**/TestUtil*";

String buildLog = FileUtils.fileRead( new File( basedir, "build.log" ) );
if ( buildLog.indexOf( agentOptions ) < 0 ) {
    throw new RuntimeException("Include/Exclude was not configured correct" );
}

File databaseUtilReportFile = new File( basedir, "target/site/jacoco/org.project/DatabaseUtil.html" );
if ( !databaseUtilReportFile.isFile() )
{
    throw new FileNotFoundException( "DatabaseUtil should NOT be excluded: " + databaseUtilReportFile );
}

File testUtilReportFile = new File( basedir, "target/site/jacoco/org.project/TestUtil.html" );
if ( testUtilReportFile.isFile() )
{
    throw new RuntimeException( "TestUtil SHOULD be excluded: " + testUtilReportFile );
}
