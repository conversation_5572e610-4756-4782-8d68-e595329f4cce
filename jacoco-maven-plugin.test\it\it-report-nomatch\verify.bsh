/*******************************************************************************
 * Copyright (c) 2009, 2025 Mountainminds GmbH & Co. KG and Contributors
 * This program and the accompanying materials are made available under
 * the terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 * Contributors:
 *    Evgeny <PERSON>v - initial API and implementation
 *
 *******************************************************************************/
import java.io.*;
import org.codehaus.plexus.util.*;

String buildLog = FileUtils.fileRead( new File( basedir, "build.log" ) );
if ( buildLog.indexOf( "Classes in bundle " ) < 0 || buildLog.indexOf(" do not match with execution data." ) < 0 ) {
    throw new RuntimeException( "Warning 1 was not printed" );
}
if ( buildLog.indexOf( "For report generation the same class files must be used as at runtime." ) < 0 ) {
    throw new RuntimeException( "Warning 2 was not printed" );
}
if ( buildLog.indexOf( "Execution data for class Example does not match." ) < 0 ) {
    throw new RuntimeException( "Warning 3 was not printed" );
}
