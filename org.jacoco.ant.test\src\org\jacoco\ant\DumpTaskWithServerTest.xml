<?xml version="1.0" encoding="UTF-8"?>

<!--
   Copyright (c) 2009, 2025 Mountainminds GmbH & Co. KG and Contributors
   This program and the accompanying materials are made available under
   the terms of the Eclipse Public License 2.0 which is available at
   http://www.eclipse.org/legal/epl-2.0

   SPDX-License-Identifier: EPL-2.0

   Contributors:
      <PERSON> - initial API and implementation
-->

<project name="JaCoCo Dump Task Tests" xmlns:au="antlib:org.apache.ant.antunit" xmlns:jacoco="antlib:org.jacoco.ant">

	<target name="setUp">
		<tempfile property="temp.dir" prefix="jacocoTest" destdir="${java.io.tmpdir}" />
		<mkdir dir="${temp.dir}"/>
		<property name="exec.file" location="${temp.dir}/exec.file" />
		<property name="term.file" location="${temp.dir}/term.file" />

		<jacoco:coverage output="tcpserver" port="6300">
			<java classname="org.jacoco.ant.TestTarget" spawn="true" fork="true" maxmemory="16m">
				<classpath path="${org.jacoco.ant.dumpTaskWithServerTest.classes.dir}"/>
				<jvmarg value="-client"/>
				<arg value="${term.file}"/>
			</java>
		</jacoco:coverage>

		<waitfor maxwait="60" maxwaitunit="second" checkevery="10" checkeveryunit="millisecond" timeoutproperty="timeout">
			<socket server="localhost" port="6300"/>
		</waitfor>
		<fail message="Server startup timeout." if="timeout"/>
	</target>

	<target name="tearDown">
		<touch file="${term.file}"/>
		<waitfor maxwait="60" maxwaitunit="second" checkevery="10" checkeveryunit="millisecond" timeoutproperty="timeout">
			<not>
				<socket server="localhost" port="6300"/>
			</not>
		</waitfor>
		<fail message="Server shutdown timeout." if="timeout"/>
		<delete dir="${temp.dir}" quiet="false" failonerror="true"/>
	</target>


	<target name="testDump">
		<jacoco:dump dump="true" destfile="${exec.file}"/>

		<au:assertFileExists file="${exec.file}"/>
		<au:assertLogContains text="Dumping execution data to ${exec.file}"/>
	</target>

	<target name="testNoDumpOrReset">
		<jacoco:dump dump="false" reset="false"/>

		<au:assertLogDoesntContain text="Dumping execution data to"/>
	</target>

	<target name="testResetOnly">
		<jacoco:dump dump="false" reset="true"/>

		<au:assertLogDoesntContain text="Dumping execution data to"/>
	</target>

	<target name="testNoDumpWithFileSet">
		<jacoco:dump dump="false" destfile="${exec.file}"/>

		<au:assertLogDoesntContain text="Dumping execution data to"/>
		<au:assertFileDoesntExist file="${exec.file}"/>
	</target>

</project>
