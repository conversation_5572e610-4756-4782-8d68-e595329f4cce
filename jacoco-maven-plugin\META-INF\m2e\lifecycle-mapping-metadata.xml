<!--
   Copyright (c) 2009, 2025 Mountainminds GmbH & Co. KG and Contributors
   This program and the accompanying materials are made available under
   the terms of the Eclipse Public License 2.0 which is available at
   http://www.eclipse.org/legal/epl-2.0

   SPDX-License-Identifier: EPL-2.0

   Contributors:
      <PERSON><PERSON><PERSON> - initial API and implementation
-->
<lifecycleMappingMetadata>
  <pluginExecutions>
    <pluginExecution>
      <pluginExecutionFilter>
        <goals>
          <goal>prepare-agent</goal>
          <goal>prepare-agent-integration</goal>
          <goal>merge</goal>
          <goal>report</goal>
          <goal>report-integration</goal>
          <goal>report-aggregate</goal>
          <goal>check</goal>
          <goal>dump</goal>
          <goal>instrument</goal>
          <goal>restore-instrumented-classes</goal>
        </goals>
      </pluginExecutionFilter>
      <action>
        <ignore />
      </action>
    </pluginExecution>
  </pluginExecutions>
</lifecycleMappingMetadata>
