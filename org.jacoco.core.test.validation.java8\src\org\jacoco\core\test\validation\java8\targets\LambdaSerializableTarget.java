/*******************************************************************************
 * Copyright (c) 2009, 2025 Mountainminds GmbH & Co. KG and Contributors
 * This program and the accompanying materials are made available under
 * the terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 * Contributors:
 *    Evgeny <PERSON>v - initial API and implementation
 *
 *******************************************************************************/
package org.jacoco.core.test.validation.java8.targets;

import static org.jacoco.core.test.validation.targets.Stubs.execSerializable;
import static org.jacoco.core.test.validation.targets.Stubs.nop;

import java.io.Serializable;

/**
 * Test target with {@link Serializable} lambda.
 */
public class LambdaSerializableTarget {

	public static void main(String[] args) {

		execSerializable(() -> {
			nop(); // assertFullyCovered()
		});

	}

}
