<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <link rel="stylesheet" href="resources/doc.css" charset="UTF-8" type="text/css" />
  <link rel="stylesheet" href="../coverage/jacoco-resources/prettify.css" charset="UTF-8" type="text/css" />
  <link rel="shortcut icon" href="resources/report.gif" type="image/gif" />
  <script type="text/javascript" src="../coverage/jacoco-resources/prettify.js"></script>
  <title>JaCoC<PERSON> - <PERSON><PERSON>lug-in</title>
</head>
<body onload="prettyPrint()">

<div class="breadcrumb">
  <a href="../index.html" class="el_report">JaCoCo</a> &gt;
  <a href="index.html" class="el_group">Documentation</a> &gt;
  <span class="el_source">Maven Plug-in</span>
</div>
<div id="content">

<h1>Maven Plug-in</h1>

<p>
  The JaCoCo <a href="http://maven.apache.org/">Maven</a> plug-in provides the
  JaCoCo runtime agent to your tests and allows basic report creation.
</p>

<p class="hint">
  If you want to have line number information included in the coverage reports
  or you want source code highlighting the class files of the test target must
  be compiled with debug information.
</p>

<p class="hint">
  When using the <tt>maven-surefire-plugin</tt> or <tt>maven-failsafe-plugin</tt>
  you <b>must not</b> use a
  <a href="http://maven.apache.org/surefire/maven-surefire-plugin/test-mojo.html#forkCount">
  <tt>forkCount</tt></a> of <tt>0</tt> or set the
  <a href="http://maven.apache.org/surefire/maven-surefire-plugin/test-mojo.html#forkMode">
  <tt>forkMode</tt></a> to <tt>never</tt> as this would prevent the execution of
  the tests with the <tt>javaagent</tt> set and no coverage would be recorded.
</p>

<h2>Example</h2>

<ul>
  <li>
    The JaCoCo distribution contains a simple example how code coverage
    can be added to a Maven project.</li>
  <li>
    This <a href="examples/build/pom.xml">POM file</a>
    for a JAR project runs JUnit tests under code coverage and creates a coverage
    report (<tt>target/site/jacoco/index.html</tt>).
  </li>
  <li>
    This <a href="examples/build/pom-it.xml">POM file</a>
    for a JAR project runs unit tests and integration tests under code coverage
    and creates two coverage reports, one for unit tests (
    <tt>target/site/jacoco/index.html</tt>) and one for integration tests (
    <tt>target/site/jacoco-it/index.html</tt>).
  </li>
  <li>
  The complete example is located in the <code>./doc/examples/build</code>
  folder of the distribution.
  </li>
</ul>


<h2>Prerequisites</h2>

<p>
  The JaCoCo Maven plug-in requires
</p>

<ul>
  <li>Maven 3.0 or higher and</li>
  <li>Java 1.8 or higher for the Maven runtime, Java 1.5 or higher for the test executor.</li>
</ul>


<h2>Usage</h2>

<p>
  The Maven plug-in can be included in your build with the following
  declaration. Please check
  <a href="http://search.maven.org/#search|ga|1|g%3Aorg.jacoco%20a%3Ajacoco-maven-plugin">here</a>
  for the latest release version in the repository.
</p>

<pre class="source lang-xml linenums">
&lt;plugin&gt;
  &lt;groupId&gt;org.jacoco&lt;/groupId&gt;
  &lt;artifactId&gt;jacoco-maven-plugin&lt;/artifactId&gt;
  &lt;version&gt;${project.version}&lt;/version&gt;
&lt;/plugin&gt;
</pre>

<p>
  To receive a full list of goals and available parameters you can use
  <code>maven-help-plugin</code>:
</p>
<pre>
mvn help:describe -Dplugin=org.jacoco:jacoco-maven-plugin -Ddetail
</pre>

<p>
  Usage of plugin together with <tt>maven-site-plugin</tt> without
  <a href="https://maven.apache.org/plugins/maven-site-plugin/examples/configuring-reports.html#Selecting_Reports_from_a_Plugin:_Configuring_Report_Sets">explicit selection of reports</a>
  might lead to generation of redundant aggregate reports.
  Specify <tt>reportSets</tt> explicitly to avoid this:
</p>
<pre class="source lang-xml linenums">
&lt;project&gt;
  &lt;reporting&gt;
    &lt;plugins&gt;
      &lt;plugin&gt;
        &lt;groupId&gt;org.jacoco&lt;/groupId&gt;
        &lt;artifactId&gt;jacoco-maven-plugin&lt;/artifactId&gt;
        &lt;reportSets&gt;
          &lt;reportSet&gt;
            &lt;reports&gt;
              &lt;!-- select non-aggregate reports --&gt;
              &lt;report&gt;report&lt;/report&gt;
            &lt;/reports&gt;
          &lt;/reportSet&gt;
        &lt;/reportSets&gt;
      &lt;/plugin&gt;
    &lt;/plugins&gt;
  &lt;/reporting&gt;
&lt;/project&gt;
</pre>

<h2>Goals</h2>

<p>
  The JaCoCo Maven plug-in defines the following goals:
</p>

<ul>
  <li><a href="help-mojo.html">help</a></li>
  <li><a href="prepare-agent-mojo.html">prepare-agent</a></li>
  <li><a href="prepare-agent-integration-mojo.html">prepare-agent-integration</a></li>
  <li><a href="merge-mojo.html">merge</a></li>
  <li><a href="report-mojo.html">report</a></li>
  <li><a href="report-integration-mojo.html">report-integration</a></li>
  <li><a href="report-aggregate-mojo.html">report-aggregate</a></li>
  <li><a href="check-mojo.html">check</a></li>
  <li><a href="dump-mojo.html">dump</a></li>
  <li><a href="instrument-mojo.html">instrument</a></li>
  <li><a href="restore-instrumented-classes-mojo.html">restore-instrumented-classes</a></li>
</ul>

</div>
<div class="footer">
  <span class="right"><a href="${jacoco.home.url}">JaCoCo</a> ${qualified.bundle.version}</span>
  <a href="license.html">Copyright</a> &copy; ${copyright.years} Mountainminds GmbH &amp; Co. KG and Contributors
</div>

</body>
</html>
