<?xml version="1.0" encoding="UTF-8"?>
<!--
   Copyright (c) 2009, 2025 Mountainminds GmbH & Co. KG and Contributors
   This program and the accompanying materials are made available under
   the terms of the Eclipse Public License 2.0 which is available at
   http://www.eclipse.org/legal/epl-2.0

   SPDX-License-Identifier: EPL-2.0

   Contributors:
      <PERSON><PERSON><PERSON> - initial API and implementation
-->
<assembly>
  <id>distribution</id>
  <formats>
    <format>zip</format>
  </formats>
  <includeBaseDirectory>false</includeBaseDirectory>
  <fileSets>
    <fileSet>
      <directory>${basedir}/build</directory>
      <outputDirectory>/examples/build</outputDirectory>
      <fileMode>0644</fileMode>
      <directoryMode>0755</directoryMode>
      <filtered>true</filtered>
    </fileSet>
    <fileSet>
      <directory>${basedir}/src/org/jacoco/examples</directory>
      <outputDirectory>/examples/java</outputDirectory>
      <fileMode>0644</fileMode>
      <directoryMode>0755</directoryMode>
    </fileSet>
  </fileSets>
</assembly>
