<?xml version="1.0" encoding="UTF-8"?>

<!--
   Copyright (c) 2009, 2025 Mountainminds GmbH & Co. KG and Contributors
   This program and the accompanying materials are made available under
   the terms of the Eclipse Public License 2.0 which is available at
   http://www.eclipse.org/legal/epl-2.0

   SPDX-License-Identifier: EPL-2.0

   Contributors:
      <PERSON> - initial API and implementation
-->

<project name="JaCoCo Coverage Task Tests" xmlns:au="antlib:org.apache.ant.antunit" xmlns:jacoco="antlib:org.jacoco.ant">

	<target name="setUp">
		<tempfile property="temp.dir" prefix="jacocoTest" destdir="${java.io.tmpdir}" />
		<mkdir dir="${temp.dir}"/>
		<property name="exec.file" location="${temp.dir}/jacoco.exec" />
	</target>

	<target name="tearDown">
		<delete dir="${temp.dir}" quiet="false" failonerror="true"/>
	</target>

	<target name="testNoSubTasks">
		<au:expectfailure expectedMessage="A child task must be supplied for the coverage task">
			<jacoco:coverage/>
		</au:expectfailure>
	</target>

	<target name="testMultipleSubTasks">
		<au:expectfailure expectedMessage="Only one child task can be supplied to the coverge task">
			<jacoco:coverage>
				<java classname="org.jacoco.ant.TestTarget" fork="true" failonerror="true">
					<classpath path="${org.jacoco.ant.coverageTaskTest.classes.dir}"/>
				</java>
				<java classname="org.jacoco.ant.TestTarget" fork="true" failonerror="true">
					<classpath path="${org.jacoco.ant.coverageTaskTest.classes.dir}"/>
				</java>
			</jacoco:coverage>
		</au:expectfailure>

		<au:assertLogDoesntContain text="Target executed"/>
	</target>

	<target name="testInvalidSubTask">
		<au:expectfailure expectedMessage="jar is not a valid child of the coverage task">
			<jacoco:coverage>
				<jar destfile="test.jar"/>
			</jacoco:coverage>
		</au:expectfailure>
	</target>

	<target name="testCoverageOfForkedJava">
		<jacoco:coverage destfile="${exec.file}">
			<java classname="org.jacoco.ant.TestTarget" fork="true" failonerror="true">
				<classpath path="${org.jacoco.ant.coverageTaskTest.classes.dir}"/>
			</java>
		</jacoco:coverage>

		<au:assertLogContains text="Enhancing java with coverage"/>
		<au:assertFileExists file="${exec.file}"/>
		<au:assertLogContains text="Target executed"/>
	</target>

	<target name="testCoverageOfForkedJavaWithVariable">
		<property name="trueProperty" value="true"/>
		<jacoco:coverage destfile="${exec.file}">
			<java classname="org.jacoco.ant.TestTarget" fork="${trueProperty}" failonerror="true">
				<classpath path="${org.jacoco.ant.coverageTaskTest.classes.dir}"/>
			</java>
		</jacoco:coverage>

		<au:assertLogContains text="Enhancing java with coverage"/>
		<au:assertFileExists file="${exec.file}"/>
		<au:assertLogContains text="Target executed"/>
	</target>

	<target name="testCoverageOfForkedJavaDisabled">
		<jacoco:coverage enabled="false" destfile="${exec.file}">
			<java classname="org.jacoco.ant.TestTarget" fork="true" failonerror="true">
				<classpath path="${org.jacoco.ant.coverageTaskTest.classes.dir}"/>
			</java>
		</jacoco:coverage>

		<au:assertLogDoesntContain text="Enhancing java with coverage"/>
		<au:assertFileDoesntExist file="${exec.file}"/>
		<au:assertLogContains text="Target executed"/>
	</target>

	<target name="testCoverageOfNonForkedJava">
		<au:expectfailure expectedMessage="Coverage can only be applied on a forked VM">
			<jacoco:coverage>
				<java classname="org.jacoco.ant.TestTarget" fork="false" failonerror="true">
					<classpath path="${org.jacoco.ant.coverageTaskTest.classes.dir}"/>
				</java>
			</jacoco:coverage>
		</au:expectfailure>

		<au:assertLogDoesntContain text="Target executed"/>
	</target>

	<target name="testCoverageOfForkedJUnit">
		<jacoco:coverage destfile="${exec.file}">
			<junit fork="true" haltonfailure="true" showoutput="true">
				<classpath path="${java.class.path}"/>
				<test name="org.jacoco.ant.TestTarget" />
			</junit>
		</jacoco:coverage>

		<au:assertLogContains text="Enhancing junit with coverage"/>
		<au:assertFileExists file="${exec.file}"/>
		<au:assertLogContains text="Target executed"/>
	</target>

	<target name="testCoverageOfForkedJUnitDisabled">
		<jacoco:coverage enabled="false" destfile="${exec.file}">
			<junit fork="true" haltonfailure="true" showoutput="true">
				<classpath path="${java.class.path}"/>
				<test name="org.jacoco.ant.TestTarget" />
			</junit>
		</jacoco:coverage>

		<au:assertLogDoesntContain text="Enhancing junit with coverage"/>
		<au:assertFileDoesntExist file="${exec.file}"/>
		<au:assertLogContains text="Target executed"/>
	</target>

	<target name="testCoverageOfNonForkedJUnit">
		<au:expectfailure expectedMessage="Coverage can only be applied on a forked VM">
			<jacoco:coverage>
				<junit fork="false" haltonfailure="true" showoutput="true">
					<classpath path="${java.class.path}"/>
					<test name="org.jacoco.ant.TestTarget" />
				</junit>
			</jacoco:coverage>
		</au:expectfailure>

		<au:assertLogDoesntContain text="Target executed"/>
	</target>

	<target name="testInclBootstrapClasses">
		<jacoco:coverage destfile="${exec.file}" inclbootstrapclasses="true" includes="java/sql/*">
			<java classname="org.jacoco.ant.TestTarget" fork="true" failonerror="true">
				<classpath path="${org.jacoco.ant.coverageTaskTest.classes.dir}"/>
			</java>
		</jacoco:coverage>

		<au:assertLogContains text="Enhancing java with coverage"/>
		<au:assertFileExists file="${exec.file}"/>
		<au:assertLogContains text="Target executed"/>

		<java classname="org.jacoco.ant.DumpExecClassNames" fork="true" classpath="${java.class.path}" failonerror="true">
			<arg value="${exec.file}" />
		</java>
		<au:assertLogContains text="java/sql/Timestamp"/>
	</target>

	<target name="testIllegalReflectiveAccess">
		<jacoco:coverage destfile="${exec.file}">
			<java classname="org.jacoco.ant.IllegalReflectiveAccessTarget" fork="true" failonerror="true">
				<classpath path="${org.jacoco.ant.coverageTaskTest.classes.dir}"/>
			</java>
		</jacoco:coverage>
	</target>

</project>
