/*******************************************************************************
 * Copyright (c) 2009, 2025 Mountainminds GmbH & Co. KG and Contributors
 * This program and the accompanying materials are made available under
 * the terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 * Contributors:
 *    <PERSON>, <PERSON> - initial API and implementation
 *
 *******************************************************************************/
import org.codehaus.plexus.util.*;
import java.util.regex.*;

String buildLog = FileUtils.fileRead( new File( basedir, "build.log" ) );

// Test customization of dataFileIncludes and dataFileExcludes

if ( !Pattern.compile( "Loading execution data file \\S*child1.target.child1.coverage").matcher( buildLog ).find() ) {
    throw new RuntimeException( "Execution data from child1 was not loaded." );
}

if ( Pattern.compile( "Loading execution data file \\S*child2").matcher( buildLog ).find() ) {
    throw new RuntimeException( "Execution data from child2 was loaded." );
}

// Test customization of outputDirectory

File reportChild1 = new File( basedir, "report/target/jacoco-aggregate-customization/child1/index.html" );
if ( !reportChild1.isFile() ) {
    throw new RuntimeException( "Report for child1 was not created." );
}

File reportChild2 = new File( basedir, "report/target/jacoco-aggregate-customization/child2/index.html" );
if ( !reportChild2.isFile() ) {
    throw new RuntimeException( "Report for child2 was not created." );
}

// Test customization of includeCurrentProject

File reportReport = new File( basedir, "report/target/jacoco-aggregate-customization/report/index.html" );
if ( !reportReport.isFile() ) {
    throw new RuntimeException( "Report for report was not created." );
}
