<?xml version="1.0" encoding="UTF-8"?>
<!--
   Copyright (c) 2009, 2025 Mountainminds GmbH & Co. KG and Contributors
   This program and the accompanying materials are made available under
   the terms of the Eclipse Public License 2.0 which is available at
   http://www.eclipse.org/legal/epl-2.0

   SPDX-License-Identifier: EPL-2.0

   Contributors:
      <PERSON><PERSON><PERSON> - initial API and implementation
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.jacoco</groupId>
    <artifactId>org.jacoco.tests</artifactId>
    <version>0.8.14-SNAPSHOT</version>
    <relativePath>../org.jacoco.tests</relativePath>
  </parent>

  <artifactId>org.jacoco.cli.test</artifactId>

  <name>JaCoCo :: Test :: Command Line Interface</name>

  <properties>
    <jacoco.includes>org.jacoco.cli.*</jacoco.includes>
  </properties>

  <dependencies>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.cli</artifactId>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
    </dependency>
  </dependencies>

</project>
