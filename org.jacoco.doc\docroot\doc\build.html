<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <link rel="stylesheet" href="resources/doc.css" charset="UTF-8" type="text/css" />
  <link rel="stylesheet" href="../coverage/jacoco-resources/prettify.css" charset="UTF-8" type="text/css" />
  <link rel="shortcut icon" href="resources/report.gif" type="image/gif" />
  <script type="text/javascript" src="../coverage/jacoco-resources/prettify.js"></script>
  <title>JaCoCo - Build</title>
</head>
<body onload="prettyPrint()">

<div class="breadcrumb">
  <a href="../index.html" class="el_report">JaCoCo</a> &gt;
  <a href="index.html" class="el_group">Documentation</a> &gt;
  <span class="el_source">Build</span>
</div>
<div id="content">

<h1>Build</h1>

<p>
  The JaCoCo build is based on <a href="http://maven.apache.org/">Maven</a> and
  can be locally executed on every machine with a proper
  <a href="environment.html">environment setup</a>. In particular you need JDK 17
  installation. Developers are encouraged to run the build before every commit
  to ensure consistency of the source tree.
</p>


<h2>Running the Build</h2>

<p>
  The build can be started by executing the
  <a href="https://maven.apache.org/wrapper/">Maven Wrapper</a> using following
  command with <code>./org.jacoco.build/</code> as the working directory:
</p>

<pre>
  ../mvnw clean verify
</pre>

<p>
  Total build time is typically around 3 minutes, however first build might take
  more time, because Maven should download plugins and dependencies. The
  download ZIP will be created at the following location:
</p>

<pre>
  ../jacoco/target/jacoco-<i>x.y.z.qualifier</i>.zip
</pre>


<h2>Running Quick Build without Tests</h2>

<p>
  It is not recommended, however possible to the run build without any tests:
</p>

<pre>
  ../mvnw clean verify -DskipTests
</pre>


<h2>Compilation and testing with different JDKs</h2>

<p>
  By default for compilation and execution of tests will be used JDK that runs
  Maven. Alternatively you can use JDK from
  <a href="http://maven.apache.org/guides/mini/guide-using-toolchains.html">Maven
  Toolchains</a> by setting property <code>jdk.version</code>.
</p>

<p>
  Following table shows in which classfile version modules will be compiled to
  depending on version of JDK (empty cells = no compilation possible):
</p>

<table class="coverage">
  <thead>
  <tr>
    <td>Modules</td>
    <td>JDK 5</td>
    <td>JDK 6</td>
    <td>JDK 7</td>
    <td>JDK 8</td>
    <td>JDK 9</td>
    <td>JDK 10</td>
    <td>JDK 11</td>
    <td>JDK 12</td>
    <td>JDK 13</td>
    <td>JDK 14</td>
    <td>JDK 15</td>
    <td>JDK 16</td>
    <td>JDK 17</td>
    <td>JDK 18</td>
    <td>JDK 19</td>
    <td>JDK 20</td>
    <td>JDK 21</td>
    <td>JDK 22</td>
    <td>JDK 23</td>
    <td>JDK 24</td>
    <td>JDK 25</td>
    <td>JDK 26</td>
  </tr>
  </thead>
  <tbody>
  <tr>
    <td>org.jacoco.core.test.validation.java21</td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td>21</td>
    <td>21</td>
    <td>21</td>
    <td>21</td>
    <td>21</td>
    <td>21</td>
  </tr>
  <tr>
    <td>org.jacoco.core.test.validation.java16</td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td>16</td>
    <td>16</td>
    <td>16</td>
    <td>16</td>
    <td>16</td>
    <td>16</td>
    <td>16</td>
    <td>16</td>
    <td>16</td>
    <td>16</td>
    <td>16</td>
  </tr>
  <tr>
    <td>org.jacoco.core.test.validation.java14</td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td>14</td>
    <td>14</td>
    <td>14</td>
    <td>14</td>
    <td>14</td>
    <td>14</td>
    <td>14</td>
    <td>14</td>
    <td>14</td>
    <td>14</td>
    <td>14</td>
    <td>14</td>
    <td>14</td>
  </tr>
  <tr>
    <td>org.jacoco.core.test.validation.java8</td>
    <td></td>
    <td></td>
    <td></td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
  </tr>
  <tr>
    <td>org.jacoco.core.test.validation.java7</td>
    <td></td>
    <td></td>
    <td>7</td>
    <td>7</td>
    <td>7</td>
    <td>7</td>
    <td>7</td>
    <td>7</td>
    <td>7</td>
    <td>7</td>
    <td>7</td>
    <td>7</td>
    <td>7</td>
    <td>7</td>
    <td>7</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
  </tr>
  <tr>
    <td>org.jacoco.core.test.validation.groovy</td>
    <td></td>
    <td></td>
    <td></td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
  </tr>
  <tr>
    <td>org.jacoco.core.test.validation.kotlin</td>
    <td></td>
    <td></td>
    <td></td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
  </tr>
  <tr>
    <td>org.jacoco.core.test.validation.scala</td>
    <td></td>
    <td></td>
    <td></td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
  </tr>
  <tr>
    <td>all other modules</td>
    <td>5</td>
    <td>5</td>
    <td>5</td>
    <td>5</td>
    <td>6</td>
    <td>6</td>
    <td>6</td>
    <td>7</td>
    <td>7</td>
    <td>7</td>
    <td>7</td>
    <td>7</td>
    <td>7</td>
    <td>7</td>
    <td>7</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
    <td>8</td>
  </tr>
  </tbody>
</table>

<p>
  Alternatively you can specify bytecode version for all modules using property
  <code>bytecode.version</code>.
</p>

<p>
  For example to use JDK 10 from Maven Toolchains and compile all modules
  into bytecode version 53 (Java 9):
</p>

<pre>
  ../mvnw clean verify -Djdk.version=10 -Dbytecode.version=9
</pre>

<p>
  Also Eclipse Compiler for Java can be used for compilation:
</p>

<pre>
  ../mvnw clean verify -Decj
</pre>

<p>
  Combining these options JaCoCo is regularly tested with the following setups:
</p>

<ul>
  <li><code>../mvnw clean verify -Djdk.version=5 -Dbytecode.version=5</code></li>
  <li><code>../mvnw clean verify -Djdk.version=6 -Dbytecode.version=6</code></li>
  <li><code>../mvnw clean verify -Djdk.version=7 -Dbytecode.version=7</code></li>
  <li><code>../mvnw clean verify -Djdk.version=8 -Dbytecode.version=8</code></li>
  <li><code>../mvnw clean verify -Djdk.version=9 -Dbytecode.version=9</code></li>
  <li><code>../mvnw clean verify -Djdk.version=10 -Dbytecode.version=10</code></li>
  <li><code>../mvnw clean verify -Djdk.version=11 -Dbytecode.version=11</code></li>
  <li><code>../mvnw clean verify -Djdk.version=11 -Dbytecode.version=11 -Decj</code></li>
  <li><code>../mvnw clean verify -Djdk.version=12 -Dbytecode.version=12</code></li>
  <li><code>../mvnw clean verify -Djdk.version=13 -Dbytecode.version=13</code></li>
  <li><code>../mvnw clean verify -Djdk.version=14 -Dbytecode.version=14</code></li>
  <li><code>../mvnw clean verify -Djdk.version=15 -Dbytecode.version=15</code></li>
  <li><code>../mvnw clean verify -Djdk.version=16 -Dbytecode.version=16</code></li>
  <li><code>../mvnw clean verify -Djdk.version=17 -Dbytecode.version=17</code></li>
  <li><code>../mvnw clean verify -Djdk.version=17 -Dbytecode.version=17 -Decj</code></li>
  <li><code>../mvnw clean verify -Djdk.version=18 -Dbytecode.version=18</code></li>
  <li><code>../mvnw clean verify -Djdk.version=19 -Dbytecode.version=19</code></li>
  <li><code>../mvnw clean verify -Djdk.version=20 -Dbytecode.version=20</code></li>
  <li><code>../mvnw clean verify -Djdk.version=21 -Dbytecode.version=21</code></li>
  <li><code>../mvnw clean verify -Djdk.version=21 -Dbytecode.version=21 -Decj</code></li>
  <li><code>../mvnw clean verify -Djdk.version=22 -Dbytecode.version=22</code></li>
  <li><code>../mvnw clean verify -Djdk.version=23 -Dbytecode.version=23</code></li>
  <li><code>../mvnw clean verify -Djdk.version=24 -Dbytecode.version=24</code></li>
  <li><code>../mvnw clean verify -Djdk.version=25 -Dbytecode.version=25</code></li>
  <li><code>../mvnw clean verify -Djdk.version=26 -Dbytecode.version=26</code></li>
</ul>


</div>
<div class="footer">
  <span class="right"><a href="${jacoco.home.url}">JaCoCo</a> ${qualified.bundle.version}</span>
  <a href="license.html">Copyright</a> &copy; ${copyright.years} Mountainminds GmbH &amp; Co. KG and Contributors
</div>

</body>
</html>
