<?xml version="1.0" encoding="UTF-8"?>

<!--
   Copyright (c) 2009, 2025 Mountainminds GmbH & Co. KG and Contributors
   This program and the accompanying materials are made available under
   the terms of the Eclipse Public License 2.0 which is available at
   http://www.eclipse.org/legal/epl-2.0

   SPDX-License-Identifier: EPL-2.0

   Contributors:
      <PERSON> - initial API and implementation
-->

<project name="JaCoCo Agent Task Tests" xmlns:au="antlib:org.apache.ant.antunit" xmlns:jacoco="antlib:org.jacoco.ant">

	<target name="testCoverageAgent">
		<jacoco:agent property="jacocoagent" append="false" destfile="test.exec"
			exclClassLoader="EvilClassLoader" includes="org.example.*" excludes="*Test"
			inclbootstrapclasses="true" inclnolocationclasses="true"
		    sessionid="testid" dumponexit="false"
			output="file" address="remotehost" port="1234" jmx="true"
			classdumpdir="target/dump"/>
		<au:assertPropertySet name="jacocoagent"/>
		<au:assertPropertyContains name="jacocoagent" value="-javaagent:"/>
		<au:assertPropertyContains name="jacocoagent" value="append=false"/>
		<property name="exec.file" location="test.exec"/>
		<au:assertPropertyContains name="jacocoagent" value="destfile=${exec.file}"/>
		<au:assertPropertyContains name="jacocoagent" value="exclclassloader=EvilClassLoader"/>
		<au:assertPropertyContains name="jacocoagent" value="includes=org.example.*"/>
		<au:assertPropertyContains name="jacocoagent" value="excludes=*Test"/>
		<au:assertPropertyContains name="jacocoagent" value="inclbootstrapclasses=true"/>
		<au:assertPropertyContains name="jacocoagent" value="inclnolocationclasses=true"/>
		<au:assertPropertyContains name="jacocoagent" value="sessionid=testid"/>
		<au:assertPropertyContains name="jacocoagent" value="dumponexit=false"/>
		<au:assertPropertyContains name="jacocoagent" value="output=file"/>
		<au:assertPropertyContains name="jacocoagent" value="address=remotehost"/>
		<au:assertPropertyContains name="jacocoagent" value="port=1234"/>
		<au:assertPropertyContains name="jacocoagent" value="jmx=true"/>
		<property name="dump.dir" location="target/dump"/>
		<au:assertPropertyContains name="jacocoagent" value="classdumpdir=${dump.dir}"/>
	</target>

	<target name="testCoverageAgentDisabled">
		<jacoco:agent enabled="false" property="jacocoagent" append="false" destfile="${basedir}/test.exec" exclClassLoader="sun.reflect.DelegatingClassLoader"/>
		<au:assertPropertyEquals name="jacocoagent" value=""/>
	</target>

	<target name="testCoverageAgentWithNoProperty">
		<au:expectfailure expectedMessage="Property is mandatory">
			<jacoco:agent/>
		</au:expectfailure>
	</target>

	<target name="testCoverageAgentWithEmptyProperty">
		<au:expectfailure expectedMessage="Property is mandatory">
			<jacoco:agent property=""/>
		</au:expectfailure>
	</target>

	<target name="testCoverageAgentReuseAgentJar">
		<jacoco:agent property="agent1"/>
		<jacoco:agent property="agent2"/>
		<au:assertEquals expected="${agent1}" actual="${agent2}"/>
	</target>

</project>
