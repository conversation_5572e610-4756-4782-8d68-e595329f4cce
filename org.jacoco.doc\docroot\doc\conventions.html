<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <link rel="stylesheet" href="resources/doc.css" charset="UTF-8" type="text/css" />
  <link rel="shortcut icon" href="resources/report.gif" type="image/gif" />
  <title>JaCoCo - Development Conventions</title>
</head>
<body>

<div class="breadcrumb">
  <a href="../index.html" class="el_report">JaCoCo</a> &gt;
  <a href="index.html" class="el_group">Documentation</a> &gt;
  <span class="el_source">Development Conventions</span>
</div>
<div id="content">

<h1>Development Conventions</h1>

<p>
  Efficient team work that produces high-quality software requires a common set
  of rules.
</p>

<h2>Consistent Source Tree</h2>

<p>
  Follow the existing coding style and formatting rules. The projects contain
  auto-formatting rules for the Eclipse IDE.
</p>

<p>
  Update your local branches and run the build locally before every push. Push
  only if the build succeeds and the configured compiler settings do not show
  any warnings in the Eclipse IDE.
</p>

<p>
  Documentation is part of the product. Whenever you implement a new feature or
  change existing behavior make sure to update all corresponding JavaDoc as well
  as other documentation with the same change set. The product should always be
  in a consistent state. For every change the following items should be checked:
</p>

<ul>
  <li>Corresponding JavaDoc, every public type and member requires JavaDoc.</li>
  <li>Documentation referring to the modified concepts, interfaces or
    implementation.</li>
  <li>New features, bug fixes and modified behavior should be enlisted in the
    <code>org.jacoco.doc/docroot/doc/changes.html</code> file together with
    the corresponding issue tracker id.</li>
</ul>

<h2>Design for Integration</h2>

<p>
  The primary focus of the JaCoCo project is to provide a code coverage library.
  Integrators may want to embed JaCoCo in different tools and environments with
  very different usage scenarios. Therefore following aspects should be considered:
</p>

<p>
  <b>Documentation:</b> All APIs should be properly documented on different
  levels of granularity:
</p>

<ul>
  <li>General usage</li>
  <li>Bundle summary</li>
  <li>Package summary</li>
  <li>Type description</li>
  <li>Member description</li>
</ul>

<p>
  <b>Proper Units:</b> All APIs and internal implementation classes should form proper
  units with well defined responsibilities and dependencies. Each class and method
  should focus on a single concept. It should be possible to use different aspects
  separately.
</p>

<p>
  <b>Abstraction:</b> All APIs must  use the most general abstractions possible. For instance
  reading binary data should rely on the <code>java.io.InputStream</code> interface, not
  on a <code>java.io.File</code> object.
</p>

<p>
  <b>System Dependencies:</b> Avoid any dependencies to the local file system, network
  resources, threads, processes etc.
</p>

<h2>Test Driven Development</h2>

<p>
  All code added to JaCoCo should have corresponding JUnit test cases. Ideally tests
  are developed before or along with the actual implementation:
</p>

<ul>
  <li>Every new feature should be verified by test cases.</li>
  <li>Modified behavior should also be reflected by test cases.</li>
  <li>Ideally for every reported bug a reproducer is added to the unit tests.</li>
</ul>

<h2>Keep an Eye on License Issues</h2>

<p>
  All code included with JaCoCo must conform to the EPL license.
</p>

<ul>
  <li>
    Every committer and contributor must agree that all code will be published
    under EPL. He or she must be the original author and must have the
    permission to contribute code to JaCoCo, for example if such a permission
    is required by the employer.
  </li>
  <li>
    Every third party content must be enlisted in the corresponding
    <code>about.html</code> file along with its license.
  </li>
  <li>
    Every third party content included with the JaCoCo distribution must be
    enlisted in the <code>org.jacoco.doc/docroot/doc/license.html</code> file
    and the corresponding <code>about.html</code> file along with its license.
  </li>
  <li>
    Every source file (Java, Build Script, DTD) must have a
    <a href="http://www.eclipse.org/legal/copyrightandlicensenotice.php">EPL
    license notice</a>. The initial contributor should be listed. In case of
    significant changes or additions additional contributors should also
    be listed.
  </li>
</ul>

<h2>Contribution process</h2>

<p>
  All changes on the JaCoCo code base are handled via
  <a href="https://github.com/jacoco/jacoco/pulls">GitHub pull requests</a> and
  always reviewed by a second developer. This applies for external contributors
  as well as for project members. Beside functional correctness every pull
  request needs to fulfill the conventions above.
</p>

<p>
  For external contributors the following recommendations will help the project
  to incorporate their precious work:
</p>

<ul>
  <li>
    <b>Get in touch</b>: Before you start a bigger contribution
    please get in touch through our <a href="support.html">mailing list</a> to
    make sure the JaCoCo project considers this in scope and the approach fits
    in the overall architecture.
  </li>
  <li>
    <b>Clear scope</b>: We track and review every semantical change through a
    separate pull request. Pull requests handling various topics ("I fixed this
    and that") are typically difficult in handling and are therefore declined.
  </li>
  <li>
    <b>No technical debt</b>: We are committed to maintain JaCoCo in the long
    run with on a high quality level. Therefore we will not accept contributions
    as long as they add technical debt to the project (e.g. lack of tests or
    design issues).
  </li>
</ul>

</div>
<div class="footer">
  <span class="right"><a href="${jacoco.home.url}">JaCoCo</a> ${qualified.bundle.version}</span>
  <a href="license.html">Copyright</a> &copy; ${copyright.years} Mountainminds GmbH &amp; Co. KG and Contributors
</div>

</body>
</html>
