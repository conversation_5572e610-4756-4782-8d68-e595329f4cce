<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <link rel="stylesheet" href="doc/resources/doc.css" charset="UTF-8" type="text/css" />
  <link rel="shortcut icon" href="doc/resources/report.gif" type="image/gif" />
  <title>JaCoCo - Java Code Coverage Library</title>
</head>
<body>

<div class="breadcrumb">
  <span class="el_report">JaCoCo</span>
</div>
<div id="content">

<h1>JaCoCo - Java Code Coverage Library</h1>

<p>
  JaCoCo is a free Java code coverage library distributed under the
  <a href="doc/license.html">Eclipse Public License</a>. Check
  <a href="${jacoco.home.url}">${jacoco.home.url}</a> for updates and feedback.
</p>

<p>
  This is the distribution of version ${qualified.bundle.version} created on
  ${build.date} based on commit
  <a href="https://github.com/jacoco/jacoco/tree/${build.commitId}">${build.commitId}</a>.
</p>

<h2>Contents</h2>

<ul>
  <li><a href="doc/index.html">Documentation</a></li>
  <li><a href="test/index.html">JUnit Test Results</a></li>
  <li><a href="coverage/index.html">Code Coverage Report</a>
      (<a href="coverage/jacoco.csv">CSV</a>,
       <a href="coverage/jacoco.xml">XML</a>)</li>
  <li><a href="doc/changes.html">Change History</a></li>
  <li><a href="doc/license.html">License</a></li>
</ul>

<p>
  The JaCoCo distribution contains the following libraries in the
  <code>./lib</code> folder:
</p>

<table class="coverage">
  <thead>
    <tr>
      <td>File</td>
      <td>OSGi Bundle</td>
      <td>Description</td>
      <td>Dependencies</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><span class="el_jar">jacocoagent.jar</span></td>
      <td>no</td>
      <td>Java agent for execution data recording</td>
      <td>- <i>(all dependencies included)</i></td>
    </tr>
    <tr>
      <td><span class="el_jar">jacocoant.jar</span></td>
      <td>no</td>
      <td>JaCoCo Ant tasks</td>
      <td>Ant <i>(all other dependencies included)</i></td>
    </tr>
    <tr>
      <td><span class="el_jar">jacococli.jar</span></td>
      <td>no</td>
      <td>JaCoCo Command Line Interface</td>
      <td>- <i>(all dependencies included)</i></td>
    </tr>
    <tr>
      <td><span class="el_jar">org.jacoco.agent_${qualified.bundle.version}.jar</span></td>
      <td>yes</td>
      <td>JaCoCo agent</td>
      <td>-</td>
    </tr>
    <tr>
      <td><span class="el_jar">org.jacoco.core_${qualified.bundle.version}.jar</span></td>
      <td>yes</td>
      <td>JaCoCo core</td>
      <td>ASM</td>
    </tr>
    <tr>
      <td><span class="el_jar">org.jacoco.report_${qualified.bundle.version}.jar</span></td>
      <td>yes</td>
      <td>JaCoCo reporting</td>
      <td>org.jacoco.core, ASM</td>
    </tr>
    <tr>
      <td><span class="el_jar">org.jacoco.ant_${qualified.bundle.version}.jar</span></td>
      <td>yes</td>
      <td>JaCoCo Ant tasks</td>
      <td>org.jacoco.core, org.jacoco.agent, org.jacoco.report, ASM, Ant</td>
    </tr>
  </tbody>
</table>

<p>
  All libraries as well as the JaCoCo <a href="doc/maven.html">Maven plug-in</a>
  are also available from the Maven <a href="doc/repo.html">repository</a>.
</p>

</div>
<div class="footer">
  <span class="right"><a href="${jacoco.home.url}">JaCoCo</a> ${qualified.bundle.version}</span>
  <a href="doc/license.html">Copyright</a> &copy; ${copyright.years} Mountainminds GmbH &amp; Co. KG and Contributors
</div>

</body>
</html>
