<?xml version="1.0" encoding="UTF-8"?>
<!--
   Copyright (c) 2009, 2025 Mountainminds GmbH & Co. KG and Contributors
   This program and the accompanying materials are made available under
   the terms of the Eclipse Public License 2.0 which is available at
   http://www.eclipse.org/legal/epl-2.0

   SPDX-License-Identifier: EPL-2.0

   Contributors:
      <PERSON><PERSON><PERSON> - initial API and implementation
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.jacoco</groupId>
    <artifactId>org.jacoco.core.test.validation</artifactId>
    <version>0.8.14-SNAPSHOT</version>
    <relativePath>../org.jacoco.core.test.validation</relativePath>
  </parent>

  <artifactId>org.jacoco.core.test.validation.java21</artifactId>

  <name>JaCoCo :: Test :: Core :: Validation Java 21</name>

  <properties>
    <bytecode.version>21</bytecode.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.core.test</artifactId>
      <version>${project.version}</version>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>com.diffplug.spotless</groupId>
        <artifactId>spotless-maven-plugin</artifactId>
        <configuration>
          <java>
            <eclipse>
              <!--
              Temporary workaround until there is an Eclipse release with Java 21 support:
              Configuration file referenced below
              is a copy of ../org.jacoco.core/.settings/org.eclipse.jdt.core.prefs
              with the following line added
              org.eclipse.jdt.core.compiler.problem.enablePreviewFeatures=enabled
              -->
              <file>../org.jacoco.core.test.validation.java21/.settings/org.eclipse.jdt.core.prefs</file>
            </eclipse>
          </java>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
