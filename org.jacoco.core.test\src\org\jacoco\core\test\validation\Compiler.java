/*******************************************************************************
 * Copyright (c) 2009, 2025 Mountainminds GmbH & Co. KG and Contributors
 * This program and the accompanying materials are made available under
 * the terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 * Contributors:
 *    Evgeny <PERSON>ikov - initial API and implementation
 *
 *******************************************************************************/
package org.jacoco.core.test.validation;

/**
 * Provides ability to detect compiler based on difference in generated bytecode
 * for switch by enum.
 */
enum Compiler {

	DETECT;

	/**
	 * @return <code>true</code> if this file was compiled by javac
	 */
	boolean isJDK() {
		switch (DETECT) {
		default:
			try {
				Compiler.class.getDeclaredField("$SWITCH_TABLE$"
						+ Compiler.class.getName().replace('.', '$'));
				return false;
			} catch (NoSuchFieldException e) {
				return true;
			}
		}
	}

}
