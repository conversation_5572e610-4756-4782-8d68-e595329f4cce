---
name: 'Bug report'
about: 'Report a bug in JaCoCo'
title:
labels: 'type: bug :bug:'
assignes:

---

THIS IS A BUG TRACKER ONLY. FOR QUESTIONS PLEASE CHECK FAQ OR USE FORUM:

http://www.jacoco.org/jacoco/trunk/doc/faq.html

https://groups.google.com/forum/?fromgroups=#!forum/jacoco

Please understand that
ISSUES WITHOUT FOLLOWING INFORMATION WILL BE CLOSED WITHOUT COMMENTS!
Thank you for filing a useful bug report!

### Steps to reproduce

* JaCoCo version:  (from right bottom corner of JaCoCo report)
* Operating system:
* Tool integration: Maven / Ant / CLI / API (for others please report to respective project)
* Complete executable reproducer: (e.g. GitHub Repo)
* Steps: (what exactly are you doing with the above reproducer?)

### Expected behaviour

### Actual behaviour

(In case of exceptions provide FULL STACKTRACE)
