/*******************************************************************************
 * Copyright (c) 2009, 2025 Mountainminds GmbH & Co. KG and Contributors
 * This program and the accompanying materials are made available under
 * the terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 * Contributors:
 *    <PERSON><PERSON> - implementation of MergeMojo
 *
 *******************************************************************************/
 import java.io.*;
 import org.codehaus.plexus.util.*;

 String buildLog = FileUtils.fileRead( new File( basedir, "build.log" ) );
 if ( buildLog.indexOf( "Loading execution data file" ) < 0 ) {
     throw new RuntimeException( "Could not load execution data file" );
 }
 if ( buildLog.indexOf( "Writing merged execution data to" ) < 0 ) {
     throw new RuntimeException( "Could not write merged execution data" );
 }
