<?xml version="1.0" encoding="UTF-8"?>
<!--
   Copyright (c) 2009, 2025 Mountainminds GmbH & Co. KG and Contributors
   This program and the accompanying materials are made available under
   the terms of the Eclipse Public License 2.0 which is available at
   http://www.eclipse.org/legal/epl-2.0

   SPDX-License-Identifier: EPL-2.0

   Contributors:
      <PERSON><PERSON><PERSON> - initial API and implementation
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.jacoco</groupId>
    <artifactId>org.jacoco.build</artifactId>
    <version>0.8.14-SNAPSHOT</version>
    <relativePath>../org.jacoco.build</relativePath>
  </parent>

  <artifactId>org.jacoco.tests</artifactId>
  <packaging>pom</packaging>

  <name>JaCoCo :: Tests</name>

  <modules>
    <module>../org.jacoco.core.test</module>
    <module>../org.jacoco.core.test.validation</module>
    <module>../org.jacoco.report.test</module>
    <module>../org.jacoco.agent.rt.test</module>
    <module>../org.jacoco.agent.test</module>
    <module>../org.jacoco.ant.test</module>
    <module>../org.jacoco.cli.test</module>
    <module>../org.jacoco.examples.test</module>
    <module>../jacoco-maven-plugin.test</module>
  </modules>

  <properties>
    <maven.deploy.skip>true</maven.deploy.skip>
    <maven.javadoc.skip>true</maven.javadoc.skip>
    <sonar.skip>true</sonar.skip>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>org.jacoco.core.test</artifactId>
        <version>${project.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <sourceDirectory>src</sourceDirectory>

    <plugins>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>${project.version}</version>
        <configuration>
          <exclClassLoaders>sun.reflect.DelegatingClassLoader:org.jacoco.core.test.TargetLoader:org.jacoco.core.test.InstrumentingLoader</exclClassLoaders>
          <sessionId>${project.artifactId}</sessionId>
          <includes>
            <include>${jacoco.includes}</include>
          </includes>
          <excludes>
            <exclude>${jacoco.excludes}</exclude>
          </excludes>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
