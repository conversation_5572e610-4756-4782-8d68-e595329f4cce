<?xml version="1.0"?>

<!--
   Copyright (c) 2009, 2025 Mountainminds GmbH & Co. KG and Contributors
   This program and the accompanying materials are made available under
   the terms of the Eclipse Public License 2.0 which is available at
   http://www.eclipse.org/legal/epl-2.0

   SPDX-License-Identifier: EPL-2.0

   Contributors:
      <PERSON> - initial API and implementation
-->

<antlib>
   <taskdef name="coverage" classname="org.jacoco.ant.CoverageTask"/>
   <taskdef name="agent" classname="org.jacoco.ant.AgentTask"/>
   <taskdef name="report" classname="org.jacoco.ant.ReportTask"/>
   <taskdef name="merge" classname="org.jacoco.ant.MergeTask"/>
   <taskdef name="dump" classname="org.jacoco.ant.DumpTask"/>
   <taskdef name="instrument" classname="org.jacoco.ant.InstrumentTask"/>
</antlib>
